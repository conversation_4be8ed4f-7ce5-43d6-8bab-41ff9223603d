# group/network/socket_manager.py
import socketio
import requests
from PyQt5.QtCore import QThread, pyqtSignal

class SocketIOManager(QThread):
    new_broadcast_signal = pyqtSignal(str)
    new_file_signal = pyqtSignal(dict)
    timer_signal = pyqtSignal(dict)
    topic_signal = pyqtSignal(str)
    message_from_student_signal = pyqtSignal(str)
    # --- WebRTC信号 ---
    webrtc_offer_signal = pyqtSignal(dict)
    webrtc_ice_candidate_signal = pyqtSignal(dict)
    # --- 触控回传信号 ---
    touch_control_command_signal = pyqtSignal(dict)
    touch_control_event_signal = pyqtSignal(dict)
    # --- 关机信号 ---
    shutdown_command_signal = pyqtSignal()
    teacher_drawing_signal = pyqtSignal(dict)
    collaboration_started_signal = pyqtSignal()
    collaboration_stopped_signal = pyqtSignal()
    course_joined_signal = pyqtSignal(str)
    # --- 学生端交互信号 ---
    student_joined_signal = pyqtSignal(dict)  # 学生加入小组
    student_left_signal = pyqtSignal(dict)    # 学生离开小组
    screen_share_request_signal = pyqtSignal(dict)  # 学生投屏请求
    screen_share_stopped_signal = pyqtSignal(dict)  # 学生停止投屏
    whiteboard_user_joined_signal = pyqtSignal(dict)  # 学生加入白板
    whiteboard_operation_signal = pyqtSignal(dict)    # 白板操作
    file_shared_signal = pyqtSignal(dict)     # 文件分享
    artwork_shared_signal = pyqtSignal(dict) # 作品分享

    def __init__(self, server_url, ip_address, hostname):
        super().__init__()
        
        # 创建一个禁用SSL验证的requests会话
        http_session = requests.Session()
        http_session.verify = False
        
        # 将配置好的会话传递给Socket.IO客户端
        self.sio = socketio.Client(http_session=http_session)
        
        self.server_url = server_url
        self.ip_address = ip_address
        self.hostname = hostname
        self.group_id = f"group_{hostname.replace(' ', '_')}_{ip_address.replace('.', '_')}"
        self.course_id = None
        self.setup_handlers()

    def setup_handlers(self):
        @self.sio.event
        def connect():
            print("已连接到 SocketIO 服务器")
            self.sio.emit('group_client_online', {'ip': self.ip_address, 'hostname': self.hostname})
            self.sio.emit('join_group_room', {'group_id': self.group_id})
            print(f"已加入专属房间: {self.group_id}")

            # 如果之前已经加入了课程，重新加入
            if self.course_id:
                self.sio.emit('join_course', {'course_id': self.course_id})
                print(f"重新加入课程: {self.course_id}")
                self.course_joined_signal.emit(self.course_id)

        @self.sio.event
        def disconnect():
            print("已从 SocketIO 服务器断开")

        @self.sio.on('join_course_command')
        def on_join_course_command(data):
            """接收服务器指令，加入指定课程房间"""
            course_id = data.get('course_id')
            if course_id:
                # 如果已经在课程中，先离开
                if self.course_id and self.course_id != course_id:
                    self.sio.emit('leave_course', {'course_id': self.course_id})
                    print(f"已离开课程: {self.course_id}")

                self.course_id = course_id
                self.sio.emit('join_course', {'course_id': self.course_id})
                self.course_joined_signal.emit(course_id)
                print(f"已根据指令加入课程: {self.course_id}")

        @self.sio.on('broadcast_stream')
        def on_broadcast_stream(data):
            stream_name = data.get('stream_name')
            if stream_name:
                print(f"收到广播指令: 播放 {stream_name}")
                self.new_broadcast_signal.emit(stream_name)

        @self.sio.on('new_file_available')
        def on_new_file(data):
            print(f"收到新文件通知: {data}")
            self.new_file_signal.emit(data)

        @self.sio.on('start_timer')
        def on_start_timer(data):
            """处理开始计时器事件"""
            duration = data.get('duration')
            if duration is not None:
                print(f"收到计时器指令，时长: {duration}秒")
                self.timer_signal.emit({'action': 'start', 'duration': duration})

        @self.sio.on('stop_timer')
        def on_stop_timer(data):
            """处理停止计时器事件"""
            print("收到停止计时器指令")
            self.timer_signal.emit({'action': 'stop'})
            
        @self.sio.on('new_group_topic')
        def on_new_group_topic(data):
            """处理新的小组主题事件"""
            topic = data.get('topic')
            if topic:
                print(f"收到新主题: {topic}")
                self.topic_signal.emit(topic)

        @self.sio.on('message_from_student')
        def on_message_from_student(data):
            message = data.get('message')
            if message:
                self.message_from_student_signal.emit(message)
        
        @self.sio.on('webrtc_offer')
        def on_webrtc_offer(data):
            print(f"收到来自 {data.get('student_sid')} 的 WebRTC offer")
            self.webrtc_offer_signal.emit(data)

        @self.sio.on('webrtc_ice_candidate')
        def on_webrtc_ice_candidate(data):
            self.webrtc_ice_candidate_signal.emit(data)
        
        # 触控回传事件处理
        @self.sio.on('touch_control_command')
        def on_touch_control_command(data):
            print(f"收到触控控制命令: {data.get('action')}")
            self.touch_control_command_signal.emit(data)
        
        @self.sio.on('touch_control_event')
        def on_touch_control_event(data):
            self.touch_control_event_signal.emit(data)

        @self.sio.on('teacher_drawing_event')
        def on_teacher_drawing_event(data):
            self.teacher_drawing_signal.emit(data)

        @self.sio.on('collaboration_started')
        def on_collaboration_started(data):
            self.collaboration_started_signal.emit()

        @self.sio.on('collaboration_stopped')
        def on_collaboration_stopped(data):
            self.collaboration_stopped_signal.emit()

        # --- 学生端交互事件处理器 ---
        @self.sio.on('student_joined')
        def on_student_joined(data):
            """学生加入小组"""
            self.student_joined_signal.emit(data)
            print(f"学生 {data.get('student_name')} 加入小组")

        @self.sio.on('student_left')
        def on_student_left(data):
            """学生离开小组"""
            self.student_left_signal.emit(data)
            print(f"学生 {data.get('student_id')} 离开小组")

        @self.sio.on('screen_share_request')
        def on_screen_share_request(data):
            """学生投屏请求"""
            self.screen_share_request_signal.emit(data)
            print(f"收到学生 {data.get('student_name')} 的投屏请求")

        @self.sio.on('screen_share_stopped')
        def on_screen_share_stopped(data):
            """学生停止投屏"""
            self.screen_share_stopped_signal.emit(data)
            print(f"学生 {data.get('student_id')} 停止投屏")

        @self.sio.on('whiteboard_user_joined')
        def on_whiteboard_user_joined(data):
            """学生加入协同白板"""
            self.whiteboard_user_joined_signal.emit(data)
            print(f"学生 {data.get('user_name')} 加入协同白板")

        @self.sio.on('whiteboard_operation')
        def on_whiteboard_operation(data):
            """协同白板操作"""
            self.whiteboard_operation_signal.emit(data)

        @self.sio.on('file_shared')
        def on_file_shared(data):
            """学生分享文件"""
            self.file_shared_signal.emit(data)
            print(f"学生 {data.get('student_name')} 分享了文件")

        @self.sio.on('artwork_shared')
        def on_artwork_shared(data):
            """学生分享作品"""
            self.artwork_shared_signal.emit(data)
            print(f"学生 {data.get('student_name')} 分享了作品")

    def get_mac_address(self):
        """获取本机MAC地址"""
        try:
            import uuid
            mac = uuid.getnode()
            return ':'.join(f'{(mac >> i) & 0xff:02x}' for i in range(0, 8*6, 8))[::-1]
        except:
            return "00:00:00:00:00:00"

    def run(self):
        try:
            # SSL验证已在http_session中处理，此处无需额外参数
            self.sio.connect(self.server_url)
            self.sio.wait()
        except socketio.exceptions.ConnectionError as e:
            print(f"无法连接到 SocketIO 服务器: {e}")

    def stop(self):
        if self.sio.connected:
            self.sio.disconnect()

    def send_event(self, event_name, data):
        if self.sio.connected:
            # 自动为事件数据添加 course_id
            if self.course_id:
                data['course_id'] = self.course_id
            self.sio.emit(event_name, data)
    
    def is_connected(self):
        """检查Socket.IO连接状态"""
        return self.sio.connected