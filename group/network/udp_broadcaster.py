# group/network/udp_broadcaster.py
import socket
import json
import time
import uuid
from PyQt5.QtCore import QThread

class UdpBroadcaster(QThread):
    def __init__(self, broadcast_port=9999):
        super().__init__()
        self.broadcast_port = broadcast_port
        self.running = True
        self.hostname = socket.gethostname()
        try:
            self.ip_address = socket.gethostbyname(self.hostname)
        except socket.gaierror:
            self.ip_address = "127.0.0.1"
        self.mac_address = ':'.join(f'{((uuid.getnode() >> i) & 0xff):02x}' for i in reversed(range(0, 8*6, 8)))
        self.is_sharing = False

    def run(self):
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        while self.running:
            message = {
                "type": "discovery",
                "hostname": self.hostname,
                "ip": self.ip_address,
                "mac": self.mac_address,
                "status": "online",
                "is_sharing": self.is_sharing,
                "stream_name": f"group_{self.ip_address.replace('.', '_')}" if self.is_sharing else ""
            }
            encoded_message = json.dumps(message).encode('utf-8')
            try:
                sock.sendto(encoded_message, ('<broadcast>', self.broadcast_port))
                time.sleep(5)
            except Exception as e:
                print(f"广播时发生错误: {e}")
                break
        sock.close()

    def set_sharing_status(self, is_sharing):
        self.is_sharing = is_sharing

    def stop(self):
        self.running = False
        self.wait()
