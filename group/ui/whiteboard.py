# group/ui/whiteboard.py
import sys
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
import qtawesome as fa  


class Whiteboard(QWidget):
    drawing_event = pyqtSignal(dict)
    _collaboration_status_changed_signal = pyqtSignal(bool)

    def __init__(self, sio_manager=None, course_id=None):
        super().__init__()
        self.sio_manager = sio_manager
        self.course_id = course_id
        self.collaboration_enabled = False
        self.drawing = False
        self.last_point = QPoint()
        self.pen_color = QColor(Qt.black)
        self.pen_width = 3

        # 远程绘图状态（用于处理教师端绘图）
        self.remote_drawing = False
        self.remote_last_point = QPoint()
        self.remote_start_point = QPoint()
        self.remote_end_point = QPoint()
        self.remote_pen_color = QColor(Qt.black)
        self.remote_pen_width = 3
        self.remote_current_shape = "freehand"
        self.remote_points = []
        # 获取屏幕尺寸（高DPI适配）
        screen = QApplication.primaryScreen()
        self.screen_rect = screen.availableGeometry()
        self.screen_width = self.screen_rect.width()
        self.screen_height = self.screen_rect.height()
        # 初始化变量
        self.shapes = []  # 存储绘制的形状
        self.drawing = False  # 绘图状态
        self.pen_color = QColor(255, 0, 0)  # 默认画笔颜色为红色
        self.current_shape = "freehand"  # 当前绘制的形状
        self.fine = 5  # 线条粗细
        self.start_point = QPoint()  # 起始点
        self.end_point = QPoint()  # 结束点
        self.last_point = QPoint()  # 初始化 last_point
        self.isMenuExpanded = True  # 菜单状态，初始为展开状态

        self.eraser_points = []  # 初始化擦除路径
        self.eraser_size = 20  # 橡皮擦大小

        self.pixmap = None # Defer initialization to resizeEvent

        self._collaboration_status_changed_signal.connect(self._update_ui_for_collaboration_status)
        
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint |Qt.Tool | self.windowFlags())
        self.setAttribute(Qt.WA_TranslucentBackground)
        # self.setGeometry((self.screen_width - 800) // 2, self.screen_height - 140, 550, 200)
     
        # 主绘图层
        self.widget_main = QWidget(self)
        self.widget_main.setGeometry(0, 0, self.screen_width, self.screen_height)
        self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")
  
        # 底部标注菜单
        self.Draw_Menu = QWidget(self)
        self.Draw_Menu.setGeometry(
            (self.screen_width - 800) // 2, self.screen_height - 140, 550, 200)
        self.horizontal_layout = QHBoxLayout(self.Draw_Menu)

        # 菜单伸缩按钮
        self.menu_pushButton = QPushButton(fa.icon('fa5s.bars', color='white'), "", self.Draw_Menu)
        self.menu_pushButton.setFixedSize(50, 50)
        self.menu_pushButton.setIconSize(QSize(35, 35))
        self.menu_pushButton.clicked.connect(self.toggleMenu)
        self.horizontal_layout.addWidget(self.menu_pushButton)

        # 画笔按钮
        self.plotting_pushButton = QPushButton(fa.icon('fa5s.paint-brush', color='white'), "", self.Draw_Menu)
        self.plotting_pushButton.setFixedSize(50, 50)
        self.plotting_pushButton.setIconSize(QSize(35, 35))
        self.plotting_pushButton.clicked.connect(lambda: self.set_shape("freehand"))
        self.horizontal_layout.addWidget(self.plotting_pushButton)

        # 设置按钮
        self.settings_pushButton = QPushButton(fa.icon('fa5s.cog', color='white'), "", self.Draw_Menu)
        self.settings_pushButton.setFixedSize(50, 50)
        self.settings_pushButton.setIconSize(QSize(35, 35))
        self.settings_pushButton.clicked.connect(self.show_settings_menu)
        self.horizontal_layout.addWidget(self.settings_pushButton)

        # 设置菜单
        self.settings_menu = QMenu(self.Draw_Menu)
        self.settings_menu.addAction("选择颜色", self.openColorDialog)

        self.width_menu = QMenu("选择笔粗细", self.settings_menu)
        for i in range(1, 11):
            self.width_menu.addAction(f"笔宽 {i}", lambda w=i: self.set_fine(w))
        self.settings_menu.addMenu(self.width_menu)

        self.shape_menu = QMenu("形状选择", self.settings_menu)
        self.shape_menu.addAction("手绘", lambda: self.set_shape("freehand"))
        self.shape_menu.addAction("直线", lambda: self.set_shape("line"))
        self.shape_menu.addAction("矩形", lambda: self.set_shape("rectangle"))
        self.shape_menu.addAction("圆形", lambda: self.set_shape("circle"))
        self.shape_menu.addAction("三角形", lambda: self.set_shape("triangle"))
        self.settings_menu.addMenu(self.shape_menu)

        # 擦除按钮
        self.eraser_pushButton = QPushButton(fa.icon('fa5s.eraser', color='white'), "", self.Draw_Menu)
        self.eraser_pushButton.setFixedSize(50, 50)
        self.eraser_pushButton.setIconSize(QSize(35, 35))
        self.eraser_pushButton.clicked.connect(
            lambda: self.set_shape("eraser"))
        self.horizontal_layout.addWidget(self.eraser_pushButton)

        # 撤销按钮
        self.revoke_pushButton = QPushButton(fa.icon('fa5s.undo', color='white'), "", self.Draw_Menu)
        self.revoke_pushButton.setFixedSize(50, 50)
        self.revoke_pushButton.setIconSize(QSize(35, 35))
        self.revoke_pushButton.clicked.connect(self.undoLastDrawing)
        self.horizontal_layout.addWidget(self.revoke_pushButton)

        # 清屏按钮
        self.Clear_pushButton = QPushButton(fa.icon('fa5s.trash-alt', color='white'), "", self.Draw_Menu)
        self.Clear_pushButton.setFixedSize(50, 50)
        self.Clear_pushButton.setIconSize(QSize(35, 35))
        self.Clear_pushButton.clicked.connect(self.erasureClick)
        self.horizontal_layout.addWidget(self.Clear_pushButton)

        # 协作状态指示灯 (只读)
        self.collaboration_button = QPushButton(fa.icon('fa5s.users', color='white'), "", self.Draw_Menu)
        self.collaboration_button.setFixedSize(50, 50)
        self.collaboration_button.setIconSize(QSize(35, 35))
        self.collaboration_button.setEnabled(False) # 小组端不能主动控制
        self.horizontal_layout.addWidget(self.collaboration_button)
        
        # 绘制窗体关闭
        self.ppt_pushButton_close = QPushButton(fa.icon('fa5s.times', color='red'), "", self.Draw_Menu)
        self.ppt_pushButton_close.setFixedSize(50, 50)
        self.ppt_pushButton_close.setIconSize(QSize(35, 35))
        self.ppt_pushButton_close.clicked.connect(self.close)
        self.horizontal_layout.addWidget(self.ppt_pushButton_close)

        # 添加统一的样式
        self.Draw_Menu.setStyleSheet("""
            QPushButton {
                background-color: rgba(85, 85, 85, 0);
                border: 0px;
            }
            QPushButton:hover {
                background-color: rgba(100, 100, 100, 100);
                border-radius: 5px;
            }
        """)
        # —————————————————————————————————————————————————————————————————————————

    def toggleMenu(self):
        """切换菜单状态（优化穿透版）"""
        # 切换其他按钮的可见性
        for i in range(1, self.horizontal_layout.count()):
            widget = self.horizontal_layout.itemAt(i).widget()
            widget.setVisible(not widget.isVisible())

        if self.isMenuExpanded:
            # 收缩状态：关闭透明层和主图层
            self.widget_main.hide()  # 隐藏主图层
            
            self.setAttribute(Qt.WA_TransparentForMouseEvents, True)  # 设置穿透
            
            # 确保收缩按钮保持可点击
            self.menu_pushButton.setAttribute(Qt.WA_TransparentForMouseEvents, False)
            self.menu_pushButton.raise_()
        else:
            # 展开状态：恢复透明层和主图层
            self.widget_main.show()
            self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")
            self.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        
        # 保持窗口置顶
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.show()
        self.isMenuExpanded = not self.isMenuExpanded

    def show_settings_menu(self):
        """显示设置菜单"""
        pos = self.settings_pushButton.mapToGlobal(
            self.settings_pushButton.rect().topLeft())
        pos.setY(pos.y() - self.settings_menu.sizeHint().height())
        self.settings_menu.exec_(pos)

    def set_fine(self, fine):
        """设置画笔粗细"""
        self.fine = fine

    def openColorDialog(self):
        """打开颜色选择对话框"""
        color = QColorDialog.getColor(self.pen_color, self, "选择颜色")
        if color.isValid():
            self.pen_color = color

    def set_shape(self, shape):
        """设置当前绘制的形状"""
        self.current_shape = shape

    def erasureClick(self):
        """清空所有绘制内容"""
        self.shapes.clear()
        self.pixmap.fill(Qt.transparent)  # 清空 QPixmap
        self.update()
        self.sync_state_to_teacher()

    def undoLastDrawing(self):
        """撤销最后一次绘制"""
        if self.shapes:
            self.shapes.pop()
            if self.pixmap is not None:
                self.pixmap.fill(Qt.transparent)  # 清空 QPixmap
                # 重新绘制所有形状
                painter = QPainter(self.pixmap)
                for shape in self.shapes:
                    self.draw_shape(painter, shape)
                painter.end()  # 确保QPainter正确关闭
            self.update()
            self.sync_state_to_teacher()

    def draw_shape(self, painter, shape):
        """绘制单个形状"""
        pen = QPen(shape["color"], shape["fine"], Qt.SolidLine, Qt.RoundCap)
        painter.setPen(pen)

        if shape["shape"] == "line":
            painter.drawLine(shape["start"], shape["end"])
        elif shape["shape"] == "rectangle":
            # 正确创建矩形：使用左上角和右下角坐标
            x1, y1 = shape["start"].x(), shape["start"].y()
            x2, y2 = shape["end"].x(), shape["end"].y()
            rect = QRect(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))
            painter.drawRect(rect)
        elif shape["shape"] == "triangle":
            polygon = QPolygon(shape["points"])
            painter.drawPolygon(polygon)
        elif shape["shape"] == "circle":
            radius = int((shape["end"] - shape["start"]).manhattanLength() / 2)
            center = shape["start"]
            painter.drawEllipse(center, radius, radius)
        elif shape["shape"] == "freehand":
            for i in range(1, len(shape["points"])):
                painter.drawLine(shape["points"][i - 1], shape["points"][i])

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 停止任何正在进行的远程绘图
            if self.remote_drawing:
                self.remote_drawing = False

            self.drawing = True
            self.start_point = event.pos()
            if self.current_shape == "freehand":
                self.points = [event.pos()]
            elif self.current_shape == "eraser":
                self.eraser_points = [event.pos()]  # 初始化擦除路径

            event_data = {
                'type': 'mouse_press',
                'course_id': self.course_id,
                'data': {
                    'x': self.start_point.x(),
                    'y': self.start_point.y(),
                    'color': self.pen_color.name(),  # 添加颜色信息
                    'fine': self.fine,  # 添加线宽信息
                    'shape': self.current_shape  # 添加形状信息
                }
            }
            self.drawing_event.emit(event_data)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() & Qt.LeftButton and self.drawing:
            self.end_point = event.pos()
            if self.current_shape == "freehand":
                self.points.append(event.pos())
            elif self.current_shape == "eraser":
                # 在 QPixmap 上擦除
                if self.pixmap is not None:
                    painter = QPainter(self.pixmap)
                    painter.setCompositionMode(
                        QPainter.CompositionMode_Clear)  # 使用透明模式擦除
                    painter.setPen(
                        QPen(Qt.transparent, self.eraser_size, Qt.SolidLine, Qt.RoundCap))
                    painter.drawLine(self.last_point, event.pos())
                    painter.end()  # 确保QPainter正确关闭
            self.last_point = event.pos()
            self.update()  # 触发 paintEvent

            event_data = {
                'type': 'mouse_move',
                'course_id': self.course_id,
                'data': {
                    'x': self.end_point.x(),
                    'y': self.end_point.y(),
                    'color': self.pen_color.name(),  # 添加颜色信息
                    'fine': self.fine,  # 添加线宽信息
                    'shape': self.current_shape  # 添加形状信息
                }
            }
            self.drawing_event.emit(event_data)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.drawing:
            self.drawing = False
            shape = None

            if self.current_shape == "line":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "rectangle":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "triangle":
                mid_x = (self.start_point.x() + self.end_point.x()) / 2
                side_length = abs(self.end_point.x() - self.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.start_point.x(), self.end_point.y()),
                    QPoint(self.end_point.x(), self.end_point.y()),
                    QPoint(int(mid_x), self.end_point.y() - height)
                ]
                shape = {
                    "shape": self.current_shape,
                    "points": points,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "circle":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "freehand":
                shape = {
                    "shape": self.current_shape,
                    "points": self.points[:],
                    "color": self.pen_color,
                    "fine": self.fine
                }

            if shape:
                self.shapes.append(shape)
                # 将形状绘制到 QPixmap 中
                if self.pixmap is not None:
                    painter = QPainter(self.pixmap)
                    self.draw_shape(painter, shape)
                    painter.end()  # 确保QPainter正确关闭
            self.update()
            event_data = {
                'type': 'mouse_release',
                'course_id': self.course_id,
                'data': {
                    'x': self.end_point.x(),
                    'y': self.end_point.y(),
                    'color': self.pen_color.name(),  # 添加颜色信息
                    'fine': self.fine,  # 添加线宽信息
                    'shape': self.current_shape  # 添加形状信息
                }
            }
            self.drawing_event.emit(event_data)
            self.sync_state_to_teacher()

    def paintEvent(self, event):
        """绘制事件"""
        if self.pixmap is None:
            return
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.drawPixmap(0, 0, self.pixmap)

        # 绘制当前实时预览形状（小组端自己的绘图）
        if self.drawing and not self.remote_drawing:
            pen = QPen(self.pen_color, self.fine, Qt.SolidLine, Qt.RoundCap)
            painter.setPen(pen)

            if self.current_shape == "line":
                painter.drawLine(self.start_point, self.end_point)
            elif self.current_shape == "rectangle":
                # 正确创建矩形：使用左上角和右下角坐标
                x1, y1 = self.start_point.x(), self.start_point.y()
                x2, y2 = self.end_point.x(), self.end_point.y()
                rect = QRect(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))
                painter.drawRect(rect)
            elif self.current_shape == "triangle":
                mid_x = (self.start_point.x() + self.end_point.x()) / 2
                side_length = abs(self.end_point.x() - self.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.start_point.x(), self.end_point.y()),
                    QPoint(self.end_point.x(), self.end_point.y()),
                    QPoint(int(mid_x), self.end_point.y() - height)
                ]
                painter.drawPolygon(QPolygon(points))
            elif self.current_shape == "circle":
                radius = int(
                    (self.end_point - self.start_point).manhattanLength() / 2)
                center = self.start_point
                painter.drawEllipse(center, radius, radius)
            elif self.current_shape == "freehand" and hasattr(self, 'points') and len(self.points) > 1:
                for i in range(1, len(self.points)):
                    painter.drawLine(self.points[i - 1], self.points[i])

        # 绘制教师端的实时预览（如果正在进行远程绘图）
        if self.remote_drawing and self.collaboration_enabled:
            pen = QPen(self.remote_pen_color, self.remote_pen_width, Qt.SolidLine, Qt.RoundCap)
            painter.setPen(pen)

            if self.remote_current_shape == "freehand" and hasattr(self, 'remote_points') and len(self.remote_points) > 1:
                for i in range(1, len(self.remote_points)):
                    painter.drawLine(self.remote_points[i - 1], self.remote_points[i])
            elif self.remote_current_shape == "line" and hasattr(self, 'remote_start_point') and hasattr(self, 'remote_end_point'):
                painter.drawLine(self.remote_start_point, self.remote_end_point)
            elif self.remote_current_shape == "rectangle" and hasattr(self, 'remote_start_point') and hasattr(self, 'remote_end_point'):
                # 正确创建矩形：使用左上角和右下角坐标
                x1, y1 = self.remote_start_point.x(), self.remote_start_point.y()
                x2, y2 = self.remote_end_point.x(), self.remote_end_point.y()
                rect = QRect(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))
                painter.drawRect(rect)
            elif self.remote_current_shape == "circle" and hasattr(self, 'remote_start_point') and hasattr(self, 'remote_end_point'):
                radius = int((self.remote_end_point - self.remote_start_point).manhattanLength() / 2)
                center = self.remote_start_point
                painter.drawEllipse(center, radius, radius)
            elif self.remote_current_shape == "triangle" and hasattr(self, 'remote_start_point') and hasattr(self, 'remote_end_point'):
                mid_x = (self.remote_start_point.x() + self.remote_end_point.x()) / 2
                side_length = abs(self.remote_end_point.x() - self.remote_start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.remote_start_point.x(), self.remote_end_point.y()),
                    QPoint(self.remote_end_point.x(), self.remote_end_point.y()),
                    QPoint(int(mid_x), self.remote_end_point.y() - height)
                ]
                painter.drawPolygon(QPolygon(points))

    def resizeEvent(self, event):
        if self.pixmap is None or self.size() != self.pixmap.size():
            new_pixmap = QPixmap(self.size())
            new_pixmap.fill(Qt.transparent)
            if self.pixmap is not None:
                painter = QPainter(new_pixmap)
                painter.drawPixmap(QPoint(0, 0), self.pixmap)
                painter.end()  # 确保QPainter正确关闭
            self.pixmap = new_pixmap

    def handle_remote_drawing_event(self, data):
        print(f"Group Whiteboard: Received remote drawing event: {data}")
        event_type = data.get('type')
        course_id = data.get('course_id')
        drawing_data = data.get('data')

        if course_id != self.course_id:
            print(f"Group Whiteboard: Course ID mismatch. Expected {self.course_id}, got {course_id}")
            return

        # 如果未开启协作模式，不处理教师端的绘图事件
        if not self.collaboration_enabled and event_type != "mouse_release":
            print("Group Whiteboard: Collaboration not enabled, ignoring teacher drawing event")
            return

        if event_type == "mouse_press":
            self.remote_last_point = QPoint(drawing_data['x'], drawing_data['y'])
            self.remote_start_point = QPoint(drawing_data['x'], drawing_data['y'])
            self.remote_drawing = True
            # 存储远程画笔属性
            self.remote_pen_color = QColor(drawing_data.get('color', Qt.black))
            self.remote_pen_width = drawing_data.get('fine', 3)
            self.remote_current_shape = drawing_data.get('shape', "freehand")
            if self.remote_current_shape == "freehand":
                self.remote_points = [self.remote_last_point]
        elif event_type == "mouse_move" and self.remote_drawing:
            point = QPoint(drawing_data['x'], drawing_data['y'])
            self.remote_end_point = point

            if self.remote_current_shape == "freehand":
                self.remote_points.append(point)
                if self.pixmap is not None:
                    painter = QPainter(self.pixmap)
                    painter.setPen(QPen(self.remote_pen_color, self.remote_pen_width, Qt.SolidLine, Qt.RoundCap, Qt.RoundJoin))
                    painter.drawLine(self.remote_last_point, point)
                    painter.end()  # 确保QPainter正确关闭
                self.remote_last_point = point

            self.update()  # 触发paintEvent来绘制其他形状的预览
        elif event_type == "mouse_release":
            if self.remote_drawing:
                self.remote_end_point = QPoint(drawing_data['x'], drawing_data['y'])
                self.remote_drawing = False

                # 如果协作模式已启用，将教师的绘图添加到形状列表中
                if self.collaboration_enabled:
                    shape = None
                    if self.remote_current_shape == "freehand" and hasattr(self, 'remote_points') and len(self.remote_points) > 1:
                        shape = {
                            "shape": self.remote_current_shape,
                            "points": self.remote_points[:],
                            "color": self.remote_pen_color,
                            "fine": self.remote_pen_width
                        }
                    elif self.remote_current_shape == "line":
                        shape = {
                            "shape": self.remote_current_shape,
                            "start": self.remote_start_point,
                            "end": self.remote_end_point,
                            "color": self.remote_pen_color,
                            "fine": self.remote_pen_width
                        }
                    elif self.remote_current_shape == "rectangle":
                        shape = {
                            "shape": self.remote_current_shape,
                            "start": self.remote_start_point,
                            "end": self.remote_end_point,
                            "color": self.remote_pen_color,
                            "fine": self.remote_pen_width
                        }
                    elif self.remote_current_shape == "circle":
                        shape = {
                            "shape": self.remote_current_shape,
                            "start": self.remote_start_point,
                            "end": self.remote_end_point,
                            "color": self.remote_pen_color,
                            "fine": self.remote_pen_width
                        }
                    elif self.remote_current_shape == "triangle":
                        mid_x = (self.remote_start_point.x() + self.remote_end_point.x()) / 2
                        side_length = abs(self.remote_end_point.x() - self.remote_start_point.x())
                        height = int((3 ** 0.5 / 2) * side_length)
                        points = [
                            QPoint(self.remote_start_point.x(), self.remote_end_point.y()),
                            QPoint(self.remote_end_point.x(), self.remote_end_point.y()),
                            QPoint(int(mid_x), self.remote_end_point.y() - height)
                        ]
                        shape = {
                            "shape": self.remote_current_shape,
                            "points": points,
                            "color": self.remote_pen_color,
                            "fine": self.remote_pen_width
                        }

                    if shape:
                        self.shapes.append(shape)
                        # 绘制最终形状到pixmap
                        if self.pixmap is not None:
                            painter = QPainter(self.pixmap)
                            self.draw_shape(painter, shape)
                            painter.end()  # 确保QPainter正确关闭
                        self.update()


    def close(self):
        """关闭当前实例（窗口）"""
        super().close()

    def closeEvent(self, event):
        """重写关闭事件"""
        super().closeEvent(event)

    def sync_state_to_teacher(self):
        """将当前白板状态同步到教师端"""
        if not self.collaboration_enabled:
            return

        if not self.sio_manager or not self.sio_manager.is_connected():
            print("SocketIO not connected, cannot sync whiteboard state.")
            return

        # 将Qt对象转换为可序列化的字典
        serializable_shapes = []
        for shape in self.shapes:
            s_shape = shape.copy()
            # 转换颜色
            s_shape['color'] = s_shape['color'].name() # #RRGGBB
            
            # 转换点
            if 'start' in s_shape and 'end' in s_shape:
                s_shape['start'] = {'x': s_shape['start'].x(), 'y': s_shape['start'].y()}
                s_shape['end'] = {'x': s_shape['end'].x(), 'y': s_shape['end'].y()}
            
            if 'points' in s_shape:
                s_shape['points'] = [{'x': p.x(), 'y': p.y()} for p in s_shape['points']]
            
            serializable_shapes.append(s_shape)

        payload = {
            'shapes': serializable_shapes,
            'course_id': self.course_id
        }
        
        # 通过sio_manager发送事件
        self.sio_manager.send_event('group_whiteboard_sync', payload)

    def enable_collaboration(self):
        """启用协作模式（线程安全）"""
        self.collaboration_enabled = True
        self._collaboration_status_changed_signal.emit(True)
        print("Group Whiteboard: Collaboration enabled")

    def disable_collaboration(self):
        """禁用协作模式（线程安全）"""
        self.collaboration_enabled = False
        # 停止任何正在进行的远程绘图
        self.remote_drawing = False
        self._collaboration_status_changed_signal.emit(False)
        print("Group Whiteboard: Collaboration disabled")

    def _update_ui_for_collaboration_status(self, is_enabled):
        """在UI线程中更新协作状态相关的UI元素"""
        if is_enabled:
            self.collaboration_button.setStyleSheet("background-color: rgba(0, 255, 0, 100); border-radius: 5px;")
        else:
            self.collaboration_button.setStyleSheet("")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Whiteboard()
    window.showFullScreen()
    sys.exit(app.exec_())