# group/features/touch_handler.py
import json
import logging
import platform
import subprocess
import threading
import time
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtGui import QCursor
from PyQt5.QtCore import Qt

log = logging.getLogger(__name__)

class TouchHandler(QObject):
    # 信号：向Socket发送响应
    touch_response_signal = pyqtSignal(dict)
    keyboard_status_signal = pyqtSignal(dict)
    
    def __init__(self, parent=None, socket_manager=None, group_id=None):
        super().__init__()
        self.parent = parent
        self.socket_manager = socket_manager
        self.group_id = group_id
        self.virtual_keyboard_process = None
        self.touch_control_active = False
        self.whiteboard_window = None
        self.system_type = platform.system().lower()

    def set_whiteboard_window(self, whiteboard_window):
        """设置当前活动的白板窗口实例"""
        self.whiteboard_window = whiteboard_window
        # 假设设置窗口实例也意味着协作模式开始
        self.whiteboard_collaboration_active = True if whiteboard_window else False
        
    def handle_touch_control_command(self, data):
        """处理来自教师端的触控控制命令"""
        action = data.get('action')
        group_id = data.get('group_id')
        
        if group_id != self.group_id:
            return  # 不是给自己的命令
            
        try:
            if action == 'show_virtual_keyboard':
                success = self._show_virtual_keyboard()
                self._send_keyboard_status('shown' if success else 'error')
                
            elif action == 'hide_virtual_keyboard':
                success = self._hide_virtual_keyboard()
                self._send_keyboard_status('hidden' if success else 'error')
                
            elif action == 'start_touch_control':
                success = self._start_touch_control()
                self._send_touch_response('touch_control_started', success)
                
            elif action == 'stop_touch_control':
                success = self._stop_touch_control()
                self._send_touch_response('touch_control_stopped', success)
                
            else:
                log.warning(f"未知的触控控制命令: {action}")
                
        except Exception as e:
            log.error(f"处理触控控制命令失败: {e}")
            self._send_touch_response('error', False, str(e))
    
    def handle_touch_control_event(self, data):
        """处理来自教师端的触控事件"""
        if not self.touch_control_active:
            return
            
        action = data.get('action')
        group_id = data.get('group_id')
        
        if group_id != self.group_id:
            return

        # 如果白板协作模式开启，则将事件转发给白板
        if self.whiteboard_collaboration_active and self.whiteboard_window:
            if action == 'touch_event':
                self.whiteboard_window.handle_remote_drawing_event(data)
            return
            
        try:
            if action == 'touch_event':
                self._process_touch_event(data)
            elif action == 'text_input':
                self._process_text_input(data)
            else:
                log.warning(f"未知的触控事件: {action}")
                
        except Exception as e:
            log.error(f"处理触控事件失败: {e}")
    
    def _show_virtual_keyboard(self):
        """显示虚拟键盘"""
        try:
            env = os.environ.copy()
            if self.system_type == 'linux':
                # 检测是否为UOS/Deepin系统
                if self._is_uos_system():
                    return self._toggle_uos_keyboard()
                else:
                    # 通用Linux系统使用onboard虚拟键盘
                    if not self._is_onboard_installed():
                        log.error("onboard虚拟键盘未安装")
                        return False
                        
                    if self.virtual_keyboard_process is None:
                        self.virtual_keyboard_process = subprocess.Popen([
                            'onboard'
                        ], env=env)
                        log.info("通用Linux虚拟键盘已启动")
                    return True
                
            elif self.system_type == 'windows':
                # Windows系统调用系统虚拟键盘
                subprocess.run(['osk'], check=False, env=env)
                log.info("Windows虚拟键盘已调起")
                return True
                
            else:
                log.warning(f"不支持的系统类型: {self.system_type}")
                return False
                
        except Exception as e:
            log.error(f"显示虚拟键盘失败: {e}")
            return False
    
    def _hide_virtual_keyboard(self):
        """隐藏虚拟键盘"""
        try:
            if self.system_type == 'linux':
                if self._is_uos_system():
                    return self._toggle_uos_keyboard()
                else:
                    if self.virtual_keyboard_process:
                        self.virtual_keyboard_process.terminate()
                        self.virtual_keyboard_process = None
                        log.info("通用Linux虚拟键盘已关闭")
                    return True
            else:
                # Windows处理
                if self.virtual_keyboard_process:
                    self.virtual_keyboard_process.terminate()
                    self.virtual_keyboard_process = None
                return True
            
        except Exception as e:
            log.error(f"隐藏虚拟键盘失败: {e}")
            return False

    def _toggle_uos_keyboard(self):
        """通过DBus发送命令来切换UOS/Deepin的虚拟键盘显示状态。"""
        try:
            cmd = [
                'dbus-send', '--session',
                '--dest=org.onboard.Onboard',
                '--type=method_call',
                '/org/onboard/Onboard/Keyboard',
                'org.onboard.Onboard.Keyboard.ToggleVisible'
            ]
            env = os.environ.copy()
            
            log.info(f"正在执行UOS键盘命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                log.info("✓ DBus命令已成功发送")
                return True
            else:
                log.error(f"✗ DBus命令执行失败: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"✗ 执行UOS键盘DBus命令时发生异常: {e}")
            return False

    def _is_uos_system(self):
        """检测是否为UOS/Deepin系统"""
        try:
            with open('/etc/os-release', 'r') as f:
                content = f.read().lower()
                return 'uos' in content or 'deepin' in content
        except:
            return False

    def _is_onboard_installed(self):
        """检查onboard是否已安装"""
        try:
            env = os.environ.copy()
            subprocess.run(['which', 'onboard'], check=True, capture_output=True, env=env)
            return True
        except subprocess.CalledProcessError:
            return False

    def _start_touch_control(self):
        """启动触控控制模式"""
        try:
            self.touch_control_active = True
            log.info("触控控制模式已启动")
            return True
            
        except Exception as e:
            log.error(f"启动触控控制失败: {e}")
            return False
    
    def _stop_touch_control(self):
        """停止触控控制模式"""
        try:
            self.touch_control_active = False
            # 同时隐藏虚拟键盘
            self._hide_virtual_keyboard()
            log.info("触控控制模式已停止")
            return True
            
        except Exception as e:
            log.error(f"停止触控控制失败: {e}")
            return False
    
    def _process_touch_event(self, data):
        """处理触控事件"""
        event_type = data.get('event_type')
        coordinates = data.get('coordinates', {})
        pressure = data.get('pressure', 1.0)
        
        x = coordinates.get('x', 0)
        y = coordinates.get('y', 0)
        
        try:
            if self.system_type == 'linux':
                self._simulate_linux_touch(event_type, x, y, pressure)
            elif self.system_type == 'windows':
                self._simulate_windows_touch(event_type, x, y, pressure)
            else:
                log.warning(f"不支持的系统类型: {self.system_type}")
                
        except Exception as e:
            log.error(f"处理触控事件失败: {e}")
    
    def _process_text_input(self, data):
        """处理文本输入事件"""
        text = data.get('text', '')
        
        try:
            env = os.environ.copy()
            if self.system_type == 'linux':
                # 使用xdotool发送文本
                subprocess.run(['xdotool', 'type', text], check=False, env=env)
                
            elif self.system_type == 'windows':
                # 使用PyQt模拟文本输入
                app = QApplication.instance()
                if app:
                    for char in text:
                        # 这里可以优化为更直接的键盘输入模拟
                        pass
                        
            log.info(f"已模拟文本输入: {text}")
            
        except Exception as e:
            log.error(f"处理文本输入失败: {e}")
    
    def _simulate_linux_touch(self, event_type, x, y, pressure):
        """Linux系统触控模拟"""
        try:
            env = os.environ.copy()
            if event_type == 'touch_down':
                # 模拟鼠标按下
                subprocess.run(['xdotool', 'mousemove', str(x), str(y)], check=False, env=env)
                subprocess.run(['xdotool', 'mousedown', '1'], check=False, env=env)
                
            elif event_type == 'touch_move':
                # 模拟鼠标拖拽
                subprocess.run(['xdotool', 'mousemove', str(x), str(y)], check=False, env=env)
                
            elif event_type == 'touch_up':
                # 模拟鼠标释放
                subprocess.run(['xdotool', 'mouseup', '1'], check=False, env=env)
                
        except Exception as e:
            log.error(f"Linux触控模拟失败: {e}")
    
    def _simulate_windows_touch(self, event_type, x, y, pressure):
        """Windows系统触控模拟"""
        try:
            import ctypes
            from ctypes import wintypes
            
            # Windows API调用实现触控模拟
            user32 = ctypes.windll.user32
            
            if event_type == 'touch_down':
                user32.SetCursorPos(x, y)
                user32.mouse_event(0x0002, x, y, 0, 0)  # MOUSEEVENTF_LEFTDOWN
                
            elif event_type == 'touch_move':
                user32.SetCursorPos(x, y)
                
            elif event_type == 'touch_up':
                user32.mouse_event(0x0004, x, y, 0, 0)  # MOUSEEVENTF_LEFTUP
                
        except Exception as e:
            log.error(f"Windows触控模拟失败: {e}")
    
    def _send_touch_response(self, response_type, success, error_msg=None):
        """发送触控控制响应"""
        response_data = {
            'group_id': self.group_id,
            'response_type': response_type,
            'success': success,
            'timestamp': int(time.time() * 1000)
        }
        
        if error_msg:
            response_data['error'] = error_msg
            
        if self.socket_manager and self.socket_manager.is_connected():
            self.socket_manager.sio.emit('touch_control_response', response_data)
    
    def _send_keyboard_status(self, status):
        """发送虚拟键盘状态"""
        status_data = {
            'group_id': self.group_id,
            'status': status,
            'timestamp': int(time.time() * 1000)
        }
        
        if self.socket_manager and self.socket_manager.is_connected():
            self.socket_manager.sio.emit('virtual_keyboard_status', status_data)
    
    def cleanup(self):
        """清理资源"""
        self._hide_virtual_keyboard()
        self._stop_touch_control()