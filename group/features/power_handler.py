# group/features/power_handler.py
import subprocess
import platform
import logging

log = logging.getLogger(__name__)

class PowerHandler:
    def __init__(self):
        self.system_type = platform.system().lower()

    def shutdown(self):
        """执行系统关机命令"""
        try:
            if self.system_type == "linux":
                # 对于Linux，使用 sudo shutdown
                # 注意：需要配置sudoers以允许无密码执行此命令
                command = ['sudo', 'shutdown', '-h', 'now']
                log.info(f"正在执行关机命令: {' '.join(command)}")
                subprocess.run(command, check=True)
                return True
            elif self.system_type == "windows":
                command = ['shutdown', '/s', '/t', '0']
                log.info(f"正在执行关机命令: {' '.join(command)}")
                subprocess.run(command, check=True)
                return True
            else:
                log.warning(f"不支持的操作系统: {self.system_type}，无法执行关机。")
                return False
        except FileNotFoundError:
            log.error("关机命令未找到。请确保 'shutdown' (或 'sudo') 在系统的PATH中。")
            return False
        except subprocess.CalledProcessError as e:
            log.error(f"执行关机命令失败: {e}")
            return False
        except Exception as e:
            log.error(f"关机时发生未知错误: {e}")
            return False
