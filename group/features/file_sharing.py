# group/features/file_sharing.py
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidget, 
                             QListWidgetItem, QPushButton, QFileDialog, QLabel,
                             QTextEdit, QMessageBox, QSplitter, QFrame, QComboBox,
                             QProgressBar, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QFont
import os
import shutil
import mimetypes
from pathlib import Path
import subprocess
import json

class FileItem(QListWidgetItem):
    """文件项"""
    def __init__(self, file_path, shared_by="", parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.shared_by = shared_by
        self.file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        
        filename = os.path.basename(file_path)
        file_ext = os.path.splitext(filename)[1].lower()
        
        # 设置显示文本
        display_text = filename
        if shared_by:
            display_text += f" (分享者: {shared_by})"
        
        self.setText(display_text)
        self.setToolTip(f"文件: {filename}\n路径: {file_path}\n大小: {self.format_file_size()}")
        
        # 设置图标（根据文件类型）
        self.set_file_icon(file_ext)
    
    def set_file_icon(self, file_ext):
        """根据文件扩展名设置图标"""
        # 这里可以根据文件类型设置不同的图标
        # 简化处理，可以后续扩展
        pass
    
    def format_file_size(self):
        """格式化文件大小"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"

class FilePreviewWidget(QWidget):
    """文件预览组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_file = None
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 文件信息标签
        self.info_label = QLabel("请选择文件进行预览")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setFont(QFont("Arial", 12))
        layout.addWidget(self.info_label)
        
        # 预览区域
        self.preview_area = QFrame()
        self.preview_area.setFrameStyle(QFrame.Box)
        self.preview_area.setMinimumHeight(300)
        layout.addWidget(self.preview_area, 1)
        
        # 预览布局
        self.preview_layout = QVBoxLayout(self.preview_area)
        
        # 默认显示
        self.default_label = QLabel("选择文件查看预览")
        self.default_label.setAlignment(Qt.AlignCenter)
        self.default_label.setStyleSheet("color: #666; font-size: 14px;")
        self.preview_layout.addWidget(self.default_label)
    
    def preview_file(self, file_path):
        """预览文件"""
        self.current_file = file_path
        
        if not os.path.exists(file_path):
            self.show_error("文件不存在")
            return
        
        # 清除现有预览
        self.clear_preview()
        
        filename = os.path.basename(file_path)
        file_ext = os.path.splitext(filename)[1].lower()
        file_size = os.path.getsize(file_path)
        
        # 更新文件信息
        self.info_label.setText(f"文件: {filename} ({self.format_size(file_size)})")
        
        # 根据文件类型进行预览
        if file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
            self.preview_image(file_path)
        elif file_ext in ['.txt', '.md', '.py', '.js', '.html', '.css']:
            self.preview_text(file_path)
        elif file_ext == '.pdf':
            self.preview_pdf(file_path)
        elif file_ext in ['.doc', '.docx']:
            self.preview_document(file_path)
        else:
            self.show_unsupported(file_ext)
    
    def clear_preview(self):
        """清除预览内容"""
        while self.preview_layout.count():
            child = self.preview_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def preview_image(self, file_path):
        """预览图片"""
        try:
            pixmap = QPixmap(file_path)
            if not pixmap.isNull():
                label = QLabel()
                label.setAlignment(Qt.AlignCenter)
                label.setScaledContents(True)
                
                # 缩放图片以适应预览区域
                scaled_pixmap = pixmap.scaled(
                    400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation
                )
                label.setPixmap(scaled_pixmap)
                self.preview_layout.addWidget(label)
            else:
                self.show_error("无法加载图片")
        except Exception as e:
            self.show_error(f"图片预览失败: {e}")
    
    def preview_text(self, file_path):
        """预览文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(5000)  # 只读取前5000个字符
            
            text_edit = QTextEdit()
            text_edit.setPlainText(content)
            text_edit.setReadOnly(True)
            self.preview_layout.addWidget(text_edit)
            
            if len(content) >= 5000:
                warning_label = QLabel("注意: 只显示文件的前5000个字符")
                warning_label.setStyleSheet("color: orange;")
                self.preview_layout.addWidget(warning_label)
                
        except Exception as e:
            self.show_error(f"文本预览失败: {e}")
    
    def preview_pdf(self, file_path):
        """预览PDF文件"""
        info_label = QLabel("PDF文件预览")
        info_label.setAlignment(Qt.AlignCenter)
        self.preview_layout.addWidget(info_label)
        
        open_btn = QPushButton("打开PDF文件")
        open_btn.clicked.connect(lambda: self.open_with_system(file_path))
        self.preview_layout.addWidget(open_btn)
    
    def preview_document(self, file_path):
        """预览文档文件"""
        info_label = QLabel("文档文件预览")
        info_label.setAlignment(Qt.AlignCenter)
        self.preview_layout.addWidget(info_label)
        
        open_btn = QPushButton("打开文档文件")
        open_btn.clicked.connect(lambda: self.open_with_system(file_path))
        self.preview_layout.addWidget(open_btn)
    
    def show_unsupported(self, file_ext):
        """显示不支持的文件类型"""
        label = QLabel(f"不支持预览 {file_ext} 文件")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: #666;")
        self.preview_layout.addWidget(label)
        
        if self.current_file:
            open_btn = QPushButton("用系统默认程序打开")
            open_btn.clicked.connect(lambda: self.open_with_system(self.current_file))
            self.preview_layout.addWidget(open_btn)
    
    def show_error(self, message):
        """显示错误信息"""
        label = QLabel(f"错误: {message}")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: red;")
        self.preview_layout.addWidget(label)
    
    def open_with_system(self, file_path):
        """用系统默认程序打开文件"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(file_path)
            elif os.name == 'posix':  # Linux/Mac
                subprocess.run(['xdg-open', file_path])
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开文件: {e}")
    
    def format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"

class FileSharingWidget(QWidget):
    """文件分享主窗口"""
    file_shared = pyqtSignal(str, str)  # 文件路径, 分享者
    file_requested = pyqtSignal(str)    # 请求文件
    student_file_received = pyqtSignal(dict)  # 收到学生分享的文件
    
    def __init__(self, user_name="", group_id="", parent=None):
        super().__init__(parent)
        self.user_name = user_name
        self.group_id = group_id
        self.shared_files = {}  # file_path -> file_info
        self.student_shared_files = {}  # 存储学生分享的文件
        
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        self.setWindowTitle("资料分享")
        self.setMinimumSize(800, 600)
        
        main_layout = QHBoxLayout(self)
        
        # 左侧文件列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 控制按钮
        btn_layout = QHBoxLayout()
        
        self.add_file_btn = QPushButton("添加文件")
        self.add_file_btn.clicked.connect(self.add_files)
        
        self.remove_file_btn = QPushButton("移除文件")
        self.remove_file_btn.clicked.connect(self.remove_selected_file)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_file_list)
        
        btn_layout.addWidget(self.add_file_btn)
        btn_layout.addWidget(self.remove_file_btn)
        btn_layout.addWidget(self.refresh_btn)
        
        left_layout.addLayout(btn_layout)
        
        # 文件类型过滤
        filter_layout = QHBoxLayout()
        filter_label = QLabel("文件类型:")
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "全部", "图片", "文档", "视频", "音频", "其他"
        ])
        self.filter_combo.currentTextChanged.connect(self.filter_files)
        
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.filter_combo)
        filter_layout.addStretch()
        
        left_layout.addLayout(filter_layout)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.itemClicked.connect(self.on_file_selected)
        self.file_list.itemDoubleClicked.connect(self.on_file_double_clicked)
        left_layout.addWidget(self.file_list)
        
        # 右侧预览区域
        self.preview_widget = FilePreviewWidget()
        
        # 使用分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(self.preview_widget)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        main_layout.addWidget(splitter)
    
    def add_files(self):
        """添加文件"""
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self, "选择要分享的文件", "", 
            "所有支持的文件 (*.jpg *.jpeg *.png *.bmp *.gif *.pdf *.doc *.docx *.txt *.md);;所有文件 (*)"
        )
        
        if file_paths:
            for file_path in file_paths:
                self.add_shared_file(file_path, self.user_name)
    
    def add_shared_file(self, file_path, shared_by):
        """添加分享文件"""
        if file_path not in self.shared_files:
            file_info = {
                'path': file_path,
                'shared_by': shared_by,
                'shared_time': None,
                'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
            }
            
            self.shared_files[file_path] = file_info
            
            # 添加到列表
            item = FileItem(file_path, shared_by)
            self.file_list.addItem(item)
            
            # 发送分享信号
            self.file_shared.emit(file_path, shared_by)
    
    def remove_selected_file(self):
        """移除选中的文件"""
        current_item = self.file_list.currentItem()
        if current_item and isinstance(current_item, FileItem):
            file_path = current_item.file_path
            
            # 只能移除自己分享的文件
            if self.shared_files[file_path]['shared_by'] == self.user_name:
                reply = QMessageBox.question(
                    self, "确认", "确定要移除这个文件吗？",
                    QMessageBox.Yes | QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    del self.shared_files[file_path]
                    self.file_list.takeItem(self.file_list.row(current_item))
            else:
                QMessageBox.information(self, "提示", "只能移除自己分享的文件")
    
    def on_file_selected(self, item):
        """文件被选中"""
        if isinstance(item, FileItem):
            self.preview_widget.preview_file(item.file_path)
    
    def on_file_double_clicked(self, item):
        """文件被双击"""
        if isinstance(item, FileItem):
            self.preview_widget.open_with_system(item.file_path)
    
    def filter_files(self, filter_type):
        """过滤文件"""
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if isinstance(item, FileItem):
                should_show = self.should_show_file(item.file_path, filter_type)
                item.setHidden(not should_show)
    
    def should_show_file(self, file_path, filter_type):
        """判断文件是否应该显示"""
        if filter_type == "全部":
            return True
        
        file_ext = os.path.splitext(file_path)[1].lower()
        
        type_map = {
            "图片": ['.jpg', '.jpeg', '.png', '.bmp', '.gif'],
            "文档": ['.pdf', '.doc', '.docx', '.txt', '.md'],
            "视频": ['.mp4', '.avi', '.mkv', '.mov'],
            "音频": ['.mp3', '.wav', '.flac', '.aac']
        }
        
        if filter_type in type_map:
            return file_ext in type_map[filter_type]
        
        # "其他"类型
        all_known_exts = []
        for exts in type_map.values():
            all_known_exts.extend(exts)
        
        return file_ext not in all_known_exts
    
    def refresh_file_list(self):
        """刷新文件列表"""
        # 检查文件是否仍然存在
        to_remove = []
        for file_path in self.shared_files:
            if not os.path.exists(file_path):
                to_remove.append(file_path)
        
        # 移除不存在的文件
        for file_path in to_remove:
            del self.shared_files[file_path]
            
            # 从列表中移除
            for i in range(self.file_list.count()):
                item = self.file_list.item(i)
                if isinstance(item, FileItem) and item.file_path == file_path:
                    self.file_list.takeItem(i)
                    break
    
    def get_shared_files(self):
        """获取分享的文件列表"""
        return list(self.shared_files.keys())
    
    def clear_all_files(self):
        """清空所有文件"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有分享的文件吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.shared_files.clear()
            self.file_list.clear()

    def add_shared_file(self, file_info, shared_by):
        """添加学生分享的文件"""
        try:
            # 创建文件项
            file_item = FileItem(
                file_info.get('filename', '未知文件'),
                shared_by
            )

            # 添加到列表
            self.file_list.addItem(file_item)

            # 存储文件信息
            file_key = f"{shared_by}_{file_info.get('filename', '')}"
            self.student_shared_files[file_key] = {
                'file_info': file_info,
                'shared_by': shared_by,
                'shared_at': file_info.get('shared_at'),
                'item': file_item
            }

            self.student_file_received.emit(file_info)
            print(f"收到学生 {shared_by} 分享的文件: {file_info.get('filename')}")

        except Exception as e:
            print(f"添加学生分享文件失败: {e}")

    def remove_student_file(self, file_key):
        """移除学生分享的文件"""
        if file_key in self.student_shared_files:
            file_data = self.student_shared_files[file_key]
            item = file_data['item']

            # 从列表中移除
            row = self.file_list.row(item)
            if row >= 0:
                self.file_list.takeItem(row)

            # 从存储中移除
            del self.student_shared_files[file_key]

    def get_student_shared_files(self):
        """获取所有学生分享的文件"""
        return list(self.student_shared_files.values())
