# group/features/artwork_display.py
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QPushButton, QFileDialog, QScrollArea,
                             QFrame, QComboBox, QMessageBox, QMenu, QAction)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QPixmap, QFont, QPainter, QPen
import os
from pathlib import Path

class ArtworkItem(QFrame):
    """单个作品展示项"""
    item_clicked = pyqtSignal(str)  # 点击信号，传递文件路径
    item_removed = pyqtSignal(str)  # 移除信号
    
    def __init__(self, file_path, author_name="", parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.author_name = author_name
        
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(1)
        self.setStyleSheet("QFrame:hover { border: 2px solid #4CAF50; }")
        
        self.initUI()
        self.load_image()
    
    def initUI(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 图片显示标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;")
        self.image_label.setMinimumSize(150, 150)
        self.image_label.setScaledContents(True)
        layout.addWidget(self.image_label)
        
        # 文件名标签
        filename = os.path.basename(self.file_path)
        self.filename_label = QLabel(filename)
        self.filename_label.setAlignment(Qt.AlignCenter)
        self.filename_label.setFont(QFont("Arial", 9))
        self.filename_label.setWordWrap(True)
        layout.addWidget(self.filename_label)
        
        # 作者标签
        if self.author_name:
            self.author_label = QLabel(f"作者: {self.author_name}")
            self.author_label.setAlignment(Qt.AlignCenter)
            self.author_label.setFont(QFont("Arial", 8))
            self.author_label.setStyleSheet("color: #666;")
            layout.addWidget(self.author_label)
    
    def load_image(self):
        """加载图片"""
        try:
            pixmap = QPixmap(self.file_path)
            if not pixmap.isNull():
                # 缩放图片以适应标签
                scaled_pixmap = pixmap.scaled(
                    self.image_label.size(), 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                self.image_label.setPixmap(scaled_pixmap)
            else:
                self.image_label.setText("无法加载图片")
        except Exception as e:
            self.image_label.setText("加载失败")
            print(f"加载图片失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.item_clicked.emit(self.file_path)
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.pos())
    
    def show_context_menu(self, pos):
        """显示右键菜单"""
        menu = QMenu(self)
        
        view_action = QAction("查看大图", self)
        view_action.triggered.connect(lambda: self.item_clicked.emit(self.file_path))
        menu.addAction(view_action)
        
        remove_action = QAction("移除", self)
        remove_action.triggered.connect(lambda: self.item_removed.emit(self.file_path))
        menu.addAction(remove_action)
        
        menu.exec_(self.mapToGlobal(pos))

class ArtworkDisplayWidget(QWidget):
    """作品展示主窗口"""
    artwork_selected = pyqtSignal(str)  # 作品被选中
    layout_changed = pyqtSignal(str)    # 布局改变
    student_artwork_received = pyqtSignal(dict)  # 收到学生分享的作品
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.artworks = []  # 存储作品文件路径
        self.current_layout = "grid_2x2"  # 当前布局模式
        self.student_artworks = {}  # 存储学生分享的作品
        
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        self.setWindowTitle("作品展示")
        self.setMinimumSize(800, 600)
        
        main_layout = QVBoxLayout(self)
        
        # 顶部控制栏
        control_layout = QHBoxLayout()
        
        # 布局选择
        layout_label = QLabel("布局模式:")
        self.layout_combo = QComboBox()
        self.layout_combo.addItems([
            "单画面", "双画面(横)", "双画面(竖)", 
            "三画面", "四画面", "六画面"
        ])
        self.layout_combo.currentTextChanged.connect(self.change_layout)
        
        # 添加作品按钮
        self.add_btn = QPushButton("添加作品")
        self.add_btn.clicked.connect(self.add_artwork)
        
        # 清空按钮
        self.clear_btn = QPushButton("清空所有")
        self.clear_btn.clicked.connect(self.clear_all_artworks)
        
        control_layout.addWidget(layout_label)
        control_layout.addWidget(self.layout_combo)
        control_layout.addStretch()
        control_layout.addWidget(self.add_btn)
        control_layout.addWidget(self.clear_btn)
        
        main_layout.addLayout(control_layout)
        
        # 作品展示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.display_widget = QWidget()
        self.scroll_area.setWidget(self.display_widget)
        
        main_layout.addWidget(self.scroll_area)
        
        # 初始化布局
        self.setup_layout()
    
    def setup_layout(self):
        """设置显示布局"""
        # 清除现有布局
        if self.display_widget.layout():
            while self.display_widget.layout().count():
                child = self.display_widget.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            self.display_widget.layout().deleteLater()
        
        # 根据当前布局模式创建新布局
        layout_map = {
            "单画面": (1, 1),
            "双画面(横)": (1, 2),
            "双画面(竖)": (2, 1),
            "三画面": (1, 3),
            "四画面": (2, 2),
            "六画面": (2, 3)
        }
        
        current_text = self.layout_combo.currentText()
        if current_text in layout_map:
            rows, cols = layout_map[current_text]
            self.create_grid_layout(rows, cols)
        else:
            self.create_grid_layout(2, 2)  # 默认2x2
    
    def create_grid_layout(self, rows, cols):
        """创建网格布局"""
        grid_layout = QGridLayout(self.display_widget)
        grid_layout.setSpacing(10)
        
        # 创建占位符
        self.artwork_slots = []
        for row in range(rows):
            for col in range(cols):
                slot = QLabel("点击添加作品")
                slot.setAlignment(Qt.AlignCenter)
                slot.setStyleSheet("""
                    QLabel {
                        border: 2px dashed #ccc;
                        background-color: #f9f9f9;
                        min-height: 200px;
                        font-size: 14px;
                        color: #666;
                    }
                    QLabel:hover {
                        border-color: #4CAF50;
                        background-color: #f0f8f0;
                    }
                """)
                slot.mousePressEvent = lambda event, r=row, c=col: self.slot_clicked(r, c)
                
                grid_layout.addWidget(slot, row, col)
                self.artwork_slots.append(slot)
        
        # 更新现有作品显示
        self.update_artwork_display()
    
    def slot_clicked(self, row, col):
        """槽位点击事件"""
        index = row * self.get_cols() + col
        if index < len(self.artworks):
            # 如果已有作品，显示大图
            self.show_artwork_detail(self.artworks[index])
        else:
            # 如果没有作品，添加新作品
            self.add_artwork()
    
    def get_cols(self):
        """获取当前布局的列数"""
        layout_map = {
            "单画面": 1, "双画面(横)": 2, "双画面(竖)": 1,
            "三画面": 3, "四画面": 2, "六画面": 3
        }
        return layout_map.get(self.layout_combo.currentText(), 2)
    
    def change_layout(self, layout_text):
        """改变布局模式"""
        self.current_layout = layout_text
        self.setup_layout()
        self.layout_changed.emit(layout_text)
    
    def add_artwork(self):
        """添加作品"""
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self, "选择作品文件", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif);;所有文件 (*)"
        )
        
        if file_paths:
            for file_path in file_paths:
                if file_path not in self.artworks:
                    self.artworks.append(file_path)
            
            self.update_artwork_display()
    
    def remove_artwork(self, file_path):
        """移除作品"""
        if file_path in self.artworks:
            self.artworks.remove(file_path)
            self.update_artwork_display()
    
    def clear_all_artworks(self):
        """清空所有作品"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有作品吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.artworks.clear()
            self.update_artwork_display()
    
    def update_artwork_display(self):
        """更新作品显示"""
        for i, slot in enumerate(self.artwork_slots):
            if i < len(self.artworks):
                # 显示作品
                file_path = self.artworks[i]
                try:
                    pixmap = QPixmap(file_path)
                    if not pixmap.isNull():
                        scaled_pixmap = pixmap.scaled(
                            slot.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                        )
                        slot.setPixmap(scaled_pixmap)
                        slot.setText("")
                        
                        # 添加文件名提示
                        filename = os.path.basename(file_path)
                        slot.setToolTip(f"文件: {filename}")
                    else:
                        slot.setText("无法加载图片")
                        slot.setPixmap(QPixmap())
                except Exception as e:
                    slot.setText("加载失败")
                    slot.setPixmap(QPixmap())
                    print(f"加载作品失败: {e}")
            else:
                # 显示空槽位
                slot.setText("点击添加作品")
                slot.setPixmap(QPixmap())
                slot.setToolTip("")
    
    def show_artwork_detail(self, file_path):
        """显示作品详情"""
        self.artwork_selected.emit(file_path)
        # 这里可以打开一个详细查看窗口
    
    def get_artworks(self):
        """获取当前作品列表"""
        return self.artworks.copy()
    
    def set_artworks(self, artworks):
        """设置作品列表"""
        self.artworks = artworks.copy()
        self.update_artwork_display()

    def add_shared_artwork(self, artwork_info, shared_by):
        """添加学生分享的作品"""
        try:
            artworks = artwork_info.get('artworks', [])
            layout = artwork_info.get('layout', 'single')

            # 存储学生作品信息
            student_key = f"{shared_by}_{artwork_info.get('shared_at', '')}"
            self.student_artworks[student_key] = {
                'artworks': artworks,
                'layout': layout,
                'shared_by': shared_by,
                'shared_at': artwork_info.get('shared_at')
            }

            # 将学生作品添加到展示列表
            for artwork in artworks:
                # 为学生作品添加标识
                artwork_with_info = {
                    'path': artwork.get('url', ''),
                    'title': artwork.get('title', ''),
                    'author': shared_by,
                    'date': artwork.get('date', ''),
                    'type': 'student_shared'
                }
                self.artworks.append(artwork_with_info['path'])

            # 更新显示
            self.update_artwork_display()

            # 发送信号
            self.student_artwork_received.emit(artwork_info)

            print(f"收到学生 {shared_by} 分享的 {len(artworks)} 个作品")

        except Exception as e:
            print(f"添加学生分享作品失败: {e}")

    def remove_student_artwork(self, student_key):
        """移除学生分享的作品"""
        if student_key in self.student_artworks:
            artwork_data = self.student_artworks[student_key]

            # 从展示列表中移除学生作品
            for artwork in artwork_data['artworks']:
                artwork_path = artwork.get('url', '')
                if artwork_path in self.artworks:
                    self.artworks.remove(artwork_path)

            # 从存储中移除
            del self.student_artworks[student_key]

            # 更新显示
            self.update_artwork_display()

    def get_student_artworks(self):
        """获取所有学生分享的作品"""
        return list(self.student_artworks.values())

    def clear_student_artworks(self):
        """清空所有学生分享的作品"""
        # 从展示列表中移除所有学生作品
        for artwork_data in self.student_artworks.values():
            for artwork in artwork_data['artworks']:
                artwork_path = artwork.get('url', '')
                if artwork_path in self.artworks:
                    self.artworks.remove(artwork_path)

        # 清空存储
        self.student_artworks.clear()

        # 更新显示
        self.update_artwork_display()
