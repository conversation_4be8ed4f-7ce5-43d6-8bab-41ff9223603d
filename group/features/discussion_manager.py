# group/features/discussion_manager.py
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTextEdit, QListWidget, QListWidgetItem,
                             QProgressBar, QTabWidget, QFrame, QScrollArea,
                             QMessageBox, QDialog, QDialogButtonBox, QLineEdit,
                             QComboBox, QSpinBox, QTimeEdit, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QTime
from PyQt5.QtGui import QFont, QPixmap, QIcon
import json
import time
from datetime import datetime, timedelta
from enum import Enum

class DiscussionStatus(Enum):
    """讨论状态"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class DiscussionTopic:
    """讨论主题"""
    def __init__(self, topic_id, title, description, materials=None, time_limit=None):
        self.topic_id = topic_id
        self.title = title
        self.description = description
        self.materials = materials or []  # 相关资料
        self.time_limit = time_limit  # 讨论时间限制（分钟）
        self.created_at = datetime.now()
        
    def to_dict(self):
        return {
            'topic_id': self.topic_id,
            'title': self.title,
            'description': self.description,
            'materials': self.materials,
            'time_limit': self.time_limit,
            'created_at': self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data):
        topic = cls(
            data['topic_id'],
            data['title'],
            data['description'],
            data.get('materials', []),
            data.get('time_limit')
        )
        if 'created_at' in data:
            topic.created_at = datetime.fromisoformat(data['created_at'])
        return topic

class StudentContribution:
    """学生贡献"""
    def __init__(self, student_id, student_name, content_type, content, timestamp=None):
        self.student_id = student_id
        self.student_name = student_name
        self.content_type = content_type  # text, image, file, voice
        self.content = content
        self.timestamp = timestamp or datetime.now()
        
    def to_dict(self):
        return {
            'student_id': self.student_id,
            'student_name': self.student_name,
            'content_type': self.content_type,
            'content': self.content,
            'timestamp': self.timestamp.isoformat()
        }

class DiscussionSession:
    """讨论会话"""
    def __init__(self, session_id, topic, participants=None):
        self.session_id = session_id
        self.topic = topic
        self.participants = participants or []  # 参与者列表
        self.status = DiscussionStatus.NOT_STARTED
        self.start_time = None
        self.end_time = None
        self.contributions = []  # 学生贡献列表
        self.progress_notes = []  # 进度记录
        
    def start_discussion(self):
        """开始讨论"""
        self.status = DiscussionStatus.IN_PROGRESS
        self.start_time = datetime.now()
        
    def pause_discussion(self):
        """暂停讨论"""
        if self.status == DiscussionStatus.IN_PROGRESS:
            self.status = DiscussionStatus.PAUSED
            
    def resume_discussion(self):
        """恢复讨论"""
        if self.status == DiscussionStatus.PAUSED:
            self.status = DiscussionStatus.IN_PROGRESS
            
    def complete_discussion(self):
        """完成讨论"""
        self.status = DiscussionStatus.COMPLETED
        self.end_time = datetime.now()
        
    def add_contribution(self, contribution):
        """添加学生贡献"""
        self.contributions.append(contribution)
        
    def get_duration(self):
        """获取讨论持续时间"""
        if self.start_time:
            end = self.end_time or datetime.now()
            return end - self.start_time
        return timedelta(0)

class DiscussionManagerWidget(QWidget):
    """分组讨论管理主界面"""
    
    # 信号定义
    topic_distributed = pyqtSignal(dict)  # 主题分发
    discussion_started = pyqtSignal(str)  # 讨论开始
    discussion_paused = pyqtSignal(str)   # 讨论暂停
    discussion_completed = pyqtSignal(str)  # 讨论完成
    contribution_received = pyqtSignal(dict)  # 收到学生贡献
    
    def __init__(self, socket_manager=None, parent=None):
        super().__init__(parent)
        self.socket_manager = socket_manager
        self.current_session = None
        self.available_topics = []
        self.student_list = []
        
        self.initUI()
        self.setup_timer()
        
    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle("分组讨论管理")
        self.setMinimumSize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 主题管理标签页
        self.topic_tab = self.create_topic_tab()
        self.tab_widget.addTab(self.topic_tab, "讨论主题")
        
        # 讨论进行标签页
        self.discussion_tab = self.create_discussion_tab()
        self.tab_widget.addTab(self.discussion_tab, "讨论进行")
        
        # 学生贡献标签页
        self.contribution_tab = self.create_contribution_tab()
        self.tab_widget.addTab(self.contribution_tab, "学生贡献")
        
        # 讨论总结标签页
        self.summary_tab = self.create_summary_tab()
        self.tab_widget.addTab(self.summary_tab, "讨论总结")
        
    def create_topic_tab(self):
        """创建主题管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主题列表
        topic_group = QGroupBox("讨论主题")
        topic_layout = QVBoxLayout(topic_group)
        
        # 主题操作按钮
        topic_btn_layout = QHBoxLayout()
        self.add_topic_btn = QPushButton("添加主题")
        self.edit_topic_btn = QPushButton("编辑主题")
        self.delete_topic_btn = QPushButton("删除主题")
        self.distribute_topic_btn = QPushButton("分发主题")
        
        topic_btn_layout.addWidget(self.add_topic_btn)
        topic_btn_layout.addWidget(self.edit_topic_btn)
        topic_btn_layout.addWidget(self.delete_topic_btn)
        topic_btn_layout.addStretch()
        topic_btn_layout.addWidget(self.distribute_topic_btn)
        
        topic_layout.addLayout(topic_btn_layout)
        
        # 主题列表
        self.topic_list = QListWidget()
        topic_layout.addWidget(self.topic_list)
        
        layout.addWidget(topic_group)
        
        # 连接信号
        self.add_topic_btn.clicked.connect(self.add_topic)
        self.edit_topic_btn.clicked.connect(self.edit_topic)
        self.delete_topic_btn.clicked.connect(self.delete_topic)
        self.distribute_topic_btn.clicked.connect(self.distribute_topic)
        
        return widget
        
    def create_discussion_tab(self):
        """创建讨论进行标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 讨论控制区域
        control_group = QGroupBox("讨论控制")
        control_layout = QVBoxLayout(control_group)
        
        # 当前主题显示
        self.current_topic_label = QLabel("当前主题: 未选择")
        self.current_topic_label.setFont(QFont("Arial", 12, QFont.Bold))
        control_layout.addWidget(self.current_topic_label)
        
        # 讨论状态和时间
        status_layout = QHBoxLayout()
        self.status_label = QLabel("状态: 未开始")
        self.time_label = QLabel("时间: 00:00:00")
        self.progress_bar = QProgressBar()
        
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.time_label)
        status_layout.addWidget(self.progress_bar)
        
        control_layout.addLayout(status_layout)
        
        # 控制按钮
        btn_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始讨论")
        self.pause_btn = QPushButton("暂停讨论")
        self.resume_btn = QPushButton("恢复讨论")
        self.complete_btn = QPushButton("完成讨论")
        
        btn_layout.addWidget(self.start_btn)
        btn_layout.addWidget(self.pause_btn)
        btn_layout.addWidget(self.resume_btn)
        btn_layout.addWidget(self.complete_btn)
        btn_layout.addStretch()
        
        control_layout.addLayout(btn_layout)
        
        layout.addWidget(control_group)
        
        # 参与者列表
        participants_group = QGroupBox("参与者")
        participants_layout = QVBoxLayout(participants_group)
        
        self.participants_list = QListWidget()
        participants_layout.addWidget(self.participants_list)
        
        layout.addWidget(participants_group)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_discussion)
        self.pause_btn.clicked.connect(self.pause_discussion)
        self.resume_btn.clicked.connect(self.resume_discussion)
        self.complete_btn.clicked.connect(self.complete_discussion)
        
        # 初始状态
        self.update_discussion_controls()
        
        return widget
        
    def create_contribution_tab(self):
        """创建学生贡献标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 贡献列表
        self.contribution_list = QListWidget()
        layout.addWidget(self.contribution_list)
        
        # 贡献详情
        detail_group = QGroupBox("贡献详情")
        detail_layout = QVBoxLayout(detail_group)
        
        self.contribution_detail = QTextEdit()
        self.contribution_detail.setReadOnly(True)
        detail_layout.addWidget(self.contribution_detail)
        
        layout.addWidget(detail_group)
        
        # 连接信号
        self.contribution_list.currentItemChanged.connect(self.show_contribution_detail)
        
        return widget
        
    def create_summary_tab(self):
        """创建讨论总结标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 总结信息
        summary_group = QGroupBox("讨论总结")
        summary_layout = QVBoxLayout(summary_group)
        
        self.summary_text = QTextEdit()
        summary_layout.addWidget(self.summary_text)
        
        # 导出按钮
        export_layout = QHBoxLayout()
        self.export_report_btn = QPushButton("导出报告")
        self.save_summary_btn = QPushButton("保存总结")
        
        export_layout.addWidget(self.export_report_btn)
        export_layout.addWidget(self.save_summary_btn)
        export_layout.addStretch()
        
        summary_layout.addLayout(export_layout)
        
        layout.addWidget(summary_group)
        
        # 连接信号
        self.export_report_btn.clicked.connect(self.export_report)
        self.save_summary_btn.clicked.connect(self.save_summary)
        
        return widget
        
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_discussion_time)
        self.timer.start(1000)  # 每秒更新一次
        
    def add_topic(self):
        """添加讨论主题"""
        dialog = TopicEditDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            topic = dialog.get_topic()
            self.available_topics.append(topic)
            self.update_topic_list()
            
    def edit_topic(self):
        """编辑讨论主题"""
        current_item = self.topic_list.currentItem()
        if current_item:
            topic_index = self.topic_list.row(current_item)
            topic = self.available_topics[topic_index]
            
            dialog = TopicEditDialog(self, topic)
            if dialog.exec_() == QDialog.Accepted:
                updated_topic = dialog.get_topic()
                self.available_topics[topic_index] = updated_topic
                self.update_topic_list()
                
    def delete_topic(self):
        """删除讨论主题"""
        current_item = self.topic_list.currentItem()
        if current_item:
            reply = QMessageBox.question(
                self, "确认删除", "确定要删除选中的主题吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                topic_index = self.topic_list.row(current_item)
                del self.available_topics[topic_index]
                self.update_topic_list()
                
    def distribute_topic(self):
        """分发讨论主题"""
        current_item = self.topic_list.currentItem()
        if current_item:
            topic_index = self.topic_list.row(current_item)
            topic = self.available_topics[topic_index]
            
            # 创建讨论会话
            session_id = f"session_{int(time.time())}"
            self.current_session = DiscussionSession(session_id, topic, self.student_list)
            
            # 更新界面
            self.current_topic_label.setText(f"当前主题: {topic.title}")
            self.update_discussion_controls()
            
            # 发送主题到学生端
            if self.socket_manager:
                topic_data = topic.to_dict()
                self.socket_manager.send_event('distribute_discussion_topic', {
                    'session_id': session_id,
                    'topic': topic_data
                })
            
            self.topic_distributed.emit(topic.to_dict())
            
            # 切换到讨论进行标签页
            self.tab_widget.setCurrentIndex(1)
            
    def start_discussion(self):
        """开始讨论"""
        if self.current_session:
            self.current_session.start_discussion()
            self.update_discussion_controls()
            
            if self.socket_manager:
                self.socket_manager.send_event('start_discussion', {
                    'session_id': self.current_session.session_id
                })
            
            self.discussion_started.emit(self.current_session.session_id)
            
    def pause_discussion(self):
        """暂停讨论"""
        if self.current_session:
            self.current_session.pause_discussion()
            self.update_discussion_controls()
            
            if self.socket_manager:
                self.socket_manager.send_event('pause_discussion', {
                    'session_id': self.current_session.session_id
                })
            
            self.discussion_paused.emit(self.current_session.session_id)
            
    def resume_discussion(self):
        """恢复讨论"""
        if self.current_session:
            self.current_session.resume_discussion()
            self.update_discussion_controls()
            
            if self.socket_manager:
                self.socket_manager.send_event('resume_discussion', {
                    'session_id': self.current_session.session_id
                })
            
    def complete_discussion(self):
        """完成讨论"""
        if self.current_session:
            self.current_session.complete_discussion()
            self.update_discussion_controls()
            self.generate_summary()
            
            if self.socket_manager:
                self.socket_manager.send_event('complete_discussion', {
                    'session_id': self.current_session.session_id
                })
            
            self.discussion_completed.emit(self.current_session.session_id)
            
            # 切换到总结标签页
            self.tab_widget.setCurrentIndex(3)
            
    def update_topic_list(self):
        """更新主题列表"""
        self.topic_list.clear()
        for topic in self.available_topics:
            item = QListWidgetItem(f"{topic.title} - {topic.description[:50]}...")
            item.setData(Qt.UserRole, topic)
            self.topic_list.addItem(item)
            
    def update_discussion_controls(self):
        """更新讨论控制按钮状态"""
        if not self.current_session:
            self.start_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.resume_btn.setEnabled(False)
            self.complete_btn.setEnabled(False)
            self.status_label.setText("状态: 未选择主题")
            return
            
        status = self.current_session.status
        
        self.start_btn.setEnabled(status == DiscussionStatus.NOT_STARTED)
        self.pause_btn.setEnabled(status == DiscussionStatus.IN_PROGRESS)
        self.resume_btn.setEnabled(status == DiscussionStatus.PAUSED)
        self.complete_btn.setEnabled(status in [DiscussionStatus.IN_PROGRESS, DiscussionStatus.PAUSED])
        
        status_text = {
            DiscussionStatus.NOT_STARTED: "未开始",
            DiscussionStatus.IN_PROGRESS: "进行中",
            DiscussionStatus.PAUSED: "已暂停",
            DiscussionStatus.COMPLETED: "已完成",
            DiscussionStatus.CANCELLED: "已取消"
        }
        
        self.status_label.setText(f"状态: {status_text.get(status, '未知')}")
        
    def update_discussion_time(self):
        """更新讨论时间显示"""
        if self.current_session and self.current_session.status == DiscussionStatus.IN_PROGRESS:
            duration = self.current_session.get_duration()
            hours, remainder = divmod(duration.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            self.time_label.setText(f"时间: {time_str}")
            
            # 更新进度条（如果有时间限制）
            if self.current_session.topic.time_limit:
                progress = (duration.total_seconds() / (self.current_session.topic.time_limit * 60)) * 100
                self.progress_bar.setValue(min(int(progress), 100))
            else:
                self.progress_bar.setValue(0)
                
    def add_student_contribution(self, contribution_data):
        """添加学生贡献"""
        if self.current_session:
            contribution = StudentContribution(
                contribution_data['student_id'],
                contribution_data['student_name'],
                contribution_data['content_type'],
                contribution_data['content']
            )
            
            self.current_session.add_contribution(contribution)
            self.update_contribution_list()
            
            self.contribution_received.emit(contribution_data)
            
    def update_contribution_list(self):
        """更新贡献列表"""
        self.contribution_list.clear()
        
        if self.current_session:
            for contribution in self.current_session.contributions:
                item_text = f"{contribution.student_name} - {contribution.content_type} - {contribution.timestamp.strftime('%H:%M:%S')}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, contribution)
                self.contribution_list.addItem(item)
                
    def show_contribution_detail(self, current, previous):
        """显示贡献详情"""
        if current:
            contribution = current.data(Qt.UserRole)
            if contribution:
                detail_text = f"学生: {contribution.student_name}\n"
                detail_text += f"类型: {contribution.content_type}\n"
                detail_text += f"时间: {contribution.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                detail_text += f"内容:\n{contribution.content}"
                
                self.contribution_detail.setText(detail_text)
                
    def generate_summary(self):
        """生成讨论总结"""
        if not self.current_session:
            return
            
        summary = f"讨论主题: {self.current_session.topic.title}\n\n"
        summary += f"讨论描述: {self.current_session.topic.description}\n\n"
        summary += f"开始时间: {self.current_session.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        summary += f"结束时间: {self.current_session.end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        summary += f"讨论时长: {self.current_session.get_duration()}\n\n"
        summary += f"参与人数: {len(self.current_session.participants)}\n"
        summary += f"贡献数量: {len(self.current_session.contributions)}\n\n"
        
        summary += "学生贡献:\n"
        for i, contribution in enumerate(self.current_session.contributions, 1):
            summary += f"{i}. {contribution.student_name} ({contribution.timestamp.strftime('%H:%M:%S')})\n"
            summary += f"   类型: {contribution.content_type}\n"
            summary += f"   内容: {contribution.content[:100]}...\n\n"
            
        self.summary_text.setText(summary)
        
    def export_report(self):
        """导出讨论报告"""
        # 实现报告导出功能
        QMessageBox.information(self, "导出报告", "报告导出功能待实现")
        
    def save_summary(self):
        """保存讨论总结"""
        # 实现总结保存功能
        QMessageBox.information(self, "保存总结", "总结保存功能待实现")

class TopicEditDialog(QDialog):
    """主题编辑对话框"""
    
    def __init__(self, parent=None, topic=None):
        super().__init__(parent)
        self.topic = topic
        self.initUI()
        
        if topic:
            self.load_topic(topic)
            
    def initUI(self):
        """初始化界面"""
        self.setWindowTitle("编辑讨论主题")
        self.setMinimumSize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 主题标题
        layout.addWidget(QLabel("主题标题:"))
        self.title_edit = QLineEdit()
        layout.addWidget(self.title_edit)
        
        # 主题描述
        layout.addWidget(QLabel("主题描述:"))
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        layout.addWidget(self.description_edit)
        
        # 时间限制
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("时间限制(分钟):"))
        self.time_limit_spin = QSpinBox()
        self.time_limit_spin.setRange(0, 180)
        self.time_limit_spin.setValue(30)
        self.time_limit_spin.setSpecialValueText("无限制")
        time_layout.addWidget(self.time_limit_spin)
        time_layout.addStretch()
        
        layout.addLayout(time_layout)
        
        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
    def load_topic(self, topic):
        """加载主题数据"""
        self.title_edit.setText(topic.title)
        self.description_edit.setText(topic.description)
        if topic.time_limit:
            self.time_limit_spin.setValue(topic.time_limit)
        else:
            self.time_limit_spin.setValue(0)
            
    def get_topic(self):
        """获取主题数据"""
        topic_id = self.topic.topic_id if self.topic else f"topic_{int(time.time())}"
        time_limit = self.time_limit_spin.value() if self.time_limit_spin.value() > 0 else None
        
        return DiscussionTopic(
            topic_id,
            self.title_edit.text(),
            self.description_edit.toPlainText(),
            time_limit=time_limit
        )
