# group/features/collaborative_whiteboard.py
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtGui import QColor, QPen, QBrush
from PyQt5.QtWidgets import QApplication
import json
import time
import uuid

class CollaborativeOperation:
    """协作操作数据结构"""
    def __init__(self, op_type, user_id, user_name, data, timestamp=None):
        self.id = str(uuid.uuid4())
        self.type = op_type  # 'draw', 'erase', 'text', 'image', 'move', 'delete'
        self.user_id = user_id
        self.user_name = user_name
        self.data = data
        self.timestamp = timestamp or time.time()
    
    def to_dict(self):
        return {
            'id': self.id,
            'type': self.type,
            'user_id': self.user_id,
            'user_name': self.user_name,
            'data': self.data,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data):
        op = cls(
            data['type'],
            data['user_id'], 
            data['user_name'],
            data['data'],
            data['timestamp']
        )
        op.id = data['id']
        return op

class CollaborativeWhiteboardManager(QObject):
    """协同白板管理器"""
    operation_received = pyqtSignal(dict)  # 接收到远程操作
    operation_sent = pyqtSignal(dict)  # 发送本地操作
    user_joined = pyqtSignal(str, str)  # 用户加入 (user_id, user_name)
    user_left = pyqtSignal(str)  # 用户离开 (user_id)
    conflict_resolved = pyqtSignal(dict)  # 冲突解决
    
    def __init__(self, socket_manager, group_id, user_id, user_name, parent=None):
        super().__init__(parent)
        self.socket_manager = socket_manager
        self.group_id = group_id
        self.user_id = user_id
        self.user_name = user_name

        # 操作历史和状态
        self.operation_history = []  # 所有操作的历史记录
        self.local_operations = []   # 本地未确认的操作
        self.remote_operations = []  # 远程操作缓存

        # 用户管理
        self.active_users = {}  # user_id -> user_info
        self.user_colors = {}   # user_id -> color
        self.color_palette = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
        ]
        self.color_index = 0

        # 冲突解决
        self.operation_queue = []
        self.processing_operations = False

        # 定时同步
        self.sync_timer = QTimer()
        self.sync_timer.timeout.connect(self.sync_operations)
        self.sync_timer.start(1000)  # 每秒同步一次

        # 连接socket事件
        self.setup_socket_handlers()

        # 添加自己作为第一个用户
        self.add_user(user_id, user_name, 'group')
    
    def setup_socket_handlers(self):
        """设置socket事件处理器"""
        if self.socket_manager:
            # 这里需要在socket_manager中添加相应的信号
            pass
    
    def add_local_operation(self, op_type, data):
        """添加本地操作"""
        operation = CollaborativeOperation(
            op_type, self.user_id, self.user_name, data
        )
        
        # 添加到本地操作队列
        self.local_operations.append(operation)
        self.operation_history.append(operation)
        
        # 立即发送给其他用户
        self.send_operation(operation)
        
        return operation
    
    def send_operation(self, operation):
        """发送操作给其他用户"""
        if self.socket_manager and self.socket_manager.is_connected():
            event_data = {
                'group_id': self.group_id,
                'operation': operation.to_dict()
            }
            self.socket_manager.send_event('collaborative_operation', event_data)
            self.operation_sent.emit(operation.to_dict())
    
    def receive_operation(self, operation_data):
        """接收远程操作"""
        try:
            operation = CollaborativeOperation.from_dict(operation_data)
            
            # 检查是否是自己的操作
            if operation.user_id == self.user_id:
                return
            
            # 添加到远程操作队列
            self.remote_operations.append(operation)
            self.operation_queue.append(operation)
            
            # 处理操作队列
            self.process_operation_queue()
            
        except Exception as e:
            print(f"处理远程操作失败: {e}")
    
    def process_operation_queue(self):
        """处理操作队列"""
        if self.processing_operations:
            return
        
        self.processing_operations = True
        
        try:
            # 按时间戳排序
            self.operation_queue.sort(key=lambda op: op.timestamp)
            
            while self.operation_queue:
                operation = self.operation_queue.pop(0)
                self.apply_operation(operation)
                
        finally:
            self.processing_operations = False
    
    def apply_operation(self, operation):
        """应用操作到白板"""
        # 检查冲突
        if self.has_conflict(operation):
            resolved_operation = self.resolve_conflict(operation)
            if resolved_operation:
                operation = resolved_operation
        
        # 添加到历史记录
        self.operation_history.append(operation)
        
        # 发送信号给白板组件
        self.operation_received.emit(operation.to_dict())
    
    def has_conflict(self, operation):
        """检查操作是否有冲突"""
        # 简单的冲突检测：检查是否有同时间的操作
        threshold = 0.1  # 100ms内的操作认为可能冲突
        
        for local_op in self.local_operations:
            if abs(local_op.timestamp - operation.timestamp) < threshold:
                if self.operations_conflict(local_op, operation):
                    return True
        return False
    
    def operations_conflict(self, op1, op2):
        """判断两个操作是否冲突"""
        # 简单的冲突判断：在相近位置的绘制操作
        if op1.type == 'draw' and op2.type == 'draw':
            data1, data2 = op1.data, op2.data
            if 'points' in data1 and 'points' in data2:
                # 检查绘制点是否重叠
                return self.points_overlap(data1['points'], data2['points'])
        return False
    
    def points_overlap(self, points1, points2, threshold=20):
        """检查两组点是否重叠"""
        for p1 in points1:
            for p2 in points2:
                distance = ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5
                if distance < threshold:
                    return True
        return False
    
    def resolve_conflict(self, operation):
        """解决操作冲突"""
        # 简单的冲突解决策略：时间戳较早的操作优先
        # 更复杂的策略可以考虑操作类型、用户权限等
        
        conflicting_ops = []
        for local_op in self.local_operations:
            if self.operations_conflict(local_op, operation):
                conflicting_ops.append(local_op)
        
        if conflicting_ops:
            # 选择时间戳最早的操作
            all_ops = conflicting_ops + [operation]
            all_ops.sort(key=lambda op: op.timestamp)
            winner = all_ops[0]
            
            # 如果远程操作获胜，返回它；否则忽略
            if winner.id == operation.id:
                self.conflict_resolved.emit({
                    'winner': winner.to_dict(),
                    'conflicts': [op.to_dict() for op in conflicting_ops]
                })
                return operation
        
        return None
    
    def add_user(self, user_id, user_name, user_type='student'):
        """添加协作用户"""
        if user_id not in self.active_users:
            color = self.color_palette[self.color_index % len(self.color_palette)]
            self.color_index += 1

            self.active_users[user_id] = {
                'name': user_name,
                'type': user_type,
                'color': color,
                'last_seen': time.time(),
                'joined_at': time.time()
            }
            self.user_colors[user_id] = color

            self.user_joined.emit(user_id, user_name)
            print(f"用户 {user_name} ({user_type}) 加入协同白板")

            return True

        return False
    
    def remove_user(self, user_id):
        """移除协作用户"""
        if user_id in self.active_users:
            del self.active_users[user_id]
            if user_id in self.user_colors:
                del self.user_colors[user_id]
            
            self.user_left.emit(user_id)
    
    def get_user_color(self, user_id):
        """获取用户颜色"""
        return self.user_colors.get(user_id, '#000000')
    
    def sync_operations(self):
        """定时同步操作"""
        # 清理过期的本地操作
        current_time = time.time()
        self.local_operations = [
            op for op in self.local_operations 
            if current_time - op.timestamp < 30  # 30秒后清理
        ]
        
        # 更新用户活跃状态
        for user_id in list(self.active_users.keys()):
            if current_time - self.active_users[user_id]['last_seen'] > 60:
                self.remove_user(user_id)
    
    def get_operation_history(self):
        """获取操作历史"""
        return [op.to_dict() for op in self.operation_history]
    
    def clear_whiteboard(self):
        """清空白板"""
        clear_operation = self.add_local_operation('clear', {})
        return clear_operation
    
    def undo_last_operation(self):
        """撤销最后一个操作"""
        if self.local_operations:
            last_op = self.local_operations[-1]
            undo_operation = self.add_local_operation('undo', {
                'target_operation_id': last_op.id
            })
            return undo_operation
        return None
