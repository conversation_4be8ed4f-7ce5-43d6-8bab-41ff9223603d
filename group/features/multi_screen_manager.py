# group/features/multi_screen_manager.py
import vlc
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QFrame, QLabel, QGridLayout
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont
import subprocess
import requests

class StudentStreamWidget(QFrame):
    """单个学员投屏显示组件"""
    def __init__(self, student_id, student_name, parent=None):
        super().__init__(parent)
        self.student_id = student_id
        self.student_name = student_name
        self.is_active = False
        
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(2)
        
        layout = QVBoxLayout(self)
        
        # 学员信息标签
        self.info_label = QLabel(f"{student_name} ({student_id})")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(self.info_label)
        
        # 视频显示区域
        self.video_widget = QFrame()
        self.video_widget.setStyleSheet("background-color: black;")
        layout.addWidget(self.video_widget, 1)
        
        # VLC播放器
        self.vlc_instance = vlc.Instance("--no-xlib")
        self.media_player = self.vlc_instance.media_player_new()
        self.media_player.set_xwindow(int(self.video_widget.winId()))
        
        # 状态标签
        self.status_label = QLabel("未连接")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: gray;")
        layout.addWidget(self.status_label)
    
    def start_stream(self, stream_url):
        """开始播放学员流"""
        try:
            media = self.vlc_instance.media_new(stream_url)
            media.add_option('network-caching=300')
            self.media_player.set_media(media)
            self.media_player.play()
            
            self.is_active = True
            self.status_label.setText("正在投屏")
            self.status_label.setStyleSheet("color: green;")
            self.setStyleSheet("border: 2px solid green;")
            
        except Exception as e:
            print(f"播放学员流失败: {e}")
            self.status_label.setText("连接失败")
            self.status_label.setStyleSheet("color: red;")
    
    def stop_stream(self):
        """停止播放学员流"""
        self.media_player.stop()
        self.is_active = False
        self.status_label.setText("未连接")
        self.status_label.setStyleSheet("color: gray;")
        self.setStyleSheet("border: 2px solid gray;")

class MultiScreenManager(QWidget):
    """多路投屏管理器"""
    student_stream_started = pyqtSignal(str, str)  # student_id, stream_url
    student_stream_stopped = pyqtSignal(str)  # student_id
    
    def __init__(self, rtsp_base_url, max_streams=6, parent=None):
        super().__init__(parent)
        self.rtsp_base_url = rtsp_base_url
        self.max_streams = max_streams
        self.active_streams = {}  # student_id -> StudentStreamWidget
        self.available_slots = list(range(max_streams))
        
        self.initUI()
        
        # 定时检查流状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.check_stream_status)
        self.status_timer.start(5000)  # 每5秒检查一次
    
    def initUI(self):
        """初始化UI"""
        self.setWindowTitle("多路投屏显示")
        
        # 创建网格布局，支持2x3或3x2布局
        self.grid_layout = QGridLayout(self)
        
        # 创建投屏位置
        self.stream_widgets = []
        rows = 2 if self.max_streams <= 6 else 3
        cols = 3 if self.max_streams <= 6 else 3
        
        for i in range(self.max_streams):
            row = i // cols
            col = i % cols
            
            widget = StudentStreamWidget(f"slot_{i}", f"位置 {i+1}")
            self.grid_layout.addWidget(widget, row, col)
            self.stream_widgets.append(widget)
    
    def add_student_stream(self, student_id, student_name, stream_name):
        """添加学员投屏"""
        if len(self.active_streams) >= self.max_streams:
            print(f"已达到最大投屏数量限制 ({self.max_streams})")
            return False
        
        if student_id in self.active_streams:
            print(f"学员 {student_id} 已在投屏中")
            return False
        
        # 分配空闲位置
        if not self.available_slots:
            print("没有可用的投屏位置")
            return False
        
        slot_index = self.available_slots.pop(0)
        widget = self.stream_widgets[slot_index]
        
        # 更新widget信息
        widget.student_id = student_id
        widget.student_name = student_name
        widget.info_label.setText(f"{student_name} ({student_id})")
        
        # 开始播放流
        stream_url = f"{self.rtsp_base_url}/{stream_name}"
        widget.start_stream(stream_url)
        
        self.active_streams[student_id] = {
            'widget': widget,
            'slot_index': slot_index,
            'stream_name': stream_name
        }
        
        self.student_stream_started.emit(student_id, stream_url)
        print(f"学员 {student_name} 开始投屏到位置 {slot_index + 1}")
        return True
    
    def remove_student_stream(self, student_id):
        """移除学员投屏"""
        if student_id not in self.active_streams:
            print(f"学员 {student_id} 未在投屏中")
            return False
        
        stream_info = self.active_streams[student_id]
        widget = stream_info['widget']
        slot_index = stream_info['slot_index']
        
        # 停止播放
        widget.stop_stream()
        
        # 重置widget
        widget.student_id = f"slot_{slot_index}"
        widget.student_name = f"位置 {slot_index + 1}"
        widget.info_label.setText(f"位置 {slot_index + 1}")
        
        # 释放位置
        self.available_slots.append(slot_index)
        self.available_slots.sort()
        
        del self.active_streams[student_id]
        
        self.student_stream_stopped.emit(student_id)
        print(f"学员 {student_id} 停止投屏")
        return True
    
    def get_active_streams(self):
        """获取当前活跃的投屏列表"""
        return list(self.active_streams.keys())
    
    def check_stream_status(self):
        """检查流状态"""
        for student_id, stream_info in list(self.active_streams.items()):
            widget = stream_info['widget']
            # 检查VLC播放器状态
            if widget.media_player.get_state() == vlc.State.Error:
                print(f"检测到学员 {student_id} 流异常，尝试重连")
                # 可以在这里实现重连逻辑
    
    def set_layout_mode(self, mode):
        """设置布局模式"""
        # 可以实现不同的布局模式：1画面、2画面、4画面、6画面等
        pass
    
    def closeEvent(self, event):
        """关闭事件"""
        # 停止所有流
        for student_id in list(self.active_streams.keys()):
            self.remove_student_stream(student_id)
        
        self.status_timer.stop()
        event.accept()
