# group/features/video_recorder.py
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QProgressBar, QComboBox, QSpinBox,
                             QFileDialog, QMessageBox, QFrame, QTextEdit)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QIcon
import subprocess
import os
import time
from datetime import datetime, timedelta
from pathlib import Path
import json

class RecordingThread(QThread):
    """录制线程"""
    recording_started = pyqtSignal()
    recording_stopped = pyqtSignal(str)  # 录制文件路径
    recording_error = pyqtSignal(str)    # 错误信息
    recording_progress = pyqtSignal(int) # 录制进度（秒）
    
    def __init__(self, output_path, screen_resolution="1920x1080", fps=30, audio_enabled=True):
        super().__init__()
        self.output_path = output_path
        self.screen_resolution = screen_resolution
        self.fps = fps
        self.audio_enabled = audio_enabled
        self.ffmpeg_process = None
        self.is_recording = False
        self.start_time = None
    
    def run(self):
        """开始录制"""
        try:
            self.start_time = time.time()
            self.is_recording = True
            
            # 构建FFmpeg命令
            cmd = self.build_ffmpeg_command()
            
            # 启动FFmpeg进程
            self.ffmpeg_process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            self.recording_started.emit()
            
            # 监控录制进度
            while self.is_recording and self.ffmpeg_process.poll() is None:
                elapsed_time = int(time.time() - self.start_time)
                self.recording_progress.emit(elapsed_time)
                self.msleep(1000)  # 每秒更新一次
            
            # 等待进程结束
            if self.ffmpeg_process:
                stdout, stderr = self.ffmpeg_process.communicate()
                
                if self.ffmpeg_process.returncode == 0:
                    self.recording_stopped.emit(self.output_path)
                else:
                    self.recording_error.emit(f"录制失败: {stderr}")
                    
        except Exception as e:
            self.recording_error.emit(f"录制异常: {str(e)}")
    
    def build_ffmpeg_command(self):
        """构建FFmpeg录制命令"""
        cmd = ['ffmpeg', '-y']  # -y 覆盖输出文件
        
        # 视频输入（屏幕录制）
        cmd.extend(['-f', 'x11grab'])
        cmd.extend(['-s', self.screen_resolution])
        cmd.extend(['-r', str(self.fps)])
        cmd.extend(['-i', ':0.0'])
        
        # 音频输入（如果启用）
        if self.audio_enabled:
            cmd.extend(['-f', 'pulse'])
            cmd.extend(['-i', 'default'])
        
        # 编码设置
        cmd.extend(['-c:v', 'libx264'])
        cmd.extend(['-preset', 'medium'])
        cmd.extend(['-crf', '23'])
        cmd.extend(['-pix_fmt', 'yuv420p'])
        
        if self.audio_enabled:
            cmd.extend(['-c:a', 'aac'])
            cmd.extend(['-b:a', '128k'])
        
        # 输出文件
        cmd.append(self.output_path)
        
        return cmd
    
    def stop_recording(self):
        """停止录制"""
        self.is_recording = False
        if self.ffmpeg_process and self.ffmpeg_process.poll() is None:
            # 发送SIGTERM信号优雅地停止FFmpeg
            self.ffmpeg_process.terminate()
            
            # 等待一段时间，如果还没结束就强制杀死
            try:
                self.ffmpeg_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ffmpeg_process.kill()

class VideoRecorderWidget(QWidget):
    """视频录制控制界面"""
    recording_started = pyqtSignal(str)  # 录制文件路径
    recording_stopped = pyqtSignal(str)  # 录制文件路径
    playback_started = pyqtSignal(str)   # 回放开始
    playback_stopped = pyqtSignal()      # 回放停止
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.recording_thread = None
        self.is_recording = False
        self.is_playing = False
        self.current_output_path = None
        self.recording_start_time = None
        self.player_process = None  # 视频播放进程
        self.recorded_videos = []   # 存储录制的视频列表
        
        # 录制设置
        self.default_save_dir = Path.home() / "Desktop" / "课堂录制"
        self.default_save_dir.mkdir(exist_ok=True)
        
        self.initUI()
        
        # 定时器用于更新UI
        self.ui_timer = QTimer()
        self.ui_timer.timeout.connect(self.update_ui)
        self.ui_timer.start(1000)  # 每秒更新
    
    def initUI(self):
        """初始化UI"""
        self.setWindowTitle("视频录制")
        self.setMinimumSize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("小组讨论录制")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # 录制设置区域
        settings_frame = QFrame()
        settings_frame.setFrameStyle(QFrame.Box)
        settings_layout = QVBoxLayout(settings_frame)
        
        # 分辨率设置
        resolution_layout = QHBoxLayout()
        resolution_layout.addWidget(QLabel("录制分辨率:"))
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems([
            "1920x1080", "1366x768", "1280x720", "1024x768"
        ])
        resolution_layout.addWidget(self.resolution_combo)
        resolution_layout.addStretch()
        settings_layout.addLayout(resolution_layout)
        
        # 帧率设置
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("帧率:"))
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(15, 60)
        self.fps_spinbox.setValue(30)
        self.fps_spinbox.setSuffix(" fps")
        fps_layout.addWidget(self.fps_spinbox)
        fps_layout.addStretch()
        settings_layout.addLayout(fps_layout)
        
        # 音频设置
        audio_layout = QHBoxLayout()
        self.audio_checkbox = QPushButton("音频录制: 开启")
        self.audio_checkbox.setCheckable(True)
        self.audio_checkbox.setChecked(True)
        self.audio_checkbox.clicked.connect(self.toggle_audio)
        audio_layout.addWidget(self.audio_checkbox)
        audio_layout.addStretch()
        settings_layout.addLayout(audio_layout)
        
        layout.addWidget(settings_frame)
        
        # 录制状态显示
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Box)
        status_layout = QVBoxLayout(status_frame)
        
        self.status_label = QLabel("准备录制")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 12))
        status_layout.addWidget(self.status_label)
        
        self.time_label = QLabel("00:00:00")
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.time_label.setStyleSheet("color: #d9534f;")
        status_layout.addWidget(self.time_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(status_frame)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始录制")
        self.start_btn.clicked.connect(self.start_recording)
        self.start_btn.setStyleSheet("QPushButton { background-color: #5cb85c; color: white; font-weight: bold; }")
        
        self.stop_btn = QPushButton("停止录制")
        self.stop_btn.clicked.connect(self.stop_recording)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("QPushButton { background-color: #d9534f; color: white; font-weight: bold; }")
        
        self.pause_btn = QPushButton("暂停录制")
        self.pause_btn.clicked.connect(self.pause_recording)
        self.pause_btn.setEnabled(False)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.pause_btn)
        button_layout.addWidget(self.stop_btn)

        layout.addLayout(button_layout)

        # 回放和导出按钮
        playback_layout = QHBoxLayout()

        self.play_btn = QPushButton("回放录制")
        self.play_btn.clicked.connect(self.play_recording)
        self.play_btn.setEnabled(False)
        self.play_btn.setStyleSheet("QPushButton { background-color: #337ab7; color: white; font-weight: bold; }")

        self.stop_play_btn = QPushButton("停止回放")
        self.stop_play_btn.clicked.connect(self.stop_playback)
        self.stop_play_btn.setEnabled(False)

        self.export_btn = QPushButton("导出视频")
        self.export_btn.clicked.connect(self.export_video)
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("QPushButton { background-color: #f0ad4e; color: white; font-weight: bold; }")

        playback_layout.addWidget(self.play_btn)
        playback_layout.addWidget(self.stop_play_btn)
        playback_layout.addWidget(self.export_btn)

        layout.addLayout(playback_layout)
        
        # 文件管理
        file_layout = QHBoxLayout()
        
        self.open_folder_btn = QPushButton("打开录制文件夹")
        self.open_folder_btn.clicked.connect(self.open_recording_folder)
        
        self.set_save_path_btn = QPushButton("设置保存路径")
        self.set_save_path_btn.clicked.connect(self.set_save_path)
        
        file_layout.addWidget(self.open_folder_btn)
        file_layout.addWidget(self.set_save_path_btn)
        
        layout.addLayout(file_layout)
        
        # 录制日志
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setPlaceholderText("录制日志...")
        layout.addWidget(self.log_text)
    
    def toggle_audio(self):
        """切换音频录制"""
        if self.audio_checkbox.isChecked():
            self.audio_checkbox.setText("音频录制: 开启")
        else:
            self.audio_checkbox.setText("音频录制: 关闭")
    
    def start_recording(self):
        """开始录制"""
        if self.is_recording:
            return
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"group_discussion_{timestamp}.mp4"
        self.current_output_path = str(self.default_save_dir / filename)
        
        # 获取录制设置
        resolution = self.resolution_combo.currentText()
        fps = self.fps_spinbox.value()
        audio_enabled = self.audio_checkbox.isChecked()
        
        # 创建录制线程
        self.recording_thread = RecordingThread(
            self.current_output_path, resolution, fps, audio_enabled
        )
        
        # 连接信号
        self.recording_thread.recording_started.connect(self.on_recording_started)
        self.recording_thread.recording_stopped.connect(self.on_recording_stopped)
        self.recording_thread.recording_error.connect(self.on_recording_error)
        self.recording_thread.recording_progress.connect(self.on_recording_progress)
        
        # 开始录制
        self.recording_thread.start()
        
        self.log_message(f"开始录制: {filename}")
    
    def stop_recording(self):
        """停止录制"""
        if not self.is_recording or not self.recording_thread:
            return
        
        self.recording_thread.stop_recording()
        self.log_message("正在停止录制...")
    
    def pause_recording(self):
        """暂停录制（暂未实现）"""
        QMessageBox.information(self, "提示", "暂停功能暂未实现")
    
    def on_recording_started(self):
        """录制开始回调"""
        self.is_recording = True
        self.recording_start_time = time.time()
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.pause_btn.setEnabled(True)
        
        self.status_label.setText("正在录制...")
        self.status_label.setStyleSheet("color: #d9534f;")
        
        self.recording_started.emit(self.current_output_path)
    
    def on_recording_stopped(self, output_path):
        """录制停止回调"""
        self.is_recording = False
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)

        # 启用回放和导出按钮
        self.play_btn.setEnabled(True)
        self.export_btn.setEnabled(True)

        self.status_label.setText("录制完成")
        self.status_label.setStyleSheet("color: #5cb85c;")
        
        # 显示文件信息
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            size_mb = file_size / (1024 * 1024)
            self.log_message(f"录制完成: {os.path.basename(output_path)} ({size_mb:.1f} MB)")
            
            # 询问是否打开文件
            reply = QMessageBox.question(
                self, "录制完成", 
                f"录制完成！\n文件: {os.path.basename(output_path)}\n大小: {size_mb:.1f} MB\n\n是否打开录制文件？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.open_video_file(output_path)
        
        self.recording_stopped.emit(output_path)
    
    def on_recording_error(self, error_message):
        """录制错误回调"""
        self.is_recording = False
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        
        self.status_label.setText("录制失败")
        self.status_label.setStyleSheet("color: #d9534f;")
        
        self.log_message(f"录制错误: {error_message}")
        QMessageBox.critical(self, "录制错误", error_message)
    
    def on_recording_progress(self, elapsed_seconds):
        """录制进度回调"""
        # 更新时间显示在update_ui中处理
        pass
    
    def update_ui(self):
        """更新UI显示"""
        if self.is_recording and self.recording_start_time:
            elapsed = int(time.time() - self.recording_start_time)
            hours = elapsed // 3600
            minutes = (elapsed % 3600) // 60
            seconds = elapsed % 60
            self.time_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")
    
    def set_save_path(self):
        """设置保存路径"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择录制文件保存文件夹", str(self.default_save_dir)
        )
        
        if folder:
            self.default_save_dir = Path(folder)
            self.log_message(f"保存路径设置为: {folder}")
    
    def open_recording_folder(self):
        """打开录制文件夹"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(str(self.default_save_dir))
            elif os.name == 'posix':  # Linux/Mac
                subprocess.run(['xdg-open', str(self.default_save_dir)])
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开文件夹: {e}")
    
    def open_video_file(self, file_path):
        """打开视频文件"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(file_path)
            elif os.name == 'posix':  # Linux/Mac
                subprocess.run(['xdg-open', file_path])
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开视频文件: {e}")
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.is_recording:
            reply = QMessageBox.question(
                self, "确认", "正在录制中，确定要关闭吗？录制将被停止。",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.stop_recording()
                if self.recording_thread:
                    self.recording_thread.wait(5000)  # 等待5秒
                event.accept()
            else:
                event.ignore()
        else:
            # 停止回放
            if self.is_playing:
                self.stop_playback()
            event.accept()

    def play_recording(self):
        """回放最新的录制视频"""
        if not self.current_output_path or not os.path.exists(self.current_output_path):
            QMessageBox.warning(self, "警告", "没有找到录制文件")
            return

        try:
            # 使用系统默认视频播放器播放
            if os.name == 'nt':  # Windows
                self.player_process = subprocess.Popen(['start', '', self.current_output_path], shell=True)
            elif os.name == 'posix':  # Linux/Mac
                self.player_process = subprocess.Popen(['xdg-open', self.current_output_path])

            self.is_playing = True
            self.play_btn.setEnabled(False)
            self.stop_play_btn.setEnabled(True)

            self.playback_started.emit(self.current_output_path)
            self.log_message(f"开始回放: {os.path.basename(self.current_output_path)}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"回放失败: {e}")
            self.log_message(f"回放失败: {e}")

    def stop_playback(self):
        """停止回放"""
        try:
            if self.player_process:
                self.player_process.terminate()
                self.player_process = None

            self.is_playing = False
            self.play_btn.setEnabled(True)
            self.stop_play_btn.setEnabled(False)

            self.playback_stopped.emit()
            self.log_message("回放已停止")

        except Exception as e:
            self.log_message(f"停止回放失败: {e}")

    def export_video(self):
        """导出视频到指定位置"""
        if not self.current_output_path or not os.path.exists(self.current_output_path):
            QMessageBox.warning(self, "警告", "没有找到录制文件")
            return

        # 选择导出路径
        export_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出视频",
            f"讨论录制_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4",
            "视频文件 (*.mp4);;所有文件 (*)"
        )

        if export_path:
            try:
                # 复制文件到导出路径
                import shutil
                shutil.copy2(self.current_output_path, export_path)

                QMessageBox.information(self, "成功", f"视频已导出到:\n{export_path}")
                self.log_message(f"视频已导出到: {export_path}")

                # 询问是否打开导出文件夹
                reply = QMessageBox.question(
                    self, "打开文件夹", "是否打开导出文件夹？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    export_dir = os.path.dirname(export_path)
                    if os.name == 'nt':  # Windows
                        os.startfile(export_dir)
                    elif os.name == 'posix':  # Linux/Mac
                        subprocess.run(['xdg-open', export_dir])

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {e}")
                self.log_message(f"导出失败: {e}")

    def get_recorded_videos(self):
        """获取所有录制的视频列表"""
        videos = []
        if self.default_save_dir.exists():
            for file_path in self.default_save_dir.glob("*.mp4"):
                if file_path.is_file():
                    stat = file_path.stat()
                    videos.append({
                        'path': str(file_path),
                        'name': file_path.name,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime),
                        'modified': datetime.fromtimestamp(stat.st_mtime)
                    })

        # 按修改时间排序，最新的在前
        videos.sort(key=lambda x: x['modified'], reverse=True)
        return videos

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def update_recorded_videos_list(self):
        """更新录制视频列表"""
        self.recorded_videos = self.get_recorded_videos()

        # 如果有录制文件，启用回放和导出按钮
        if self.recorded_videos and not self.is_recording:
            self.play_btn.setEnabled(True)
            self.export_btn.setEnabled(True)
        else:
            self.play_btn.setEnabled(False)
            self.export_btn.setEnabled(False)
