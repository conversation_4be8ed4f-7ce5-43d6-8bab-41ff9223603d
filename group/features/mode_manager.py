# group/features/mode_manager.py
from PyQt5.QtCore import QObject, pyqtSignal
from enum import Enum

class DiscussionMode(Enum):
    TEACHING = "teaching"  # 授课研讨模式
    AUTONOMOUS = "autonomous"  # 自主研讨模式

class ModeManager(QObject):
    mode_changed = pyqtSignal(str)  # 模式变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_mode = DiscussionMode.TEACHING
        self.is_teacher_connected = False
        
    def set_mode(self, mode: DiscussionMode):
        """设置讨论模式"""
        if self.current_mode != mode:
            self.current_mode = mode
            self.mode_changed.emit(mode.value)
            print(f"模式切换为: {mode.value}")
    
    def get_mode(self):
        """获取当前模式"""
        return self.current_mode
    
    def set_teacher_connection_status(self, connected: bool):
        """设置教师端连接状态"""
        self.is_teacher_connected = connected
        if not connected and self.current_mode == DiscussionMode.TEACHING:
            # 教师端断开连接时，自动切换到自主研讨模式
            self.set_mode(DiscussionMode.AUTONOMOUS)
    
    def can_use_feature(self, feature_name: str) -> bool:
        """检查当前模式下是否可以使用某个功能"""
        teaching_only_features = [
            "teacher_broadcast_receive",
            "teacher_control_receive"
        ]
        
        autonomous_only_features = [
            "independent_discussion",
            "local_file_sharing"
        ]
        
        if self.current_mode == DiscussionMode.TEACHING:
            return feature_name not in autonomous_only_features
        else:
            return feature_name not in teaching_only_features
