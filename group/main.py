# group/main.py
import sys
# 在导入vlc之前，为打包后的应用显式设置VLC插件路径
import os
os.environ['VLC_PLUGIN_PATH'] = '/usr/lib/x86_64-linux-gnu/vlc/plugins'
import vlc
import subprocess
import requests
import os
from pathlib import Path
import configparser
import asyncio
import threading
from aiortc import RTCPeerConnection, RTCSessionDescription
from aiortc.sdp import candidate_from_sdp
from av import VideoFrame
import numpy as np

from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QFrame, QMessageBox, QLabel, QTabWidget,
                             QMenuBar, QMenu, QAction, QStatusBar, QMainWindow,
                             QSystemTrayIcon, QStyle)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QImage, QPixmap

from network.udp_broadcaster import UdpBroadcaster
from network.socket_manager import SocketIOManager
from features.touch_handler import TouchHandler
from features.power_handler import PowerHandler
from features.mode_manager import ModeManager, DiscussionMode
from features.multi_screen_manager import MultiScreenManager
from features.collaborative_whiteboard import CollaborativeWhiteboardManager
from features.artwork_display import ArtworkDisplayWidget
from features.file_sharing import FileSharingWidget
from features.video_recorder import VideoRecorderWidget
from features.discussion_manager import DiscussionManagerWidget
from ui.whiteboard import Whiteboard

# 读取配置
config = configparser.ConfigParser()
# 打包后配置文件的标准路径
# prod_config_path = '/etc/group_app/config.ini'
# 开发时使用的本地配置文件路径
dev_config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.ini')

# if os.path.exists(prod_config_path):
#     config.read(prod_config_path, encoding='utf-8')
# else:
#     config.read(dev_config_path, encoding='utf-8')

network_config = config['Network']


class GroupApp(QWidget):
    new_video_frame_signal = pyqtSignal(QImage)

    def __init__(self):
        super().__init__()
        # 从配置初始化
        server_host = network_config.get('SERVER_HOST')
        server_port = network_config.getint('SERVER_PORT')
        rtsp_host = network_config.get('RTSP_HOST')
        rtsp_port = network_config.getint('RTSP_PORT')
        discovery_port = network_config.getint('DISCOVERY_PORT')

        self.rtsp_base_url = f"rtsp://{rtsp_host}:{rtsp_port}"
        self.server_url = f"https://{server_host}:{server_port}"
        self.ffmpeg_process = None

        # --- 网络和身份 ---
        self.broadcaster = UdpBroadcaster(broadcast_port=discovery_port)
        self.ip_address = self.broadcaster.ip_address
        self.hostname = self.broadcaster.hostname
        self.stream_name = f"group_{self.ip_address.replace('.', '_')}"
        self.help_requested = False

        # --- 定时器 ---
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_timer_display)
        self.remaining_seconds = 0

        # --- WebRTC ---
        self.webrtc_pc = None
        self.webrtc_loop = asyncio.new_event_loop()
        self.webrtc_thread = threading.Thread(target=self.start_webrtc_event_loop, daemon=True)
        self.webrtc_thread.start()

        # --- 服务和处理器 ---
        self.sio_manager = SocketIOManager(self.server_url, self.ip_address, self.hostname)
        self.touch_handler = TouchHandler(
            parent=self,
            socket_manager=self.sio_manager,
            group_id=self.sio_manager.group_id
        )
        self.power_handler = PowerHandler()

        # --- 新功能模块 ---
        self.mode_manager = ModeManager(self)
        self.multi_screen_manager = MultiScreenManager(self.rtsp_base_url, max_streams=6, parent=self)
        self.collaborative_whiteboard = CollaborativeWhiteboardManager(
            self.sio_manager, self.sio_manager.group_id, self.ip_address, self.hostname, self
        )
        self.artwork_display = ArtworkDisplayWidget(self)
        self.file_sharing = FileSharingWidget(self.hostname, self.sio_manager.group_id, self)
        self.video_recorder = VideoRecorderWidget()
        self.discussion_manager = DiscussionManagerWidget(self.sio_manager, self)

        # --- UI初始化 ---
        self.whiteboard = Whiteboard(sio_manager=self.sio_manager, course_id=None)
        self.whiteboard.hide()
        self.initUI()

        # --- 信号连接 ---
        self.connect_signals()
        self.whiteboard.drawing_event.connect(self.on_group_drawing_event)

        # --- 设置系统托盘 ---
        self.setup_system_tray()

        # --- 启动服务 ---
        self.broadcaster.start()
        self.sio_manager.start()

        # 默认播放教师流
        self.play_stream("teacher")

    def start_webrtc_event_loop(self):
        """在一个独立的线程中运行asyncio事件循环。"""
        asyncio.set_event_loop(self.webrtc_loop)
        self.webrtc_loop.run_forever()

    def connect_signals(self):
        """连接所有Qt信号和槽。"""
        # 基础信号连接
        self.sio_manager.new_broadcast_signal.connect(self.play_stream)
        self.sio_manager.new_file_signal.connect(self.handle_new_file)
        self.sio_manager.timer_signal.connect(self.handle_timer_event)
        self.sio_manager.topic_signal.connect(self.handle_new_topic)
        self.sio_manager.message_from_student_signal.connect(self.handle_student_message)
        self.sio_manager.webrtc_offer_signal.connect(self.handle_webrtc_offer)
        self.sio_manager.webrtc_ice_candidate_signal.connect(self.handle_webrtc_ice_candidate)
        self.new_video_frame_signal.connect(self.update_video_frame)

        # 触控回传信号连接
        self.sio_manager.touch_control_command_signal.connect(self.touch_handler.handle_touch_control_command)
        self.sio_manager.touch_control_event_signal.connect(self.touch_handler.handle_touch_control_event)
        self.sio_manager.shutdown_command_signal.connect(self.power_handler.shutdown)

        # 白板信号连接
        self.sio_manager.teacher_drawing_signal.connect(self.handle_teacher_drawing_event)
        self.sio_manager.collaboration_started_signal.connect(self.handle_collaboration_started)
        self.sio_manager.collaboration_stopped_signal.connect(self.handle_collaboration_stopped)
        self.sio_manager.course_joined_signal.connect(self.on_course_joined)

        # 模式管理信号连接
        self.mode_manager.mode_changed.connect(self.on_mode_changed)

        # 多路投屏信号连接
        self.multi_screen_manager.student_stream_started.connect(self.on_student_stream_started)
        self.multi_screen_manager.student_stream_stopped.connect(self.on_student_stream_stopped)

        # 协同白板信号连接
        self.collaborative_whiteboard.operation_received.connect(self.on_whiteboard_operation_received)
        self.collaborative_whiteboard.user_joined.connect(self.on_whiteboard_user_joined)
        self.collaborative_whiteboard.user_left.connect(self.on_whiteboard_user_left)

        # 文件分享信号连接
        self.file_sharing.file_shared.connect(self.on_file_shared)
        self.file_sharing.file_requested.connect(self.on_file_requested)

        # 视频录制信号连接
        self.video_recorder.recording_started.connect(self.on_recording_started)
        self.video_recorder.recording_stopped.connect(self.on_recording_stopped)

        # 学生端交互信号连接
        self.sio_manager.student_joined_signal.connect(self.handle_student_joined)
        self.sio_manager.student_left_signal.connect(self.handle_student_left)
        self.sio_manager.screen_share_request_signal.connect(self.handle_screen_share_request)
        self.sio_manager.screen_share_stopped_signal.connect(self.handle_screen_share_stopped)
        self.sio_manager.whiteboard_user_joined_signal.connect(self.handle_whiteboard_user_joined)
        self.sio_manager.whiteboard_operation_signal.connect(self.handle_whiteboard_operation)
        self.sio_manager.file_shared_signal.connect(self.handle_file_shared)
        self.sio_manager.artwork_shared_signal.connect(self.handle_artwork_shared)

    def initUI(self):
        """初始化用户界面。"""
        self.setWindowTitle(f'小组端 - {self.hostname} ({self.ip_address})')
        self.setGeometry(100, 100, 1280, 720)

        # 创建菜单栏
        self.create_menu_bar()

        # 创建主布局
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        # 顶部信息栏
        top_bar_layout = QHBoxLayout()

        # 模式显示
        self.mode_label = QLabel("授课研讨模式")
        self.mode_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.mode_label.setStyleSheet("color: #2196F3; padding: 5px;")

        self.topic_label = QLabel("当前无讨论主题")
        self.topic_label.setFont(QFont("Arial", 14))
        self.topic_label.setAlignment(Qt.AlignCenter)

        self.timer_label = QLabel("00:00")
        self.timer_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.timer_label.setStyleSheet("color: #d9534f;")
        self.timer_label.setFixedWidth(150)
        self.timer_label.setAlignment(Qt.AlignCenter)
        self.timer_label.setVisible(False)

        top_bar_layout.addWidget(self.mode_label)
        top_bar_layout.addWidget(self.topic_label)
        top_bar_layout.addStretch()
        top_bar_layout.addWidget(self.timer_label)
        self.main_layout.addLayout(top_bar_layout)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 主视频标签页
        self.main_tab = QWidget()
        self.setup_main_tab()
        self.tab_widget.addTab(self.main_tab, "主屏幕")

        # 多路投屏标签页
        self.tab_widget.addTab(self.multi_screen_manager, "多路投屏")

        # 作品展示标签页
        self.tab_widget.addTab(self.artwork_display, "作品展示")

        # 资料分享标签页
        self.tab_widget.addTab(self.file_sharing, "资料分享")

        # 讨论管理标签页
        self.tab_widget.addTab(self.discussion_manager, "讨论管理")

        self.main_layout.addWidget(self.tab_widget, 1)

        # 底部按钮
        self.create_bottom_buttons()

        self.setLayout(self.main_layout)

        # VLC播放器实例
        self.vlc_instance = vlc.Instance("--no-xlib")
        self.media_player = self.vlc_instance.media_player_new()
        # 在setup_main_tab之后设置video widget
        self.media_player.set_xwindow(int(self.vlc_video_widget.winId()))

    def create_menu_bar(self):
        """创建菜单栏"""
        # 这里可以添加菜单栏，但QWidget不直接支持菜单栏
        # 如果需要菜单栏，可以考虑使用QMainWindow
        pass

    def setup_main_tab(self):
        """设置主标签页"""
        layout = QVBoxLayout(self.main_tab)
        layout.setContentsMargins(0, 0, 0, 0)

        # 视频显示区域
        self.video_container = QFrame()
        video_layout = QVBoxLayout(self.video_container)
        video_layout.setContentsMargins(0, 0, 0, 0)
        self.vlc_video_widget = QFrame()
        self.webrtc_video_label = QLabel()
        self.webrtc_video_label.setAlignment(Qt.AlignCenter)
        self.webrtc_video_label.setStyleSheet("background-color: black;")
        video_layout.addWidget(self.vlc_video_widget)
        video_layout.addWidget(self.webrtc_video_label)
        layout.addWidget(self.video_container, 1)

    def create_bottom_buttons(self):
        """创建底部按钮"""
        buttons_layout = QHBoxLayout()

        # 基础功能按钮
        self.share_screen_btn = QPushButton("分享屏幕")
        self.share_screen_btn.clicked.connect(self.toggle_screen_sharing)

        self.help_btn = QPushButton("举手求助")
        self.help_btn.clicked.connect(self.toggle_help_request)

        self.whiteboard_btn = QPushButton("打开白板")
        self.whiteboard_btn.clicked.connect(self.open_whiteboard)

        # 新功能按钮
        self.mode_btn = QPushButton("切换模式")
        self.mode_btn.clicked.connect(self.toggle_mode)

        self.record_btn = QPushButton("录制讨论")
        self.record_btn.clicked.connect(self.open_video_recorder)

        # self.discussion_btn = QPushButton("讨论管理")
        # self.discussion_btn.clicked.connect(self.open_discussion_manager)

        buttons_layout.addWidget(self.share_screen_btn)
        buttons_layout.addWidget(self.help_btn)
        buttons_layout.addWidget(self.whiteboard_btn)
        buttons_layout.addWidget(self.mode_btn)
        buttons_layout.addWidget(self.record_btn)
        # buttons_layout.addWidget(self.discussion_btn)

        self.main_layout.addLayout(buttons_layout)

    def open_whiteboard(self):
        """打开或关闭一个独立的白板窗口"""
        if self.whiteboard.isVisible():
            self.whiteboard.hide()
            self.whiteboard_btn.setText("打开白板")
        else:
            self.touch_handler.set_whiteboard_window(self.whiteboard)
            self.whiteboard.show()
            self.whiteboard.raise_()
            self.whiteboard.activateWindow()
            self.whiteboard_btn.setText("关闭白板")

    def toggle_mode(self):
        """切换讨论模式"""
        current_mode = self.mode_manager.get_mode()
        if current_mode == DiscussionMode.TEACHING:
            self.mode_manager.set_mode(DiscussionMode.AUTONOMOUS)
        else:
            self.mode_manager.set_mode(DiscussionMode.TEACHING)

    def open_video_recorder(self):
        """打开视频录制窗口"""
        if not hasattr(self, '_video_recorder_window') or not self.video_recorder.isVisible():
            self.video_recorder.show()
            self.video_recorder.raise_()
            self.video_recorder.activateWindow()
            self.record_btn.setText("录制中...")
        else:
            self.video_recorder.hide()
            self.record_btn.setText("录制讨论")

    # def open_discussion_manager(self):
    #     """打开讨论管理窗口"""
    #     if not self.discussion_manager.isVisible():
    #         self.discussion_manager.show()
    #         self.discussion_manager.raise_()
    #         self.discussion_manager.activateWindow()
    #         self.discussion_btn.setText("关闭讨论")
    #     else:
    #         self.discussion_manager.hide()
    #         self.discussion_btn.setText("讨论管理")

    # --- 信号处理器 ---

    def handle_student_message(self, message):
        QMessageBox.information(self, "收到学员消息", message)

    def handle_new_file(self, data):
        filename = data.get('filename')
        file_url_path = data.get('url')
        if not filename or not file_url_path:
            return
        download_url = f"{self.server_url}{file_url_path}"
        try:
            save_dir = Path.home() / "Desktop" / "课堂接收文件"
            save_dir.mkdir(exist_ok=True)
            save_path = save_dir / filename
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            QMessageBox.information(self, "收到新文件", f"文件 '{filename}' 已保存到您的桌面 '课堂接收文件' 文件夹中。")
        except Exception as e:
            QMessageBox.warning(self, "下载失败", f"无法下载文件 '{filename}'。错误: {e}")

    def handle_timer_event(self, data):
        action = data.get('action')
        if action == 'start':
            self.remaining_seconds = int(data.get('duration', 0))
            self.timer_label.setVisible(True)
            self.update_timer_display()
            self.timer.start(1000)
        elif action == 'stop':
            self.timer.stop()
            self.timer_label.setVisible(False)

    def update_timer_display(self):
        if self.remaining_seconds > 0:
            mins, secs = divmod(self.remaining_seconds, 60)
            self.timer_label.setText(f"{mins:02d}:{secs:02d}")
            self.remaining_seconds -= 1
        else:
            self.timer.stop()
            self.timer_label.setVisible(False)
            QMessageBox.information(self, "时间到", "计时结束！")

    def handle_new_topic(self, topic):
        self.topic_label.setText(f"讨论主题: {topic}")

    def toggle_help_request(self):
        self.help_requested = not self.help_requested
        if self.help_requested:
            self.help_btn.setText("取消求助")
            self.sio_manager.send_event('request_help', {'ip': self.ip_address, 'hostname': self.hostname})
        else:
            self.help_btn.setText("举手求助")
            self.sio_manager.send_event('cancel_help', {'ip': self.ip_address, 'hostname': self.hostname})

    # --- WebRTC 核心逻辑 ---

    def handle_webrtc_offer(self, data):
        """在asyncio线程中处理WebRTC offer。"""
        asyncio.run_coroutine_threadsafe(self._process_webrtc_offer(data), self.webrtc_loop)

    async def _process_webrtc_offer(self, data):
        """异步处理offer，创建answer并设置轨道处理器。"""
        if self.webrtc_pc:
            await self.webrtc_pc.close()

        offer = RTCSessionDescription(sdp=data['offer']['sdp'], type=data['offer']['type'])
        student_sid = data['student_sid']
        
        self.webrtc_pc = RTCPeerConnection()

        @self.webrtc_pc.on("iceconnectionstatechange")
        async def on_iceconnectionstatechange():
            print(f"ICE 连接状态: {self.webrtc_pc.iceConnectionState}")

        @self.webrtc_pc.on("track")
        async def on_track(track):
            print(f"接收到轨道: {track.kind}")
            if track.kind == "video":
                self.vlc_video_widget.hide()
                self.webrtc_video_label.show()
                self.media_player.stop()
                while True:
                    try:
                        frame = await track.recv()
                        img = frame.to_ndarray(format="bgr24")
                        q_img = QImage(img.data, img.shape[1], img.shape[0], img.strides[0], QImage.Format_BGR888)
                        self.new_video_frame_signal.emit(q_img.copy())
                    except Exception as e:
                        print(f"处理视频帧时出错: {e}")
                        break
        
        @self.webrtc_pc.on("icecandidate")
        async def on_icecandidate(candidate):
            if candidate:
                self.socket_manager.send_event('webrtc_ice_candidate', {
                    'target_sid': student_sid,
                    'candidate': {
                        'candidate': candidate.candidate,
                        'sdpMid': candidate.sdpMid,
                        'sdpMLineIndex': candidate.sdpMLineIndex,
                    }
                })

        await self.webrtc_pc.setRemoteDescription(offer)
        answer = await self.webrtc_pc.createAnswer()
        await self.webrtc_pc.setLocalDescription(answer)

        self.socket_manager.send_event('webrtc_answer', {
            'student_sid': student_sid,
            'answer': {'sdp': self.webrtc_pc.localDescription.sdp, 'type': self.webrtc_pc.localDescription.type}
        })
        print("已发送WebRTC Answer")

    def handle_webrtc_ice_candidate(self, data):
        """在asyncio线程中处理收到的ICE candidate。"""
        if self.webrtc_pc:
            candidate_data = data.get('candidate')
            if candidate_data and candidate_data.get('candidate'):
                try:
                    candidate = candidate_from_sdp(candidate_data['candidate'])
                    candidate.sdpMid = candidate_data['sdpMid']
                    candidate.sdpMLineIndex = candidate_data['sdpMLineIndex']
                    asyncio.run_coroutine_threadsafe(self.webrtc_pc.addIceCandidate(candidate), self.webrtc_loop)
                except Exception as e:
                    print(f"添加ICE candidate失败: {e}")

    def update_video_frame(self, image):
        """在主线程中更新视频帧。"""
        pixmap = QPixmap.fromImage(image)
        self.webrtc_video_label.setPixmap(pixmap.scaled(
            self.webrtc_video_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))

    # --- 流媒体和屏幕分享控制 ---

    def play_stream(self, stream_name):
        """播放指定的RTSP流。"""
        if self.webrtc_pc:
            asyncio.run_coroutine_threadsafe(self.webrtc_pc.close(), self.webrtc_loop)
            self.webrtc_pc = None
        
        self.webrtc_video_label.hide()
        self.vlc_video_widget.show()
        self.media_player.stop()

        media = self.vlc_instance.media_new(f"{self.rtsp_base_url}/{stream_name}")
        media.add_option('network-caching=300')
        self.media_player.set_media(media)
        self.media_player.play()
        print(f"正在播放RTSP流: {stream_name}")

    def toggle_screen_sharing(self):
        if self.ffmpeg_process:
            self.stop_screen_sharing()
        else:
            self.start_screen_sharing()

    def get_screen_resolution(self):
        try:
            cmd = "xrandr | grep '*'"
            process = subprocess.run(cmd, shell=True, check=True, stdout=subprocess.PIPE, text=True)
            return process.stdout.split()[0]
        except:
            return "1920x1080"

    def start_screen_sharing(self):
        resolution = self.get_screen_resolution()
        command = ['ffmpeg', '-f', 'x11grab', '-s', resolution, '-i', ':0.0',
                   '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
                   '-pix_fmt', 'yuv420p', '-f', 'rtsp', f'{self.rtsp_base_url}/{self.stream_name}']
        try:
            self.ffmpeg_process = subprocess.Popen(command)
            self.share_screen_btn.setText("停止分享")
            self.broadcaster.set_sharing_status(True)
        except Exception as e:
            print(f"启动分享失败: {e}")
            self.ffmpeg_process = None

    def stop_screen_sharing(self):
        if self.ffmpeg_process:
            self.ffmpeg_process.terminate()
            self.ffmpeg_process.wait()
            self.ffmpeg_process = None
        self.share_screen_btn.setText("分享屏幕")
        self.broadcaster.set_sharing_status(False)

    def closeEvent(self, event):
        """关闭窗口时清理所有资源。"""
        self.stop_screen_sharing()
        self.media_player.stop()
        self.broadcaster.stop()
        self.sio_manager.stop()
        
        if self.webrtc_loop.is_running():
            if self.webrtc_pc:
                future = asyncio.run_coroutine_threadsafe(self.webrtc_pc.close(), self.webrtc_loop)
                try:
                    future.result(timeout=5)
                except Exception as e:
                    print(f"关闭WebRTC连接时出错: {e}")
            self.webrtc_loop.call_soon_threadsafe(self.webrtc_loop.stop)
        
        self.webrtc_thread.join(timeout=5)
        event.accept()
    def on_group_drawing_event(self, data):
        """处理本小组白板的绘图事件，并转发给教师端"""
        if self.sio_manager and self.sio_manager.is_connected():
            data['hostname'] = self.hostname
            self.sio_manager.send_event('group_drawing_event', data)

    def on_course_joined(self, course_id):
        """处理加入课程事件，设置白板的course_id"""
        self.whiteboard.course_id = course_id
        print(f"GroupApp: Whiteboard course_id set to {course_id}")

    def handle_teacher_drawing_event(self, data):
        """处理教师端绘图事件"""
        try:
            if self.whiteboard:
                self.whiteboard.handle_remote_drawing_event(data)
        except Exception as e:
            print(f"GroupApp: Error handling teacher drawing event: {e}")

    def handle_collaboration_started(self):
        """处理协作模式开启事件"""
        try:
            if self.whiteboard:
                self.whiteboard.enable_collaboration()
                print("GroupApp: Collaboration started")
        except Exception as e:
            print(f"GroupApp: Error handling collaboration started: {e}")

    def handle_collaboration_stopped(self):
        """处理协作模式停止事件"""
        try:
            if self.whiteboard:
                self.whiteboard.disable_collaboration()
                print("GroupApp: Collaboration stopped")
        except Exception as e:
            print(f"GroupApp: Error handling collaboration stopped: {e}")

    def on_mode_changed(self, mode):
        """处理模式变化"""
        if mode == "teaching":
            self.mode_label.setText("授课研讨模式")
            self.mode_label.setStyleSheet("color: #2196F3; padding: 5px;")
            self.mode_btn.setText("切换到自主模式")
        else:
            self.mode_label.setText("自主研讨模式")
            self.mode_label.setStyleSheet("color: #FF9800; padding: 5px;")
            self.mode_btn.setText("切换到授课模式")

    def on_student_stream_started(self, student_id, stream_url):
        """处理学员投屏开始"""
        print(f"学员 {student_id} 开始投屏: {stream_url}")

    def on_student_stream_stopped(self, student_id):
        """处理学员投屏停止"""
        print(f"学员 {student_id} 停止投屏")

    def on_whiteboard_operation_received(self, operation_data):
        """处理协同白板操作"""
        print(f"收到协同白板操作: {operation_data}")

    def on_whiteboard_user_joined(self, user_id, user_name):
        """处理用户加入协同白板"""
        print(f"用户 {user_name} 加入协同白板")

    def on_whiteboard_user_left(self, user_id):
        """处理用户离开协同白板"""
        print(f"用户 {user_id} 离开协同白板")

    def on_file_shared(self, file_path, shared_by):
        """处理文件分享"""
        print(f"文件 {file_path} 被 {shared_by} 分享")

    def on_file_requested(self, file_path):
        """处理文件请求"""
        print(f"请求文件: {file_path}")

    def on_recording_started(self, output_path):
        """处理录制开始"""
        self.record_btn.setText("停止录制")
        self.record_btn.setStyleSheet("QPushButton { background-color: #d9534f; color: white; }")
        print(f"开始录制: {output_path}")

    def on_recording_stopped(self, output_path):
        """处理录制停止"""
        self.record_btn.setText("录制讨论")
        self.record_btn.setStyleSheet("")
        print(f"录制完成: {output_path}")

    # --- 学生端交互处理方法 ---

    def handle_student_joined(self, data):
        """处理学生加入小组"""
        student_id = data.get('student_id')
        student_name = data.get('student_name')
        student_sid = data.get('student_sid')

        print(f"学生 {student_name} ({student_id}) 加入小组")

        # 显示系统托盘通知
        self.show_notification("学生加入", f"学生 {student_name} 已加入小组")

        # 更新状态栏
        self.update_status_message(f"学生 {student_name} 加入小组")

    def handle_student_left(self, data):
        """处理学生离开小组"""
        student_id = data.get('student_id')
        print(f"学生 {student_id} 离开小组")

    def handle_screen_share_request(self, data):
        """处理学生投屏请求"""
        student_id = data.get('student_id')
        student_name = data.get('student_name')
        stream_name = data.get('stream_name')
        stream_type = data.get('stream_type', 'screen')

        print(f"收到学生 {student_name} 的投屏请求")

        # 自动接受投屏请求并添加到多路投屏管理器
        success = self.multi_screen_manager.add_student_stream(
            student_id, student_name, stream_name
        )

        if success:
            print(f"学生 {student_name} 投屏成功")
            # 显示多路投屏窗口
            if not self.multi_screen_manager.isVisible():
                self.multi_screen_manager.show()
        else:
            print(f"学生 {student_name} 投屏失败")

    def handle_screen_share_stopped(self, data):
        """处理学生停止投屏"""
        student_id = data.get('student_id')
        stream_name = data.get('stream_name')

        # 从多路投屏管理器中移除
        self.multi_screen_manager.remove_student_stream(student_id)
        print(f"学生 {student_id} 停止投屏")

    def handle_whiteboard_user_joined(self, data):
        """处理学生加入协同白板"""
        user_id = data.get('user_id')
        user_name = data.get('user_name')
        user_type = data.get('user_type')

        # 添加用户到协同白板管理器
        self.collaborative_whiteboard.add_user(user_id, user_name, user_type)
        print(f"学生 {user_name} 加入协同白板")

    def handle_whiteboard_operation(self, data):
        """处理协同白板操作"""
        operation = data.get('operation')
        sender_sid = data.get('sender_sid')

        # 将操作应用到本地白板
        if operation and self.whiteboard:
            self.collaborative_whiteboard.receive_operation(operation)

    def handle_file_shared(self, data):
        """处理学生分享文件"""
        student_id = data.get('student_id')
        student_name = data.get('student_name')
        file_info = data.get('file_info')
        shared_at = data.get('shared_at')

        print(f"学生 {student_name} 分享了文件: {file_info.get('filename')}")

        # 添加到文件分享组件
        self.file_sharing.add_shared_file(file_info, student_name)

        # 显示通知
        QMessageBox.information(
            self,
            "文件分享",
            f"学生 {student_name} 分享了文件:\n{file_info.get('filename')}"
        )

    def handle_artwork_shared(self, data):
        """处理学生分享作品"""
        student_id = data.get('student_id')
        student_name = data.get('student_name')
        artwork_info = data.get('artwork_info')
        shared_at = data.get('shared_at')

        print(f"学生 {student_name} 分享了作品")

        # 添加到作品展示组件
        self.artwork_display.add_shared_artwork(artwork_info, student_name)

        # 显示通知
        self.show_notification("作品分享", f"学生 {student_name} 分享了作品")

    def show_notification(self, title, message):
        """显示系统通知"""
        try:
            # 创建系统托盘通知
            if hasattr(self, 'tray_icon') and self.tray_icon:
                self.tray_icon.showMessage(title, message, QSystemTrayIcon.Information, 3000)
            else:
                # 如果没有系统托盘，使用消息框
                QMessageBox.information(self, title, message)
        except Exception as e:
            print(f"显示通知失败: {e}")

    def update_status_message(self, message):
        """更新状态栏消息"""
        try:
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage(message, 5000)  # 显示5秒
            print(f"状态更新: {message}")
        except Exception as e:
            print(f"更新状态失败: {e}")

    def setup_system_tray(self):
        """设置系统托盘"""
        try:
            if QSystemTrayIcon.isSystemTrayAvailable():
                self.tray_icon = QSystemTrayIcon(self)
                self.tray_icon.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
                self.tray_icon.setToolTip("小组研讨系统")

                # 创建托盘菜单
                tray_menu = QMenu()

                show_action = QAction("显示主窗口", self)
                show_action.triggered.connect(self.show)
                tray_menu.addAction(show_action)

                tray_menu.addSeparator()

                quit_action = QAction("退出", self)
                quit_action.triggered.connect(self.close)
                tray_menu.addAction(quit_action)

                self.tray_icon.setContextMenu(tray_menu)
                self.tray_icon.show()

                # 双击托盘图标显示窗口
                self.tray_icon.activated.connect(self.on_tray_icon_activated)

        except Exception as e:
            print(f"设置系统托盘失败: {e}")

    def on_tray_icon_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show()
            self.raise_()
            self.activateWindow()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = GroupApp()
    ex.show()
    sys.exit(app.exec_())
