#!/usr/bin/env python3
"""
仅测试登录功能的脚本
"""
import sys
import os
import configparser
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_login():
    """测试登录功能"""
    # 读取配置
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.ini')
    config = configparser.ConfigParser()
    config.read(config_path)
    
    server_host = config['Network'].get('SERVER_HOST')
    server_port = config['Network'].getint('SERVER_PORT')
    api_url = f"https://{server_host}:{server_port}"
    
    print(f"测试登录到: {api_url}")
    
    # 测试用户凭据
    username = "T001"
    password = "123456"
    
    try:
        print(f"正在尝试登录...")
        print(f"用户名: {username}")
        print(f"密码: {password}")
        
        login_data = {'user_id': username, 'password': password}
        print(f"发送数据: {login_data}")
        
        response = requests.post(
            f"{api_url}/api/login", 
            json=login_data, 
            verify=False, 
            timeout=15
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get('token')
            print(f"✅ 登录成功！")
            print(f"Token: {token[:50]}..." if token else "未获取到token")
            return token
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            return None
            
    except requests.exceptions.Timeout as e:
        print(f"❌ 请求超时: {e}")
        print("可能的原因：")
        print("1. 服务器响应慢")
        print("2. 网络连接问题")
        print("3. 服务器负载过高")
        return None
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        print("可能的原因：")
        print("1. 服务器未启动")
        print("2. 网络不可达")
        print("3. 端口被阻塞")
        return None
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        return None

if __name__ == "__main__":
    print("开始登录测试...")
    token = test_login()
    if token:
        print("\n✅ 登录测试成功！")
    else:
        print("\n❌ 登录测试失败！")
    print("测试完成。")
