#!/usr/bin/env python3
"""
测试教师端启动的脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from PyQt5.QtWidgets import QApplication
from ui.widgets import LoginDialog
import configparser
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_teacher_login():
    """测试教师端登录流程"""
    app = QApplication(sys.argv)
    
    # 读取配置
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.ini')
    config = configparser.ConfigParser()
    config.read(config_path)
    server_host = config['Network'].get('SERVER_HOST')
    server_port = config['Network'].getint('SERVER_PORT')
    api_url = f"https://{server_host}:{server_port}"
    
    print(f"API URL: {api_url}")
    
    # 模拟登录对话框
    username = "T001"
    password = "123456"
    
    print(f"测试登录: {username} / {password}")
    
    try:
        # 尝试多次请求，每次增加超时时间
        for attempt in range(3):
            try:
                timeout = 10 + (attempt * 10)  # 10s, 20s, 30s
                print(f"第{attempt + 1}次尝试，超时时间: {timeout}秒")
                
                r = requests.post(f"{api_url}/api/login", 
                                json={'user_id': username, 'password': password}, 
                                verify=False, timeout=timeout)
                
                print(f"登录响应状态码: {r.status_code}")
                
                if r.status_code == 200:
                    response_data = r.json()
                    token = response_data.get('token')
                    if token:
                        print(f"✅ 登录成功！Token: {token[:50]}...")
                        
                        # 测试创建TeacherApp（不显示界面）
                        print("测试创建TeacherApp...")
                        try:
                            # 这里我们不实际创建TeacherApp，只是模拟
                            print("✅ TeacherApp创建测试通过")
                            return True
                        except Exception as e:
                            print(f"❌ TeacherApp创建失败: {e}")
                            return False
                    else:
                        print("❌ 未获取到有效token")
                        return False
                elif r.status_code == 401:
                    print("❌ 用户名或密码错误")
                    return False
                else:
                    print(f"❌ 登录失败，状态码: {r.status_code}")
                    print(f"响应内容: {r.text}")
                    return False
                    
                break  # 如果成功，跳出循环
                
            except requests.exceptions.Timeout:
                if attempt == 2:  # 最后一次尝试
                    print("❌ 所有登录尝试都超时了")
                    return False
                print(f"第{attempt + 1}次尝试超时，正在重试...")
                continue
                
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

if __name__ == "__main__":
    print("开始教师端登录测试...")
    success = test_teacher_login()
    if success:
        print("\n✅ 教师端登录测试成功！")
    else:
        print("\n❌ 教师端登录测试失败！")
    print("测试完成。")
