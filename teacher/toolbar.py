import sys
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

class ToolbarWindow(QMainWindow):
    button_clicked_signal = pyqtSignal(str)

    def __init__(self, parent=None, on_close_callback=None):
        super().__init__(parent)
        self.on_close_callback = on_close_callback

        self.setWindowTitle("工具栏")
        self.setWindowFlags(Qt.Tool | Qt.WindowStaysOnTopHint)

        # 创建中心控件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        self.vertical_layout = QVBoxLayout(central_widget)
        self.vertical_layout.setSpacing(5)
        self.vertical_layout.setContentsMargins(10, 10, 10, 10)
        self.vertical_layout.setAlignment(Qt.AlignTop)

        # 按钮列表
        buttons_text = ["广播", "分享", "投屏", "白板", "计时", "主题", "聊天", "选人", "签到管理", "退出"]
        self.button_states = {text: False for text in buttons_text}

        # 创建并添加按钮
        for text in buttons_text:
            button = QPushButton(text)
            # 移除样式设置
            button.clicked.connect(lambda _, t=text, b=button: self.handle_button_click(t, b))
            self.vertical_layout.addWidget(button)

        self.setMinimumWidth(150)
        self.resize(150, 300)

        # 获取屏幕尺寸并设置窗口位置
        screen_geometry = QApplication.primaryScreen().geometry()
        x = screen_geometry.width() - self.width()
        y = (screen_geometry.height() - self.height()) // 2
        self.move(x, y)

    def handle_button_click(self, text, button):
        # 发射信号，将按钮的文本传递出去
        self.button_clicked_signal.emit(text)
        
        if text == "退出":
            self.close()

    def closeEvent(self, event):
        if self.on_close_callback:
            self.on_close_callback()
        super().closeEvent(event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    toolbar = ToolbarWindow()
    toolbar.show()
    sys.exit(app.exec_())

