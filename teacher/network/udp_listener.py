# teacher/network/udp_listener.py
import socket
import json
from PyQt5.QtCore import QThread, pyqtSignal

class UdpListener(QThread):
    group_status_signal = pyqtSignal(dict)

    def __init__(self, listen_port=9999):
        super().__init__()
        self.listen_port = listen_port
        self.running = True

    def run(self):
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind(('', self.listen_port))
        while self.running:
            try:
                data, addr = sock.recvfrom(1024)
                message = json.loads(data.decode('utf-8'))
                if message.get("type") == "discovery":
                    self.group_status_signal.emit(message)
            except Exception:
                break
    
    def stop(self):
        self.running = False
        try:
            # Send a dummy packet to unblock the recvfrom call
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.sendto(b'', ('127.0.0.1', self.listen_port))
            sock.close()
        except Exception:
            pass
        self.wait()
