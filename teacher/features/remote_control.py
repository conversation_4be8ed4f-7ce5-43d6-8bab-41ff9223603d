# teacher/features/remote_control.py
import json
import os
import subprocess
import logging
import configparser
from wakeonlan import send_magic_packet
from PyQt5.QtWidgets import QMessageBox

log = logging.getLogger(__name__)

class RemoteControlManager:
    def __init__(self, parent=None):
        self.parent = parent
        self.computers = []
        self.remote_user = None
        self.remote_password = None
        self._load_config()

    def _load_config(self):
        """从 config.ini 加载远程控制凭据"""
        config = configparser.ConfigParser()
        # 路径修正，确保能从项目根目录找到 config.ini
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'config.ini')
        
        try:
            if not os.path.exists(config_path):
                log.error(f"配置文件未找到: {config_path}")
                QMessageBox.critical(self.parent, "配置错误", f"配置文件未找到: {config_path}")
                return
                
            config.read(config_path, encoding='utf-8')
            if 'RemoteControl' in config:
                self.remote_user = config['RemoteControl'].get('REMOTE_USER')
                self.remote_password = config['RemoteControl'].get('REMOTE_PASSWORD')
            else:
                log.warning("配置文件中未找到 [RemoteControl] 部分。远程关机功能将不可用。")
                # 不再使用默认值，强制要求配置
        except Exception as e:
            log.error(f"读取或解析配置文件失败: {e}")
            QMessageBox.critical(self.parent, "配置错误", f"读取配置文件失败: {e}")

    

    def get_computer_list(self):
        """返回加载的电脑信息列表"""
        return self.computers

    def wake_computers(self, mac_addresses):
        """唤醒一组指定的电脑"""
        if not mac_addresses:
            QMessageBox.information(self.parent, "提示", "没有可用的MAC地址，无法执行操作。")
            return

        print("正在发送唤醒指令...")
        try:
            for mac in mac_addresses:
                if mac:
                    send_magic_packet(mac)
                    print(f"已向 MAC 地址 {mac} 发送唤醒包。")
            QMessageBox.information(self.parent, "成功", "所有唤醒命令已发送！")
        except Exception as e:
            QMessageBox.critical(self.parent, "唤醒失败", f"发送唤醒包时发生错误: {e}")
            print(f"唤醒失败: {e}")

    def shutdown_computer(self, ip):
        """使用 SSH 和 sshpass 关闭单台指定IP的电脑"""
        if not ip:
            QMessageBox.warning(self.parent, "错误", "未提供IP地址。")
            return

        if not self.remote_user or not self.remote_password:
            QMessageBox.warning(self.parent, "配置缺失", 
                                "远程关机功能需要用户名和密码。\n"
                                "请在项目根目录的 `config.ini` 文件中正确设置 `REMOTE_USER` 和 `REMOTE_PASSWORD`。")
            return

        print(f"尝试通过SSH关闭 IP 地址为 {ip} 的电脑...")
        try:
            # 使用 sshpass 非交互式地提供密码
            command = [
                'sshpass', '-p', self.remote_password, 
                'ssh', '-o', 'StrictHostKeyChecking=no', '-o', 'UserKnownHostsFile=/dev/null',
                f'{self.remote_user}@{ip}',
                'nohup sudo shutdown -h now > /dev/null 2>&1 &'
            ]
            
            result = subprocess.run(
                command,
                check=True,
                capture_output=True,
                text=True,
                timeout=15
            )
            log.info(f"成功向 {ip} 发送SSH关机指令。输出: {result.stdout}")
            QMessageBox.information(self.parent, "成功", f"已向 {ip} 发送关机指令。")

        except FileNotFoundError:
            QMessageBox.critical(self.parent, "命令未找到", 
                                 "关机失败：'sshpass' 命令未找到。\n"
                                 "请在教师端电脑上执行 `sudo apt-get install sshpass` 进行安装。")
        except subprocess.CalledProcessError as e:
            error_message = e.stderr or e.stdout
            log.error(f"关闭 {ip} 失败。错误: {e}. 输出: {error_message}")
            if "Permission denied" in error_message:
                QMessageBox.warning(self.parent, "权限被拒绝", 
                                    f"关闭 {ip} 失败：SSH权限被拒绝。\n\n"
                                    "请检查：\n"
                                    "1. 小组端是否已安装并启动 `openssh-server`。\n"
                                    "2. `config.ini` 中的用户名和密码是否正确。\n"
                                    "3. 该用户是否有权通过SSH登录。")
            else:
                QMessageBox.warning(self.parent, "关机失败", f"关闭 {ip} 失败: {error_message}")
        except subprocess.TimeoutExpired:
            log.warning(f"关闭 {ip} 的操作超时。")
            QMessageBox.warning(self.parent, "操作超时", f"关闭 {ip} 的操作超时。请检查网络连接和SSH服务状态。")
