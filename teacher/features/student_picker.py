# teacher/features/student_picker.py
import random
from PyQt5.QtWidgets import QMessageBox

class StudentPicker:
    def __init__(self, parent=None):
        """
        初始化学生选择器。
        :param parent: 父级窗口，用于QMessageBox的定位和模态。
        """
        self.students = []
        self.parent = parent

    def update_student_list(self, students_dict):
        """
        更新可供选择的学生列表。
        :param students_dict: 从API获取的学生字典。
        """
        self.students = list(students_dict.values())

    def pick(self):
        """
        执行随机点名，并显示结果对话框。
        """
        if not self.students:
            QMessageBox.warning(self.parent, "提示", "当前课程没有学生或学生列表未加载。")
            return

        random_student = random.choice(self.students)
        student_name = random_student.get('name', '未知姓名')
        student_id = random_student.get('student_id', '未知学生')

        msg_box = QMessageBox(self.parent)
        msg_box.setWindowTitle("随机点名结果")
        msg_box.setText(f'<p>随机选中的学生是：</p><p style="font-size: 24px; color: #1E9FFF; font-weight: bold;">{student_name}</p><p>学号: {student_id}</p>')
        msg_box.setIcon(QMessageBox.Information)
        msg_box.addButton("换一个", QMessageBox.AcceptRole)
        msg_box.addButton("确定", QMessageBox.RejectRole)
        
        if msg_box.exec_() == QMessageBox.AcceptRole:
            # 如果用户点击“换一个”，则再次调用该函数
            self.pick()
