# teacher/features/file_sharing.py
import os
import json
import requests
from PyQt5.QtWidgets import QFileDialog, QMessageBox, QWidget

class FileSharingManager(QWidget):
    def __init__(self, api_url, token, parent=None):
        super().__init__(parent)
        self.api_url = api_url
        self.token = token

    def distribute_file(self):
        """
        打开文件对话框并处理文件分发逻辑。
        这个方法现在由外部调用（例如，从工具栏）。
        """
        # 使用一个临时的父级QWidget来显示对话框
        parent_widget = QWidget()
        file_path, _ = QFileDialog.getOpenFileName(parent_widget, "选择要分发的文件")
        if not file_path:
            return

        try:
            file_basename = os.path.basename(file_path)
            with open(file_path, 'rb') as f:
                files = {'files': (file_basename, f)}
                headers = {'Authorization': f'Bearer {self.token}'}
                r = requests.post(f"{self.api_url}/api/files/upload", files=files, headers=headers)
            
            if r.status_code == 200:
                try:
                    response_data = r.json()
                    if response_data.get('status') == 'success':
                        QMessageBox.information(parent_widget, "成功", response_data.get('message', '文件分发成功。'))
                    else:
                        QMessageBox.warning(parent_widget, "失败", f"文件分发失败: {response_data.get('message', '未知错误')}")
                except json.JSONDecodeError:
                     QMessageBox.warning(parent_widget, "失败", f"服务器返回了非预期的响应: {r.text}")
            else:
                QMessageBox.warning(parent_widget, "失败", f"文件分发失败 (HTTP {r.status_code}): {r.text}")
        except FileNotFoundError:
            QMessageBox.critical(parent_widget, "错误", "找不到所选文件。")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(parent_widget, "错误", f"连接服务器失败: {e}")