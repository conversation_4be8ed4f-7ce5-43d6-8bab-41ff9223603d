# teacher/features/group_topic.py
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit, 
                             QPushButton)
from PyQt5.QtCore import Qt

class TopicSetupDialog(QDialog):
    """一个对话框，用于让教师输入分组讨论的主题。"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("设置分组讨论主题")
        self.setMinimumSize(400, 250)

        self.layout = QVBoxLayout(self)
        
        self.label = QLabel("请输入讨论主题:")
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("在这里输入或粘贴讨论主题...")
        
        self.button_box = QHBoxLayout()
        self.ok_button = QPushButton("发送主题")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        self.button_box.addStretch()
        self.button_box.addWidget(self.ok_button)
        self.button_box.addWidget(self.cancel_button)
        
        self.layout.addWidget(self.label)
        self.layout.addWidget(self.text_edit)
        self.layout.addLayout(self.button_box)

    def get_topic_text(self):
        """返回输入的文本。"""
        return self.text_edit.toPlainText().strip()
