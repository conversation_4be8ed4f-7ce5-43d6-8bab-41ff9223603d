# teacher/features/touch_control.py
import json
import logging
import platform
import subprocess
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

log = logging.getLogger(__name__)

class TouchControlManager(QObject):
    # 信号：向Socket发送触控事件
    touch_event_signal = pyqtSignal(dict)
    
    def __init__(self, parent=None, socket_manager=None):
        super().__init__()
        self.parent = parent
        self.socket_manager = socket_manager
        self.active_control_sessions = {}  # {group_id: control_info}
        
    def show_virtual_keyboard(self, group_id):
        """调起指定小组端的虚拟键盘"""
        try:
            # 发送调起虚拟键盘命令
            keyboard_command = {
                'action': 'show_virtual_keyboard',
                'group_id': group_id,
                'timestamp': self._get_timestamp()
            }
            
            if self.socket_manager and self.socket_manager.is_connected():
                self.socket_manager.sio.emit('touch_control_command', keyboard_command)
                log.info(f"已向小组 {group_id} 发送虚拟键盘调起命令")
                return True
            else:
                QMessageBox.warning(self.parent, "连接错误", "Socket连接未建立，无法发送虚拟键盘命令")
                return False
                
        except Exception as e:
            log.error(f"调起虚拟键盘失败: {e}")
            QMessageBox.critical(self.parent, "错误", f"调起虚拟键盘失败: {e}")
            return False
    
    def hide_virtual_keyboard(self, group_id):
        """隐藏指定小组端的虚拟键盘"""
        try:
            keyboard_command = {
                'action': 'hide_virtual_keyboard', 
                'group_id': group_id,
                'timestamp': self._get_timestamp()
            }
            
            if self.socket_manager and self.socket_manager.is_connected():
                self.socket_manager.sio.emit('touch_control_command', keyboard_command)
                log.info(f"已向小组 {group_id} 发送虚拟键盘隐藏命令")
                return True
            else:
                QMessageBox.warning(self.parent, "连接错误", "Socket连接未建立")
                return False
                
        except Exception as e:
            log.error(f"隐藏虚拟键盘失败: {e}")
            return False
    
    def start_touch_control_session(self, group_id):
        """开始触控回传会话"""
        try:
            session_data = {
                'action': 'start_touch_control',
                'group_id': group_id,
                'timestamp': self._get_timestamp(),
                'control_mode': 'full_control'  # 完整控制模式
            }
            
            if self.socket_manager and self.socket_manager.is_connected():
                self.socket_manager.sio.emit('touch_control_command', session_data)
                self.active_control_sessions[group_id] = {
                    'start_time': self._get_timestamp(),
                    'status': 'active'
                }
                log.info(f"已启动对小组 {group_id} 的触控回传会话")
                return True
            else:
                QMessageBox.warning(self.parent, "连接错误", "无法启动触控会话")
                return False
                
        except Exception as e:
            log.error(f"启动触控会话失败: {e}")
            return False
    
    def stop_touch_control_session(self, group_id):
        """停止触控回传会话"""
        try:
            session_data = {
                'action': 'stop_touch_control',
                'group_id': group_id,
                'timestamp': self._get_timestamp()
            }
            
            if self.socket_manager and self.socket_manager.is_connected():
                self.socket_manager.sio.emit('touch_control_command', session_data)
                
            # 清理本地会话记录
            if group_id in self.active_control_sessions:
                del self.active_control_sessions[group_id]
                
            log.info(f"已停止对小组 {group_id} 的触控回传会话")
            return True
            
        except Exception as e:
            log.error(f"停止触控会话失败: {e}")
            return False
    
    def send_touch_event(self, group_id, event_type, coordinates, pressure=1.0):
        """发送触控事件到小组端
        
        Args:
            group_id: 目标小组ID
            event_type: 事件类型 ('touch_down', 'touch_move', 'touch_up', 'key_input')
            coordinates: 坐标 {'x': int, 'y': int}
            pressure: 触控压力 (0.0-1.0)
        """
        try:
            if group_id not in self.active_control_sessions:
                log.warning(f"小组 {group_id} 没有活跃的触控会话")
                return False
                
            touch_event = {
                'action': 'touch_event',
                'group_id': group_id,
                'event_type': event_type,
                'coordinates': coordinates,
                'pressure': pressure,
                'timestamp': self._get_timestamp()
            }
            
            if self.socket_manager and self.socket_manager.is_connected():
                self.socket_manager.sio.emit('touch_control_event', touch_event)
                return True
            else:
                log.warning("Socket连接断开，无法发送触控事件")
                return False
                
        except Exception as e:
            log.error(f"发送触控事件失败: {e}")
            return False
    
    def send_text_input(self, group_id, text):
        """发送文本输入到小组端"""
        try:
            if group_id not in self.active_control_sessions:
                log.warning(f"小组 {group_id} 没有活跃的触控会话")
                return False
                
            text_event = {
                'action': 'text_input',
                'group_id': group_id,
                'text': text,
                'timestamp': self._get_timestamp()
            }
            
            if self.socket_manager and self.socket_manager.is_connected():
                self.socket_manager.sio.emit('touch_control_event', text_event)
                log.info(f"已向小组 {group_id} 发送文本: {text}")
                return True
            else:
                return False
                
        except Exception as e:
            log.error(f"发送文本输入失败: {e}")
            return False
    
    def get_active_sessions(self):
        """获取当前活跃的触控会话"""
        return list(self.active_control_sessions.keys())
    
    def is_session_active(self, group_id):
        """检查指定小组是否有活跃的触控会话"""
        return group_id in self.active_control_sessions
    
    def _get_timestamp(self):
        """获取当前时间戳（毫秒）"""
        import time
        return int(time.time() * 1000)