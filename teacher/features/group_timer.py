# teacher/features/group_timer.py
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, 
                             QPushButton, QWidget)
from PyQt5.QtCore import Qt, QTimer, QTime

class TimerSetupDialog(QDialog):
    """一个简单的对话框，用于设置倒计时的分钟数。"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("设置分组计时")
        
        self.layout = QVBoxLayout(self)
        
        self.label = QLabel("请输入倒计时分钟数:")
        self.spin_box = QSpinBox()
        self.spin_box.setRange(1, 120) # 1分钟到2小时
        self.spin_box.setValue(10) # 默认10分钟
        
        self.button_box = QHBoxLayout()
        self.ok_button = QPushButton("开始计时")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        self.button_box.addStretch()
        self.button_box.addWidget(self.ok_button)
        self.button_box.addWidget(self.cancel_button)
        
        self.layout.addWidget(self.label)
        self.layout.addWidget(self.spin_box)
        self.layout.addLayout(self.button_box)

    def get_duration_seconds(self):
        """返回设置的分钟数（转换为秒）。"""
        return self.spin_box.value() * 60

class FloatingTimerWindow(QWidget):
    """一个悬浮的、可拖动的倒计时窗口。"""
    def __init__(self, duration_seconds, parent=None):
        super().__init__(parent)
        self.duration = duration_seconds
        
        self.setWindowFlags(Qt.Tool | Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        self.layout = QVBoxLayout(self)
        self.time_label = QLabel()
        self.time_label.setStyleSheet("""
            background-color: rgba(0, 0, 0, 180);
            color: white;
            font-size: 24px;
            font-weight: bold;
            padding: 10px;
            border-radius: 8px;
        """)
        self.layout.addWidget(self.time_label)
        
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_time)
        
        self.remaining_time = QTime(0, 0, 0).addSecs(self.duration)
        self.update_label()

    def start(self):
        self.timer.start(1000) # 每秒更新一次
        self.show()

    def update_time(self):
        self.remaining_time = self.remaining_time.addSecs(-1)
        self.update_label()
        if self.remaining_time == QTime(0, 0, 0):
            self.timer.stop()
            self.close()

    def update_label(self):
        self.time_label.setText(self.remaining_time.toString("mm:ss"))

    def mousePressEvent(self, event):
        self.old_pos = event.globalPos()

    def mouseMoveEvent(self, event):
        delta = event.globalPos() - self.old_pos
        self.move(self.x() + delta.x(), self.y() + delta.y())
        self.old_pos = event.globalPos()
