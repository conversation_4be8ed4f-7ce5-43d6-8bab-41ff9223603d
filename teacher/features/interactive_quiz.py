# teacher/features/interactive_quiz.py
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QPushButton, QMessageBox, QDialog, 
                             QListWidget, QListWidgetItem, QLabel, QScrollArea, QFrame,
                             QHBoxLayout, QProgressBar,QStackedLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor
import requests
import json

class PaperSelectionDialog(QDialog):
    """一个对话框，用于让教师从他们的试卷库中选择一份试卷。"""
    def __init__(self, api_url, token, parent=None):
        super().__init__(parent)
        self.api_url = api_url
        self.token = token
        self.selected_paper = None
        self.setWindowTitle("选择试卷发起互动答题")
        self.setMinimumSize(500, 400)

        self.layout = QVBoxLayout(self)
        
        self.list_widget = QListWidget()
        self.list_widget.itemDoubleClicked.connect(self.accept)
        
        self.layout.addWidget(QLabel("请选择一份试卷:"))
        self.layout.addWidget(self.list_widget)
        
        self.button_box = QHBoxLayout()
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        self.button_box.addStretch()
        self.button_box.addWidget(self.ok_button)
        self.button_box.addWidget(self.cancel_button)
        
        self.layout.addLayout(self.button_box)
        
        self.load_papers()

    def load_papers(self):
        """从API加载教师的试卷列表。"""
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            r = requests.get(f"{self.api_url}/api/teacher/papers", headers=headers)
            if r.status_code == 200:
                data = r.json()
                if data.get('success'):
                    self.list_widget.clear()
                    for paper in data.get('papers', []):
                        item_text = f"{paper['title']} (创建于: {paper['created_at'].split('T')[0]})"
                        item = QListWidgetItem(item_text)
                        item.setData(Qt.UserRole, (paper['id'], paper['title']))
                        self.list_widget.addItem(item)
                else:
                    QMessageBox.warning(self, "加载失败", data.get('message', '无法加载试卷列表。'))
            else:
                QMessageBox.warning(self, "错误", f"请求失败: {r.text}")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "网络错误", f"连接服务器失败: {e}")

    def accept(self):
        """当用户点击确定或双击列表项时调用。"""
        current_item = self.list_widget.currentItem()
        if current_item:
            self.selected_paper = current_item.data(Qt.UserRole)
            super().accept()
        else:
            QMessageBox.warning(self, "提示", "请选择一份试卷。")

    def get_selected_paper(self):
        """返回选中的试卷 (id, title)。"""
        return self.selected_paper

class QuestionResultsWidget(QFrame):
    """显示单个问题的实时答题结果。"""
    def __init__(self, question_data, question_id, parent=None):
        super().__init__(parent)
        self.question_data = question_data
        self.question_id = question_id
        self.option_labels = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        
        self.setFrameShape(QFrame.StyledPanel)
        self.layout = QVBoxLayout(self)
        
        self.question_label = QLabel(f"{question_data.get('content', '未知题目')}")
        self.question_label.setStyleSheet("font-weight: bold;")
        self.layout.addWidget(self.question_label)
        
        self.progress_bars = {}
        options = question_data.get('options')
        
        # 处理判断题
        if question_data.get('type') == 'judge':
            options = ['正确', '错误']

        if options and isinstance(options, list):
            for i, option_text in enumerate(options):
                option_key = self.option_labels[i]
                
                option_layout = QHBoxLayout()
                
                label = QLabel(f"{option_key}. {option_text}")
                label.setFixedWidth(150)
                option_layout.addWidget(label)
                
                progress_bar = QProgressBar()
                progress_bar.setRange(0, 100)
                progress_bar.setValue(0)
                progress_bar.setTextVisible(False)
                option_layout.addWidget(progress_bar)
                
                count_label = QLabel("0票 (0%)")
                count_label.setFixedWidth(100)
                option_layout.addWidget(count_label)
                
                self.progress_bars[option_key] = {
                    'bar': progress_bar,
                    'count_label': count_label
                }
                
                self.layout.addLayout(option_layout)

    def update_display(self, stats_data):
        """根据传入的统计数据刷新显示。"""
        total_submissions = stats_data.get('total', 0)
        answers = stats_data.get('answers', {})

        # 为所有可能的选项（包括未作答的）初始化显示
        for option_key, widgets in self.progress_bars.items():
            count = answers.get(option_key, 0)
            percentage = (count / total_submissions * 100) if total_submissions > 0 else 0
            widgets['bar'].setValue(int(percentage))
            widgets['count_label'].setText(f"{count}票 ({percentage:.0f}%)")


class InteractiveQuizTab(QWidget):
    """互动答题功能的主标签页。"""
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.api_url = main_window.api_url
        self.token = main_window.token
        self.current_quiz_id = None
        self.question_widgets = {}  # { 'all': {q_id: widget}, 'group_ip': {q_id: widget} }
        
        # 新的数据结构
        self.quiz_stats = {} # { q_id: { "all": { "answers": {}, "total": 0 }, "group_ip": ... } }
        self.student_group_map = {} # { student_id: group_ip }

        self.initUI()

    def initUI(self):
        self.layout = QVBoxLayout(self)
        
        # 初始视图
        self.initial_widget = QWidget()
        initial_layout = QVBoxLayout(self.initial_widget)
        self.start_button = QPushButton("发起互动答题")
        self.start_button.setMinimumHeight(50)
        self.start_button.clicked.connect(self.show_paper_selection_dialog)
        initial_layout.addStretch()
        initial_layout.addWidget(self.start_button)
        initial_layout.addStretch()
        self.layout.addWidget(self.initial_widget)

        # 实时仪表盘视图 (初始隐藏)
        self.dashboard_widget = QWidget()
        self.dashboard_layout = QVBoxLayout(self.dashboard_widget)
        
        # --- 顶部控制栏 ---
        top_controls_layout = QHBoxLayout()
        self.quiz_title_label = QLabel("答题进行中...")
        self.quiz_title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        
        self.view_switch_buttons = QHBoxLayout()
        self.all_class_btn = QPushButton("全班统计")
        self.all_class_btn.setCheckable(True)
        self.all_class_btn.setChecked(True)
        self.group_view_btn = QPushButton("分组对比")
        self.group_view_btn.setCheckable(True)
        
        self.all_class_btn.clicked.connect(lambda: self.switch_view(0))
        self.group_view_btn.clicked.connect(lambda: self.switch_view(1))

        self.view_switch_buttons.addWidget(self.all_class_btn)
        self.view_switch_buttons.addWidget(self.group_view_btn)
        
        top_controls_layout.addWidget(self.quiz_title_label)
        top_controls_layout.addStretch()
        top_controls_layout.addLayout(self.view_switch_buttons)
        self.dashboard_layout.addLayout(top_controls_layout)

        # --- 视图堆栈 ---
        self.view_stack = QStackedLayout()
        
        # 全班视图
        self.all_class_view = QWidget()
        all_class_layout = QVBoxLayout(self.all_class_view)
        scroll_area_all = QScrollArea()
        scroll_area_all.setWidgetResizable(True)
        self.questions_container_all = QWidget() # 原有的容器
        self.questions_layout_all = QVBoxLayout(self.questions_container_all)
        scroll_area_all.setWidget(self.questions_container_all)
        all_class_layout.addWidget(scroll_area_all)
        
        # 分组视图
        self.group_view = QWidget()
        group_layout = QVBoxLayout(self.group_view)
        scroll_area_group = QScrollArea()
        scroll_area_group.setWidgetResizable(True)
        self.questions_container_group = QWidget()
        self.questions_layout_group = QVBoxLayout(self.questions_container_group)
        scroll_area_group.setWidget(self.questions_container_group)
        group_layout.addWidget(scroll_area_group)
        # 临时添加一个标签以作区分
        self.questions_layout_group.addWidget(QLabel("这里将显示各小组的答题情况。"))

        self.view_stack.addWidget(self.all_class_view)
        self.view_stack.addWidget(self.group_view)
        
        self.dashboard_layout.addLayout(self.view_stack)

        # --- 底部控制栏 ---
        self.end_button = QPushButton("结束答题")
        self.end_button.clicked.connect(self.end_quiz)
        self.dashboard_layout.addWidget(self.end_button)

        self.layout.addWidget(self.dashboard_widget)
        self.dashboard_widget.hide()

    def switch_view(self, index):
        """切换全班/分组视图。"""
        self.view_stack.setCurrentIndex(index)
        self.all_class_btn.setChecked(index == 0)
        self.group_view_btn.setChecked(index == 1)

    def show_paper_selection_dialog(self):
        if not self.main_window.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先开始一门课程。")
            return
        
        dialog = PaperSelectionDialog(self.api_url, self.token, self)
        if dialog.exec_() == QDialog.Accepted:
            paper_id, paper_title = dialog.get_selected_paper()
            if paper_id:
                self.start_quiz(paper_id, paper_title)

    def start_quiz(self, paper_id, paper_title):
        # 重置状态
        self.quiz_stats = {}
        self.student_group_map = self.main_window.grouping_tab_widget.get_student_group_map()
        
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            payload = {
                'paper_id': paper_id,
                'title': f"互动答题 - {paper_title}",
                'course_schedule_id': self.main_window.current_course_schedule_id
            }
            r = requests.post(f"{self.api_url}/api/teacher/interactive/start", json=payload, headers=headers)
            
            if r.status_code == 200:
                response = r.json()
                if response.get('success'):
                    self.current_quiz_id = response.get('homework_id')
                    QMessageBox.information(self, "成功", "互动答题已发起！学生端现在可以作答。")
                    self.load_quiz_dashboard()
                else:
                    QMessageBox.warning(self, "发起失败", response.get('message', '未知错误'))
            else:
                QMessageBox.warning(self, "错误", f"发起请求失败: {r.text}")

        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "网络错误", f"连接服务器失败: {e}")

    def load_quiz_dashboard(self):
        self.initial_widget.hide()
        self.dashboard_widget.show()

        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            # 修正了API URL，使用新的、无歧义的路由
            r = requests.get(f"{self.api_url}/api/teacher/homework/{self.current_quiz_id}", headers=headers)
            
            if r.status_code == 200:
                res_data = r.json()
                if res_data.get('status') == 'success':
                    quiz_data = res_data.get('details', {})
                    self.quiz_title_label.setText(f"答题进行中: {quiz_data.get('title')}")
                    
                    questions = quiz_data.get('questions', [])
                    
                    self.display_questions(questions)
                else:
                    QMessageBox.warning(self, "错误", res_data.get('message', '无法加载题目详情'))
            else:
                QMessageBox.warning(self, "错误", f"加载题目失败: {r.text}")
        except (requests.exceptions.RequestException, json.JSONDecodeError) as e:
            QMessageBox.critical(self, "错误", f"加载或解析数据失败: {e}")

    def display_questions(self, questions):
        # 清空所有旧的题目控件
        for layout in [self.questions_layout_all, self.questions_layout_group]:
            for i in reversed(range(layout.count())): 
                item = layout.itemAt(i)
                if item.widget():
                    item.widget().setParent(None)
        
        self.question_widgets.clear()
        self.question_widgets['all'] = {}

        # 获取分组信息
        groups = self.main_window.grouping_tab_widget.get_groups()
        
        # 如果没有分组，禁用分组视图按钮
        self.group_view_btn.setEnabled(bool(groups))
        if not groups:
            self.questions_layout_group.addWidget(QLabel("请先在“分组与监看”标签页中进行分组，才能查看分组对比。"))
        else:
            self.question_widgets['groups'] = {}

        # 遍历所有题目
        for question_details in questions:
            q_id = question_details.get('id')
            if not q_id:
                continue

            # --- 1. 创建全班视图的控件 ---
            all_widget = QuestionResultsWidget(question_details, q_id)
            self.questions_layout_all.addWidget(all_widget)
            self.question_widgets['all'][q_id] = all_widget
            
            # --- 2. 初始化统计数据结构 ---
            self.quiz_stats[q_id] = {"all": {"answers": {}, "total": 0}}

            # --- 3. 创建分组视图的控件 ---
            if groups:
                # 为每个小组创建此题目的统计控件
                for group_ip, group_info in groups.items():
                    if group_ip not in self.question_widgets['groups']:
                        self.question_widgets['groups'][group_ip] = {}
                        # 为每个小组添加一个标题
                        group_title = QLabel(f"小组: {group_info.get('hostname', group_ip)}")
                        group_title.setStyleSheet("font-size: 14px; font-weight: bold; margin-top: 10px; border-bottom: 1px solid #ccc;")
                        self.questions_layout_group.addWidget(group_title)

                    group_widget = QuestionResultsWidget(question_details, q_id)
                    self.questions_layout_group.addWidget(group_widget)
                    self.question_widgets['groups'][group_ip][q_id] = group_widget
                    
                    # 初始化小组统计数据
                    self.quiz_stats[q_id][group_ip] = {"answers": {}, "total": 0}

    def get_exercise_details(self, exercise_id):
        """(此方法现在不再需要，但保留以防万一)"""
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            r = requests.get(f"{self.api_url}/api/exercises/{exercise_id}", headers=headers)
            if r.status_code == 200:
                res = r.json()
                if res.get('success'):
                    return True, res.get('exercise')
        except Exception:
            return False, None
        return False, None

    def update_answer(self, data):
        """核心数据处理方法"""
        if str(data.get('homework_id')) != str(self.current_quiz_id):
            return

        question_id = data.get('question_id')
        student_id = data.get('student_id')
        answer = data.get('answer')

        if question_id not in self.quiz_stats:
            return

        # 1. 更新全班统计
        stats_all = self.quiz_stats[question_id]['all']
        stats_all['total'] += 1
        submitted_options = answer.split(',')
        for option in submitted_options:
            option = option.strip()
            stats_all['answers'][option] = stats_all['answers'].get(option, 0) + 1
        
        # 2. 更新UI (全班)
        if question_id in self.question_widgets.get('all', {}):
            self.question_widgets['all'][question_id].update_display(stats_all)

        # 3. 更新小组统计
        group_ip = self.student_group_map.get(student_id)
        
        if group_ip:
            if group_ip not in self.quiz_stats[question_id]:
                self.quiz_stats[question_id][group_ip] = {"answers": {}, "total": 0}
            
            stats_group = self.quiz_stats[question_id][group_ip]
            stats_group['total'] += 1
            for option in submitted_options:
                option = option.strip()
                stats_group['answers'][option] = stats_group['answers'].get(option, 0) + 1
            
            # 4. 更新UI (小组)
            if group_ip in self.question_widgets.get('groups', {}) and \
               question_id in self.question_widgets['groups'][group_ip]:
                self.question_widgets['groups'][group_ip][question_id].update_display(stats_group)

    def end_quiz(self):
        if not self.current_quiz_id:
            return
            
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            r = requests.post(f"{self.api_url}/api/teacher/interactive/{self.current_quiz_id}/end", headers=headers)
            
            if r.status_code == 200:
                response = r.json()
                if response.get('status') == 'success':
                    QMessageBox.information(self, "结束", "本轮互动答题已结束，学生端已收到通知。")
                else:
                    QMessageBox.warning(self, "结束失败", response.get('message', '未知错误'))
            else:
                QMessageBox.warning(self, "错误", f"结束请求失败: {r.text}")
                
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "网络错误", f"连接服务器失败: {e}")
        
        # 重置UI状态
        self.current_quiz_id = None
        self.dashboard_widget.hide()
        self.initial_widget.show()
        
    def set_session_active(self, active):
        """根据课堂会话状态启用/禁用控件。"""
        self.setEnabled(active)
        if not active:
            # 如果课堂结束，重置UI状态
            if self.current_quiz_id:
                self.end_quiz()
