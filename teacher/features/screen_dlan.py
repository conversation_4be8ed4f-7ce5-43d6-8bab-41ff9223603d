import sys
import threading
import time
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QPushButton, QListWidget, QLabel, QMessageBox, QListWidgetItem
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QObject
from flask import Flask, send_from_directory
import socket
import upnpy
import requests
from xml.sax.saxutils import escape
import subprocess
import os
import glob
import platform


class ScreenStreamer:
    """屏幕捕获和流媒体服务"""
    def __init__(self, port=8887):  
        self.port = port
        self.running = False
        self.ffmpeg_process = None
        self.mediamtx_process = None
        self.app = Flask(__name__)
        self._initialized = False


        # 定义流媒体路由
        @self.app.route('/stream.m3u8')
        def stream_hls():
            # 使用当前工作目录
            return send_from_directory(os.getcwd(), 'stream.m3u8')

        @self.app.route('/<path:filename>')
        def send_file(filename):
            # 只提供.ts文件和.m3u8文件
            if filename.endswith('.ts') or filename.endswith('.m3u8'):
                return send_from_directory(os.getcwd(), filename)
            else:
                return "File not found", 404

        # 初始化时清理旧文件
        self.delete_stream_files()
        self._initialized = True


    def find_available_port(self, start_port=8887):
        """找到可用端口"""
        import socket
        for port in range(start_port, start_port + 10):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('', port))
                    return port
            except OSError:
                continue
        return None

    def start_http_server(self):
        """启动HTTP服务器，提供H.264视频流"""
        try:
            # 找到可用端口
            available_port = self.find_available_port(self.port)
            if available_port:
                self.port = available_port
                print(f"使用端口: {self.port}")

            self.app.run(host='0.0.0.0', port=self.port, debug=False, use_reloader=False)
        except Exception as e:
            print(f"HTTP服务器启动失败: {e}")
            raise


    def start_capture(self):
        """使用 FFmpeg 捕获屏幕内容并保存为 HLS 格式"""
        self.running = True

        os_name = platform.system()
        if os_name == "Windows":
            input_format = 'gdigrab'
            input_source = 'desktop'
        elif os_name == "Linux":
            input_format = 'x11grab'
            input_source = os.environ.get('DISPLAY', ':0.0')
        else:
            print(f"不支持的操作系统: {os_name}")
            self.stop()
            return

        command = [
            'ffmpeg',
            '-f', input_format,
            '-framerate', '25',
            '-i', input_source,
            '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2',
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-tune', 'zerolatency',
            '-pix_fmt', 'yuv420p',
            '-profile:v', 'baseline',
            '-level', '3.1',
            '-b:v', '2M',
            '-maxrate', '2M',
            '-bufsize', '4M',
            '-g', '50',
            '-keyint_min', '25',
            '-sc_threshold', '0',
            '-f', 'hls',
            '-hls_time', '2',
            '-hls_list_size', '5',
            '-hls_flags', 'delete_segments+independent_segments',
            '-hls_segment_type', 'mpegts',
            '-hls_start_number_source', 'datetime',
            'stream.m3u8'
        ]
        try:
            self.ffmpeg_process = subprocess.Popen(
                command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            print("屏幕流媒体编码器已启动")
            # 启动一个线程捕获 FFmpeg 的输出
            threading.Thread(target=self.handle_ffmpeg_output, args=(
                self.ffmpeg_process,), daemon=True).start()
        except Exception as e:
            print(f"启动FFmpeg失败: {str(e)}")
            self.stop()

    def handle_ffmpeg_output(self, process):
        for line in process.stderr:
            print(f"FFmpeg: {line.strip()}")

    def start(self):
        """启动流媒体服务和屏幕捕获"""
        try:
            if self.running:
                print("流媒体服务已在运行")
                return

            # 启动HTTP服务器
            http_thread = threading.Thread(target=self.start_http_server, daemon=True)
            http_thread.start()

            # 启动屏幕捕获
            self.start_capture()

        except Exception as e:
            print(f"启动流媒体服务失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止服务并删除生成的流文件"""
        if not self._initialized:
            return

        self.running = False

        if self.ffmpeg_process:
            try:
                self.ffmpeg_process.terminate()
                self.ffmpeg_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ffmpeg_process.kill()
            except Exception as e:
                print(f"停止FFmpeg进程失败: {e}")


        self.delete_stream_files()

    def delete_stream_files(self):
        """删除生成的流文件"""
        try:
            files = glob.glob('stream*')
            if files:
                for file in files:
                    os.remove(file)
                print(f"已删除生成的流文件: {files}")
            else:
                print("没有找到需要删除的流文件")
        except Exception as e:
            print(f"删除流文件失败: {str(e)}")

class ThreadManager(QObject):
    """线程管理器，用于安全管理线程生命周期"""
    def __init__(self):
        super().__init__()
        self.threads = []

    def add_thread(self, thread):
        """添加线程到管理器"""
        self.threads.append(thread)
        thread.finished.connect(lambda: self.remove_thread(thread))

    def remove_thread(self, thread):
        """从管理器中移除线程"""
        if thread in self.threads:
            self.threads.remove(thread)

    def cleanup_all(self):
        """清理所有线程"""
        for thread in self.threads[:]:  # 复制列表避免修改时出错
            try:
                if thread.isRunning():
                    thread.requestInterruption()
                    thread.quit()
                    # 不等待，避免死锁
            except:
                pass
        self.threads.clear()

# 全局线程管理器
_thread_manager = ThreadManager()

class DLNADeviceDiscoveryThread(QThread):
    """用于在后台发现DLNA设备的线程"""
    devices_found = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self._stop_requested = False
        # 注册到全局线程管理器
        _thread_manager.add_thread(self)

    def stop(self):
        """请求停止线程"""
        self._stop_requested = True
        self.requestInterruption()
        self.quit()

    def run(self):
        try:
            if self._stop_requested or self.isInterruptionRequested():
                return
            print("开始搜索DLNA设备...")
            upnp = upnpy.UPnP()
            if self._stop_requested or self.isInterruptionRequested():
                return
            devices = upnp.discover()
            if self._stop_requested or self.isInterruptionRequested():
                return
            valid_devices = [
                device for device in devices if 'AVTransport' in device.services]
            if not self._stop_requested and not self.isInterruptionRequested():
                self.devices_found.emit(valid_devices)
                print(f"设备搜索完成，发现 {len(valid_devices)} 个设备")
        except Exception as e:
            print(f"设备发现失败: {e}")
            if not self._stop_requested and not self.isInterruptionRequested():
                self.error_occurred.emit(str(e))
                self.devices_found.emit([])  # 发送空列表
        finally:
            # 确保线程正常退出
            print("设备发现线程结束")

class ScreenDLAN(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.streamer = None
        self.casting_devices = []
        self.devices = []
        self.discovery_thread = None
        self._is_closing = False  # 关闭状态标志
        self._initialized = False  # 初始化完成标志

        try:
            # 设置窗口标志 - 确保窗口独立且不会导致应用退出
            self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
            self.setAttribute(Qt.WA_DeleteOnClose, False)  # 关闭时不立即销毁，由主程序控制
            self.setAttribute(Qt.WA_QuitOnClose, False)  # 关闭时不退出应用程序

            # 确保应用程序不会因为这个窗口关闭而退出
            app = QApplication.instance()
            if app:
                app.setQuitOnLastWindowClosed(False)

            # 安全初始化流媒体服务
            try:
                print("开始初始化流媒体服务...")
                self.streamer = ScreenStreamer()
                print("流媒体服务初始化成功")
            except Exception as e:
                print(f"流媒体服务初始化失败: {e}")
                import traceback
                traceback.print_exc()
                self.streamer = None

            self.init_ui()
            self._initialized = True
            self.show()

        except Exception as e:
            print(f"ScreenDLAN 初始化失败: {e}")
            # 显示错误消息但不崩溃
            try:
                QMessageBox.critical(None, "初始化错误", f"投屏模块初始化失败:\n{e}")
            except:
                print("无法显示错误对话框")
   
    def init_ui(self):
        try:
            self.setWindowTitle('DLNA投屏控制')
            self.setGeometry(800, 600, 350, 250)
            
            layout = QVBoxLayout()

            # 状态标签
            self.status_label = QLabel('正在搜索设备...')
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")
            layout.addWidget(self.status_label)

            self.device_label = QLabel('选择要投屏的设备:')
            layout.addWidget(self.device_label)

            self.device_list = QListWidget()
            layout.addWidget(self.device_list)

            # 刷新设备按钮
            self.refresh_button = QPushButton('刷新设备')
            self.refresh_button.clicked.connect(self.refresh_devices)
            layout.addWidget(self.refresh_button)

            self.cast_button = QPushButton('投屏')
            self.cast_button.clicked.connect(self.start_casting)
            self.cast_button.setEnabled(False)  # 初始禁用，等设备发现完成
            layout.addWidget(self.cast_button)

            self.stop_button = QPushButton('停止')
            self.stop_button.clicked.connect(self.stop_casting)
            self.stop_button.setEnabled(False)
            layout.addWidget(self.stop_button)

            self.setLayout(layout)

            # 启动设备发现
            self.start_device_discovery()

        except Exception as e:
            print(f"init_ui 失败: {e}")
            try:
                QMessageBox.critical(self, "界面初始化错误", f"界面初始化失败:\n{e}")
            except:
                print("无法显示错误对话框")
    
    def start_device_discovery(self):
        """启动设备发现线程"""
        try:
            if self.discovery_thread and self.discovery_thread.isRunning():
                print("停止之前的设备发现线程...")
                self.discovery_thread.stop()
            self.discovery_thread = DLNADeviceDiscoveryThread()
            self.discovery_thread.devices_found.connect(self.populate_device_list)
            self.discovery_thread.error_occurred.connect(self.handle_discovery_error)
            self.discovery_thread.finished.connect(self.on_discovery_finished)
            self.discovery_thread.start()
            if hasattr(self, 'status_label'):
                self.status_label.setText('正在搜索设备...')
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        except Exception as e:
            print(f"启动设备发现失败: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f'设备发现启动失败: {e}')
                self.status_label.setStyleSheet("color: red; font-weight: bold;")

    def refresh_devices(self):
        """刷新设备列表"""
        self.device_list.clear()
        self.device_list.addItem("正在搜索...")
        self.cast_button.setEnabled(False)
        self.start_device_discovery()

    def on_discovery_finished(self):
        """设备发现完成"""
        if hasattr(self, 'status_label'):
            if hasattr(self, 'devices') and self.devices:
                self.status_label.setText(f'发现 {len(self.devices)} 个设备')
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.status_label.setText('未发现设备')
                self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def populate_device_list(self, devices):
        """填充设备列表"""
        try:
            self.devices = devices
            self.device_list.clear()

            if devices:
                for device in devices:
                    try:
                        device_name = getattr(device, 'friendly_name', '未知设备')
                        item = QListWidgetItem(device_name)
                        item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                        item.setCheckState(Qt.Unchecked)
                        self.device_list.addItem(item)
                    except Exception as e:
                        print(f"添加设备到列表失败: {e}")
                        self.device_list.addItem("设备信息错误")

                self.cast_button.setEnabled(True)
                print(f"发现 {len(devices)} 个DLNA设备")
            else:
                self.device_list.addItem("未发现设备")
                self.cast_button.setEnabled(False)
                print("未发现任何DLNA设备")

        except Exception as e:
            print(f"填充设备列表失败: {e}")
            self.device_list.clear()
            self.device_list.addItem("列表更新失败")
            self.cast_button.setEnabled(False)

    def handle_discovery_error(self, error_msg):
        """处理设备发现错误"""
        try:
            print(f"设备发现错误: {error_msg}")
            self.device_list.clear()
            self.device_list.addItem("设备发现失败")
            self.cast_button.setEnabled(False)

            if hasattr(self, 'status_label'):
                self.status_label.setText(f'设备发现失败: {error_msg}')
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
        except Exception as e:
            print(f"处理设备发现错误时出错: {e}")
    
    def get_device_info(self, dev):
        """获取设备信息"""
        try:
            base_url = dev.base_url
            control_url = dev.services["AVTransport"].control_url
            if not control_url.startswith('http://') and not control_url.startswith('https://'):
                control_url = f"{base_url}{control_url}"
            return {
                "name": dev.friendly_name,
                "control_url": control_url
            }
        except Exception as e:
            print(f"获取设备信息失败: {str(e)}")
            return None

    def build_soap_envelope(self, media_url):
        """构建SOAP请求"""
        metadata = f"""
        <DIDL-Lite xmlns:dc="http://purl.org/dc/elements/1.1/"
                   xmlns:upnp="urn:schemas-upnp-org:metadata-1-0/upnp/"
                   xmlns="urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/">
            <item id="1" parentID="0" restricted="0">
                <dc:title>Desktop Screen Cast</dc:title>
                <dc:creator>MASTAJI Screen Cast</dc:creator>
                <upnp:class>object.item.videoItem</upnp:class>
                <res protocolInfo="http-get:*:application/vnd.apple.mpegurl:DLNA.ORG_OP=01;DLNA.ORG_CI=0;DLNA.ORG_FLAGS=01500000000000000000000000000000"
                     resolution="1920x1080"
                     duration="00:00:00">{escape(media_url)}</res>
            </item>
        </DIDL-Lite>
        """

        return f"""
        <?xml version="1.0"?>
        <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"
                    s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
            <s:Body>
                <u:SetAVTransportURI xmlns:u="urn:schemas-upnp-org:service:AVTransport:1">
                    <InstanceID>0</InstanceID>
                    <CurrentURI>{escape(media_url)}</CurrentURI>
                    <CurrentURIMetaData>{escape(metadata)}</CurrentURIMetaData>
                </u:SetAVTransportURI>
            </s:Body>
        </s:Envelope>
        """

    def cast_to_device(self, device_info, media_url):
        """执行投屏操作"""
        try:
            headers = {
                'Content-Type': 'text/xml; charset="utf-8"',
                'SOAPAction': '"urn:schemas-upnp-org:service:AVTransport:1#SetAVTransportURI"'
            }
            body = self.build_soap_envelope(media_url)
            response = requests.post(
                device_info['control_url'],
                data=body,
                headers=headers,
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            print(f"投屏请求异常: {str(e)}")
            return False

    def play_control(self, device_info, command='Play'):
        """播放控制命令"""
        actions = {
            'Play': {'action': 'Play', 'params': '<Speed>1</Speed>'},
            'Stop': {'action': 'Stop', 'params': ''}
        }
        try:
            headers = {
                'Content-Type': 'text/xml; charset="utf-8"',
                'SOAPAction': f'"urn:schemas-upnp-org:service:AVTransport:1#{command}"'
            }
            body = f"""
            <?xml version="1.0"?>
            <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"
                        s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
                <s:Body>
                    <u:{command} xmlns:u="urn:schemas-upnp-org:service:AVTransport:1">
                        <InstanceID>0</InstanceID>
                        {actions[command]['params']}
                    </u:{command}>
            </s:Body>
            </s:Envelope>
            """
            response = requests.post(
                device_info['control_url'],
                data=body,
                headers=headers,
                timeout=3
            )
            return response.status_code == 200
        except Exception as e:
            print(f"控制命令{command}失败: {str(e)}")
            return False

    def get_local_ip(self):
        """获取本机有效IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip=s.getsockname()[0]
            s.close()
            return ip
        except:
            return socket.gethostbyname(socket.gethostname())

    def start_casting(self):
        """开始投屏"""
        try:
            if not self.streamer:
                QMessageBox.critical(self, "错误", "流媒体服务未初始化，无法投屏")
                return

            devices_to_cast = []
            for i in range(self.device_list.count()):
                item = self.device_list.item(i)
                if item and item.flags() & Qt.ItemIsUserCheckable:
                    if item.checkState() == Qt.Checked:
                        if i < len(self.devices):
                            devices_to_cast.append(self.devices[i])
            
            if not devices_to_cast:
                QMessageBox.warning(self, "警告", "请至少勾选一个投屏设备")
                return

            if hasattr(self, 'status_label'):
                self.status_label.setText('正在启动投屏...')
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            local_ip = self.get_local_ip()
            # 使用内置Flask服务器的URL
            media_url = f"http://{local_ip}:{self.streamer.port}/stream.m3u8"

    
            print(f"使用内置HTTP服务器: {media_url}")

            try:
                print(f"启动流媒体服务，URL: {media_url}")
                self.streamer.start()
                print("等待HLS流生成...")
                time.sleep(3)
            except Exception as e:
                print(f"启动流媒体服务失败: {e}")
                QMessageBox.critical(self, "错误", f"启动流媒体服务失败: {str(e)}")
                if hasattr(self, 'status_label'):
                    self.status_label.setText('流媒体服务启动失败')
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")
                return

            successful_casts = []
            failed_casts = []

            for device in devices_to_cast:
                device_info = self.get_device_info(device)
                if not device_info:
                    print(f"无法获取设备 {getattr(device, 'friendly_name', '未知设备')} 的信息")
                    failed_casts.append(getattr(device, 'friendly_name', '未知设备'))
                    continue

                print(f"尝试投屏到: {device_info['name']}...")
                if self.cast_to_device(device_info, media_url) and self.play_control(device_info):
                    print(f"投屏到 {device_info['name']} 成功！")
                    successful_casts.append(device)
                else:
                    print(f"投屏到 {device_info['name']} 失败。")
                    failed_casts.append(device_info['name'])
            
            self.casting_devices = successful_casts

            if self.casting_devices:
                self.cast_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                if hasattr(self, 'status_label'):
                    self.status_label.setText(f'投屏中 ({len(self.casting_devices)}个设备)...')
                    self.status_label.setStyleSheet("color: green; font-weight: bold;")
                
                success_msg = f"成功投屏到 {len(self.casting_devices)} 个设备。"
                if failed_casts:
                    fail_msg = f"\n以下设备投屏失败:\n" + "\n".join(failed_casts)
                    QMessageBox.information(self, "部分成功", success_msg + fail_msg)
                else:
                    QMessageBox.information(self, "成功", success_msg)
            else:
                print("所有设备投屏失败")
                QMessageBox.warning(self, "失败", "所有选中的设备都投屏失败，请检查网络和设备状态。")
                self.streamer.stop()
                if hasattr(self, 'status_label'):
                    self.status_label.setText('投屏失败')
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")

        except Exception as e:
            print(f"投屏过程中发生错误: {e}")
            QMessageBox.critical(self, "严重错误", f"投屏过程中发生错误: {str(e)}")
            try:
                if self.streamer:
                    self.streamer.stop()
            except:
                pass
            if hasattr(self, 'status_label'):
                self.status_label.setText(f'投屏错误: {e}')
                self.status_label.setStyleSheet("color: red; font-weight: bold;")

    def stop_casting(self):
        """停止投屏"""
        try:
            if hasattr(self, 'status_label'):
                self.status_label.setText('正在停止投屏...')
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            if self.casting_devices:
                print(f"正在停止 {len(self.casting_devices)} 个设备的投屏...")
                for device in self.casting_devices:
                    try:
                        device_info = self.get_device_info(device)
                        if device_info:
                            self.play_control(device_info, 'Stop')
                            print(f"设备 {device_info['name']} 播放已停止")
                    except Exception as e:
                        print(f"停止设备 {getattr(device, 'friendly_name', '未知')} 播放失败: {e}")
                
                self.casting_devices.clear()
            
            for i in range(self.device_list.count()):
                item = self.device_list.item(i)
                if item and item.flags() & Qt.ItemIsUserCheckable:
                    item.setCheckState(Qt.Unchecked)

            if self.streamer:
                try:
                    self.streamer.stop()
                    print("流媒体服务已停止")
                except Exception as e:
                    print(f"停止流媒体服务失败: {e}")

            self.cast_button.setEnabled(True)
            self.stop_button.setEnabled(False)

            if hasattr(self, 'status_label'):
                self.status_label.setText('投屏已停止')
                self.status_label.setStyleSheet("color: orange; font-weight: bold;")

        except Exception as e:
            print(f"停止投屏时出错: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.setText(f'停止失败: {e}')
                self.status_label.setStyleSheet("color: red; font-weight: bold;")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    screen_dlan = ScreenDLAN()
    screen_dlan.show()
    sys.exit(app.exec_())
