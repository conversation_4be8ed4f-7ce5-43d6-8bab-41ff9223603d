import sys
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
import qtawesome as fa  


class Whiteboard(QMainWindow):
    drawing_event = pyqtSignal(dict)
    collaboration_toggled = pyqtSignal(bool)

    def __init__(self, sio_manager=None, course_schedule_id=None, on_close_callback=None):
        super().__init__()
        self.drawing = False
        self.last_point = QPoint()
        self.pen_color = QColor(Qt.black)
        self.pen_width = 3
        self.sio_manager = sio_manager
        self.course_id = course_schedule_id
        self.on_close_callback = on_close_callback
        # 获取屏幕尺寸（高DPI适配）
        screen = QApplication.primaryScreen()
        self.screen_rect = screen.availableGeometry()
        self.screen_width = self.screen_rect.width()
        self.screen_height = self.screen_rect.height()
        # 初始化变量
        self.shapes = []  # 存储绘制的形状
        self.drawing = False  # 绘图状态
        self.pen_color = QColor(255, 0, 0)  # 默认画笔颜色为红色
        self.current_shape = "freehand"  # 当前绘制的形状
        self.fine = 5  # 线条粗细
        self.start_point = QPoint()  # 起始点
        self.end_point = QPoint()  # 结束点
        self.last_point = QPoint()  # 初始化 last_point
        self.isMenuExpanded = True  # 菜单状态，初始为展开状态

        self.eraser_points = []  # 初始化擦除路径
        self.eraser_size = 20  # 橡皮擦大小

        self.pixmap = QPixmap(self.screen_rect.size())  # 用于保存绘制内容
        self.pixmap.fill(Qt.transparent)  # 设置透明背景

        # Initialize remote pen properties
        self.remote_pen_color = QColor(Qt.black)
        self.remote_pen_width = 3
        self.remote_current_shape = "freehand"
        
        # 设置窗口属性
        # self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool |self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setWindowFlags(Qt.FramelessWindowHint |Qt.Tool | self.windowFlags())
        self.setAttribute(Qt.WA_TranslucentBackground)
        # self.setGeometry((self.screen_width - 800) // 2, self.screen_height - 140, 550, 200)
     
        # 主绘图层
        self.widget_main = QWidget(self)
        self.widget_main.setGeometry(0, 0, self.screen_width, self.screen_height)
        self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")
  
        # 底部标注菜单
        self.Draw_Menu = QWidget(self)
        self.Draw_Menu.setGeometry(
            (self.screen_width - 800) // 2, self.screen_height - 140, 550, 200)
        self.horizontal_layout = QHBoxLayout(self.Draw_Menu)

        # 菜单伸缩按钮
        self.menu_pushButton = QPushButton(fa.icon('fa5s.bars', color='white'), "", self.Draw_Menu)
        self.menu_pushButton.setFixedSize(50, 50)
        self.menu_pushButton.setIconSize(QSize(35, 35))
        self.menu_pushButton.clicked.connect(self.toggleMenu)
        self.horizontal_layout.addWidget(self.menu_pushButton)

        # 画笔按钮
        self.plotting_pushButton = QPushButton(fa.icon('fa5s.paint-brush', color='white'), "", self.Draw_Menu)
        self.plotting_pushButton.setFixedSize(50, 50)
        self.plotting_pushButton.setIconSize(QSize(35, 35))
        self.plotting_pushButton.clicked.connect(lambda: self.set_shape("freehand"))
        self.horizontal_layout.addWidget(self.plotting_pushButton)

        # 设置按钮
        self.settings_pushButton = QPushButton(fa.icon('fa5s.cog', color='white'), "", self.Draw_Menu)
        self.settings_pushButton.setFixedSize(50, 50)
        self.settings_pushButton.setIconSize(QSize(35, 35))
        self.settings_pushButton.clicked.connect(self.show_settings_menu)
        self.horizontal_layout.addWidget(self.settings_pushButton)

        # 设置菜单
        self.settings_menu = QMenu(self.Draw_Menu)
        self.settings_menu.addAction("选择颜色", self.openColorDialog)

        self.width_menu = QMenu("选择笔粗细", self.settings_menu)
        for i in range(1, 11):
            self.width_menu.addAction(f"笔宽 {i}", lambda w=i: self.set_fine(w))
        self.settings_menu.addMenu(self.width_menu)

        self.shape_menu = QMenu("形状选择", self.settings_menu)
        self.shape_menu.addAction("手绘", lambda: self.set_shape("freehand"))
        self.shape_menu.addAction("直线", lambda: self.set_shape("line"))
        self.shape_menu.addAction("矩形", lambda: self.set_shape("rectangle"))
        self.shape_menu.addAction("圆形", lambda: self.set_shape("circle"))
        self.shape_menu.addAction("三角形", lambda: self.set_shape("triangle"))
        self.settings_menu.addMenu(self.shape_menu)

        # 擦除按钮
        self.eraser_pushButton = QPushButton(fa.icon('fa5s.eraser', color='white'), "", self.Draw_Menu)
        self.eraser_pushButton.setFixedSize(50, 50)
        self.eraser_pushButton.setIconSize(QSize(35, 35))
        self.eraser_pushButton.clicked.connect(
            lambda: self.set_shape("eraser"))
        self.horizontal_layout.addWidget(self.eraser_pushButton)

        # 撤销按钮
        self.revoke_pushButton = QPushButton(fa.icon('fa5s.undo', color='white'), "", self.Draw_Menu)
        self.revoke_pushButton.setFixedSize(50, 50)
        self.revoke_pushButton.setIconSize(QSize(35, 35))
        self.revoke_pushButton.clicked.connect(self.undoLastDrawing)
        self.horizontal_layout.addWidget(self.revoke_pushButton)

        # 清屏按钮
        self.Clear_pushButton = QPushButton(fa.icon('fa5s.trash-alt', color='white'), "", self.Draw_Menu)
        self.Clear_pushButton.setFixedSize(50, 50)
        self.Clear_pushButton.setIconSize(QSize(35, 35))
        self.Clear_pushButton.clicked.connect(self.erasureClick)
        self.horizontal_layout.addWidget(self.Clear_pushButton)

        # 协作按钮
        self.collaboration_button = QPushButton(fa.icon('fa5s.users', color='white'), "", self.Draw_Menu)
        self.collaboration_button.setFixedSize(50, 50)
        self.collaboration_button.setIconSize(QSize(35, 35))
        self.collaboration_button.setCheckable(True)
        self.collaboration_button.toggled.connect(self.toggle_collaboration)
        self.horizontal_layout.addWidget(self.collaboration_button)
        
        # 绘制窗体关闭
        self.ppt_pushButton_close = QPushButton(fa.icon('fa5s.times', color='red'), "", self.Draw_Menu)
        self.ppt_pushButton_close.setFixedSize(50, 50)
        self.ppt_pushButton_close.setIconSize(QSize(35, 35))
        self.ppt_pushButton_close.clicked.connect(self.exit_Whiteboard)
        self.horizontal_layout.addWidget(self.ppt_pushButton_close)

        # 添加统一的样式
        self.Draw_Menu.setStyleSheet("""
            QPushButton {
                background-color: rgba(85, 85, 85, 0);
                border: 0px;
            }
            QPushButton:hover {
                background-color: rgba(100, 100, 100, 100);
                border-radius: 5px;
            }
        """)
        # —————————————————————————————————————————————————————————————————————————

    def toggleMenu(self):
        """切换菜单状态（优化穿透版）"""
        # 切换其他按钮的可见性
        for i in range(1, self.horizontal_layout.count()):
            widget = self.horizontal_layout.itemAt(i).widget()
            widget.setVisible(not widget.isVisible())

        if self.isMenuExpanded:
            # 收缩状态：关闭透明层和主图层
            self.widget_main.hide()  # 隐藏主图层
            
            self.setAttribute(Qt.WA_TransparentForMouseEvents, True)  # 设置穿透
            
            # 确保收缩按钮保持可点击
            self.menu_pushButton.setAttribute(Qt.WA_TransparentForMouseEvents, False)
            self.menu_pushButton.raise_()
        else:
            # 展开状态：恢复透明层和主图层
            self.widget_main.show()
            self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")
            self.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        
        # 保持窗口置顶
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.show()
        self.isMenuExpanded = not self.isMenuExpanded

    def toggle_collaboration(self, checked):
        """切换协作模式"""
        self.collaboration_toggled.emit(checked)
        if checked:
            self.collaboration_button.setStyleSheet("background-color: rgba(0, 255, 0, 100); border-radius: 5px;")
        else:
            self.collaboration_button.setStyleSheet("")

    def show_settings_menu(self):
        """显示设置菜单"""
        pos = self.settings_pushButton.mapToGlobal(
            self.settings_pushButton.rect().topLeft())
        pos.setY(pos.y() - self.settings_menu.sizeHint().height())
        self.settings_menu.exec_(pos)

    def set_fine(self, fine):
        """设置画笔粗细"""
        self.fine = fine

    def openColorDialog(self):
        """打开颜色选择对话框"""
        color = QColorDialog.getColor(self.pen_color, self, "选择颜色")
        if color.isValid():
            self.pen_color = color

    def set_shape(self, shape):
        """设置当前绘制的形状"""
        self.current_shape = shape

    def erasureClick(self):
        """清空所有绘制内容"""
        self.shapes.clear()
        self.pixmap.fill(Qt.transparent)  # 清空 QPixmap
        self.update()
        self._sync_state_to_clients()

    def undoLastDrawing(self):
        """撤销最后一次绘制"""
        if self.shapes:
            self.shapes.pop()
            self.pixmap.fill(Qt.transparent)  # 清空 QPixmap
            # 重新绘制所有形状
            painter = QPainter(self.pixmap)
            for shape in self.shapes:
                self.draw_shape(painter, shape)
            painter.end()  # 确保QPainter正确关闭
            self.update()
            self._sync_state_to_clients()

    def draw_shape(self, painter, shape):
        """绘制单个形状"""
        pen = QPen(shape["color"], shape["fine"], Qt.SolidLine, Qt.RoundCap)
        painter.setPen(pen)

        if shape["shape"] == "line":
            painter.drawLine(shape["start"], shape["end"])
        elif shape["shape"] == "rectangle":
            # 正确创建矩形：使用左上角和右下角坐标
            x1, y1 = shape["start"].x(), shape["start"].y()
            x2, y2 = shape["end"].x(), shape["end"].y()
            rect = QRect(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))
            painter.drawRect(rect)
        elif shape["shape"] == "triangle":
            polygon = QPolygon(shape["points"])
            painter.drawPolygon(polygon)
        elif shape["shape"] == "circle":
            radius = int((shape["end"] - shape["start"]).manhattanLength() / 2)
            center = shape["start"]
            painter.drawEllipse(center, radius, radius)
        elif shape["shape"] == "freehand":
            for i in range(1, len(shape["points"])):
                painter.drawLine(shape["points"][i - 1], shape["points"][i])

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drawing = True
            self.start_point = event.pos()
            if self.current_shape == "freehand":
                self.points = [event.pos()]
            elif self.current_shape == "eraser":
                self.eraser_points = [event.pos()]  # 初始化擦除路径

            event_data = {
                'type': 'mouse_press',
                'course_id': self.course_id,
                'data': {
                    'x': self.start_point.x(),
                    'y': self.start_point.y(),
                    'color': self.pen_color.name(),  # Add pen color
                    'fine': self.fine,  # Add pen width
                    'shape': self.current_shape  # Add current shape
                }
            }
            self.drawing_event.emit(event_data)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() & Qt.LeftButton and self.drawing:
            self.end_point = event.pos()
            if self.current_shape == "freehand":
                self.points.append(event.pos())
            elif self.current_shape == "eraser":
                # 在 QPixmap 上擦除
                painter = QPainter(self.pixmap)
                painter.setCompositionMode(
                    QPainter.CompositionMode_Clear)  # 使用透明模式擦除
                painter.setPen(
                    QPen(Qt.transparent, self.eraser_size, Qt.SolidLine, Qt.RoundCap))
                painter.drawLine(self.last_point, event.pos())
                painter.end()  # 确保QPainter正确关闭
            self.last_point = event.pos()
            self.update()  # 触发 paintEvent

            event_data = {
                'type': 'mouse_move',
                'course_id': self.course_id,
                'data': {
                    'x': self.end_point.x(),
                    'y': self.end_point.y(),
                    'color': self.pen_color.name(),  # Add pen color
                    'fine': self.fine,  # Add pen width
                    'shape': self.current_shape  # Add current shape
                }
            }
            self.drawing_event.emit(event_data)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.drawing:
            self.drawing = False
            shape = None

            if self.current_shape == "line":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "rectangle":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "triangle":
                mid_x = (self.start_point.x() + self.end_point.x()) / 2
                side_length = abs(self.end_point.x() - self.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.start_point.x(), self.end_point.y()),
                    QPoint(self.end_point.x(), self.end_point.y()),
                    QPoint(int(mid_x), self.end_point.y() - height)
                ]
                shape = {
                    "shape": self.current_shape,
                    "points": points,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "circle":
                shape = {
                    "shape": self.current_shape,
                    "start": self.start_point,
                    "end": self.end_point,
                    "color": self.pen_color,
                    "fine": self.fine
                }
            elif self.current_shape == "freehand":
                shape = {
                    "shape": self.current_shape,
                    "points": self.points[:],
                    "color": self.pen_color,
                    "fine": self.fine
                }

            if shape:
                self.shapes.append(shape)
                # 将形状绘制到 QPixmap 中
                painter = QPainter(self.pixmap)
                self.draw_shape(painter, shape)
                painter.end()  # 确保QPainter正确关闭
            self.update()
            event_data = {
                'type': 'mouse_release',
                'course_id': self.course_id,
                'data': {
                    'x': self.end_point.x(),
                    'y': self.end_point.y(),
                    'color': self.pen_color.name(),  # Add pen color
                    'fine': self.fine,  # Add pen width
                    'shape': self.current_shape  # Add current shape
                }
            }
            self.drawing_event.emit(event_data)
            self._sync_state_to_clients()

    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.drawPixmap(0, 0, self.pixmap)

        # 绘制当前实时预览形状
        if self.drawing:
            pen = QPen(self.pen_color, self.fine, Qt.SolidLine, Qt.RoundCap)
            painter.setPen(pen)

            if self.current_shape == "line":
                painter.drawLine(self.start_point, self.end_point)
            elif self.current_shape == "rectangle":
                # 正确创建矩形：使用左上角和右下角坐标
                x1, y1 = self.start_point.x(), self.start_point.y()
                x2, y2 = self.end_point.x(), self.end_point.y()
                rect = QRect(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))
                painter.drawRect(rect)
            elif self.current_shape == "triangle":
                mid_x = (self.start_point.x() + self.end_point.x()) / 2
                side_length = abs(self.end_point.x() - self.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.start_point.x(), self.end_point.y()),
                    QPoint(self.end_point.x(), self.end_point.y()),
                    QPoint(int(mid_x), self.end_point.y() - height)
                ]
                painter.drawPolygon(QPolygon(points))
            elif self.current_shape == "circle":
                radius = int(
                    (self.end_point - self.start_point).manhattanLength() / 2)
                center = self.start_point
                painter.drawEllipse(center, radius, radius)
            elif self.current_shape == "freehand" and hasattr(self, 'points') and len(self.points) > 1:
                for i in range(1, len(self.points)):
                    painter.drawLine(self.points[i - 1], self.points[i])

    def resizeEvent(self, event):
        if self.width() > self.pixmap.width() or self.height() > self.pixmap.height():
            new_pixmap = QPixmap(self.size())
            new_pixmap.fill(Qt.transparent)
            painter = QPainter(new_pixmap)
            painter.drawPixmap(QPoint(0, 0), self.pixmap)
            painter.end()  # 确保QPainter正确关闭
            self.pixmap = new_pixmap

    def handle_remote_drawing_event(self, data):
        event_type = data.get('type')
        course_id = data.get('course_id')
        drawing_data = data.get('data')

        if course_id != self.course_id:
            return

        # 避免与教师自己的绘图冲突
        if self.drawing:
            return

        if event_type == "mouse_press":
            self.last_point = QPoint(drawing_data['x'], drawing_data['y'])
            # Store remote pen properties
            self.remote_pen_color = QColor(drawing_data.get('color', Qt.black))
            self.remote_pen_width = drawing_data.get('fine', 3)
            self.remote_current_shape = drawing_data.get('shape', "freehand")
        elif event_type == "mouse_move":
            point = QPoint(drawing_data['x'], drawing_data['y'])
            painter = QPainter(self.pixmap)
            painter.setPen(QPen(self.remote_pen_color, self.remote_pen_width, Qt.SolidLine, Qt.RoundCap, Qt.RoundJoin))
            painter.drawLine(self.last_point, point)
            painter.end()  # 确保QPainter正确关闭
            self.last_point = point
            self.update()
        elif event_type == "mouse_release":
            # 不需要特殊处理，因为小组端会发送完整状态
            pass


    def exit_Whiteboard(self):
        """关闭当前实例（窗口）"""
        self.close()

    def closeEvent(self, event):
        """重写关闭事件，以更新主窗口UI"""
        if self.on_close_callback:
            self.on_close_callback()
        super().closeEvent(event)

    def on_student_drawing_received(self, data):
        """处理接收到的学生绘图数据"""
        student_id = data.get('student_id')
        shape_data = data.get('shape')

        if not shape_data:
            return

        # 反序列化 shape 数据
        qt_shape = shape_data.copy()
        qt_shape['color'] = QColor(qt_shape['color'])
        
        if 'start' in qt_shape and 'end' in qt_shape:
            qt_shape['start'] = QPoint(qt_shape['start']['x'], qt_shape['start']['y'])
            qt_shape['end'] = QPoint(qt_shape['end']['x'], qt_shape['end']['y'])
        
        if 'points' in qt_shape:
            qt_shape['points'] = [QPoint(p['x'], p['y']) for p in qt_shape['points']]

        # 将新形状添加到列表中
        self.shapes.append(qt_shape)

        # 在pixmap上绘制新形状
        painter = QPainter(self.pixmap)
        self.draw_shape(painter, qt_shape)
        painter.end()  # 确保QPainter正确关闭

        # 更新UI
        self.update()

        # 将合并了学生笔迹的最新状态同步给所有人
        self._sync_state_to_clients()

    def _sync_state_to_clients(self):
        """将当前白板状态同步到所有客户端"""
        if not self.sio_manager or not self.sio_manager.is_connected():
            print("SocketIO not connected, cannot sync whiteboard state.")
            return

        # 将Qt对象转换为可序列化的字典
        serializable_shapes = []
        for shape in self.shapes:
            s_shape = shape.copy()
            # 转换颜色
            s_shape['color'] = s_shape['color'].name() # #RRGGBB
            
            # 转换点
            if 'start' in s_shape and 'end' in s_shape:
                s_shape['start'] = {'x': s_shape['start'].x(), 'y': s_shape['start'].y()}
                s_shape['end'] = {'x': s_shape['end'].x(), 'y': s_shape['end'].y()}
            
            if 'points' in s_shape:
                s_shape['points'] = [{'x': p.x(), 'y': p.y()} for p in s_shape['points']]
            
            serializable_shapes.append(s_shape)

        payload = {
            'shapes': serializable_shapes,
            'course_schedule_id': self.course_id
        }
        
        # 通过sio_manager发送事件
        self.sio_manager.emit_whiteboard_sync(payload)

    def handle_full_state_update(self, data):
        """处理从单个小组端接收到的完整白板状态"""
        shapes_data = data.get('shapes')
        if shapes_data is None: # 注意要检查None，因为空列表是有效状态
            return

        # 清空当前形状
        self.shapes.clear()

        # 反序列化并加载新形状
        for shape_data in shapes_data:
            qt_shape = shape_data.copy()
            qt_shape['color'] = QColor(qt_shape['color'])
            
            if 'start' in qt_shape and 'end' in qt_shape:
                qt_shape['start'] = QPoint(qt_shape['start']['x'], qt_shape['start']['y'])
                qt_shape['end'] = QPoint(qt_shape['end']['x'], qt_shape['end']['y'])
            
            if 'points' in qt_shape:
                qt_shape['points'] = [QPoint(p['x'], p['y']) for p in qt_shape['points']]
            
            self.shapes.append(qt_shape)

        # 重绘整个pixmap
        self.pixmap.fill(Qt.transparent)
        painter = QPainter(self.pixmap)
        for shape in self.shapes:
            self.draw_shape(painter, shape)
        painter.end()  # 确保QPainter正确关闭

        # 更新UI
        self.update()

        # 将接收到的状态再次广播给所有其他客户端，实现同步
        self._sync_state_to_clients()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Whiteboard()
    window.showFullScreen()
    sys.exit(app.exec_())