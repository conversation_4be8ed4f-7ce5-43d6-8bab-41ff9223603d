# teacher/ui/chat_widget.py
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTextEdit, QLineEdit, QPushButton, QHBoxLayout, QMessageBox
from PyQt5.QtCore import pyqtSignal, Qt
import requests

class ChatWidget(QWidget):
    send_message_signal = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.initUI()

    def initUI(self):
        """初始化聊天界面的UI组件"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 用于显示消息的文本框
        self.messages_view = QTextEdit()
        self.messages_view.setReadOnly(True)
        self.messages_view.setStyleSheet("""
            QTextEdit {
                background-color: #f9f9f9;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                font-size: 14px;
                padding: 5px;
            }
        """)
        layout.addWidget(self.messages_view)

        # 输入和发送区域
        input_layout = QHBoxLayout()
        self.message_input = QLineEdit()
        self.message_input.setPlaceholderText("在此输入消息...")
        self.message_input.returnPressed.connect(self.send_message) # 支持按回车键发送

        self.send_button = QPushButton("发送")
        self.send_button.clicked.connect(self.send_message)

        input_layout.addWidget(self.message_input)
        input_layout.addWidget(self.send_button)

        layout.addLayout(input_layout)

        self.setLayout(layout)

    def send_message(self):
        """处理发送按钮点击事件"""
        message = self.message_input.text().strip()
        if message:
            self.send_message_signal.emit(message)
            self.message_input.clear()

    def add_message(self, data):
        """
        在UI上显示一条新消息。
        data 格式: {'user': '...', 'user_type': '...', 'message': '...'}
        """
        user = data.get('user', '匿名')
        user_type = data.get('user_type', 'unknown')
        message = data.get('message', '')

        # 根据用户类型设置颜色
        if user_type == 'teacher':
            color = '#1E9FFF' # 蓝色
        elif user_type == 'student':
            color = '#5FB878' # 绿色
        elif user_type == 'system':
            color = '#FF5722' # 橙色
        else:
            color = '#333333' # 默认黑色

        formatted_message = f'<b style="color: {color};">{user}:</b> {message}'
        self.messages_view.append(formatted_message)

    def set_enabled_state(self, is_enabled):
        """启用或禁用聊天输入"""
        self.message_input.setEnabled(is_enabled)
        self.send_button.setEnabled(is_enabled)
        if not is_enabled:
            self.message_input.setPlaceholderText("请先开始一门课程以激活聊天")
            self.messages_view.clear()
        else:
            self.message_input.setPlaceholderText("在此输入消息...")
            self.load_chat_history()

    def load_chat_history(self):
        """加载聊天历史记录"""
        if not self.main_window or not hasattr(self.main_window, 'current_course_schedule_id'):
            QMessageBox.warning(self, "提示", "无法获取课程信息")
            return

        if not self.main_window.current_course_schedule_id:
            return

        try:
            headers = {'Authorization': f'Bearer {self.main_window.token}'}
            response = requests.get(
                f"{self.main_window.api_url}/api/course/{self.main_window.current_course_schedule_id}/chat_history",
                headers=headers,
                verify=False
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    messages = data.get('messages', [])

                    # 清空现有消息
                    self.messages_view.clear()

                    # 显示历史消息
                    for msg in messages:
                        self.add_message(msg)

                else:
                    QMessageBox.warning(self, "失败", data.get('message', '加载失败'))
            else:
                QMessageBox.warning(self, "错误", f"请求失败: {response.status_code}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载聊天历史失败: {str(e)}")