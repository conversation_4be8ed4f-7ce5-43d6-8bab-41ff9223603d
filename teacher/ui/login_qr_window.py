"""
登录二维码窗口
"""
import json
import qrcode
import io
import time
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QWidget)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont


class LoginQRWindow(QDialog):
    """登录二维码窗口"""
    
    def __init__(self, parent=None, teacher_id=None, token=None):
        super().__init__(parent)
        self.teacher_id = teacher_id
        self.token = token
        
        self.setWindowTitle("客户端登录二维码")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        self.setup_ui()
        self.generate_login_qr()
        
        # 定时刷新二维码（每30秒）
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.generate_login_qr)
        self.refresh_timer.start(30000)  # 30秒刷新一次
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("扫码登录客户端")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #333; margin-bottom: 20px;")
        layout.addWidget(title_label)
        
        # 二维码显示区域
        self.qr_code_label = QLabel()
        self.qr_code_label.setFixedSize(300, 300)
        self.qr_code_label.setStyleSheet("""
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 10px;
        """)
        self.qr_code_label.setAlignment(Qt.AlignCenter)
        self.qr_code_label.setText("正在生成二维码...")
        layout.addWidget(self.qr_code_label, 0, Qt.AlignCenter)
        
        
        # 状态标签
        self.status_label = QLabel("二维码有效期：30秒")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #999; font-size: 12px;")
        layout.addWidget(self.status_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新二维码")
        refresh_btn.clicked.connect(self.generate_login_qr)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(refresh_btn)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def generate_login_qr(self):
        """生成登录二维码"""
        try:
            # 生成登录二维码数据
            qr_data = {
                "type": "client_login",
                "teacher_id": self.teacher_id,
                "token": self.token,
                "timestamp": int(time.time()),
                "expires_in": 300  # 5分钟有效期
            }
            
            # 生成二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(json.dumps(qr_data))
            qr.make(fit=True)
            
            # 创建二维码图片
            img = qr.make_image(fill_color="black", back_color="white")
            
            # 转换为QPixmap
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)
            
            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())
            
            # 缩放到合适大小
            scaled_pixmap = pixmap.scaled(280, 280, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.qr_code_label.setPixmap(scaled_pixmap)
            
        except ImportError:
            self.qr_code_label.setText("缺少二维码生成库\n请安装 qrcode 和 Pillow")
            self.status_label.setText("错误：缺少必要的库")
        except Exception as e:
            self.qr_code_label.setText(f"生成失败\n{str(e)}")
            self.status_label.setText("错误：生成二维码失败")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.refresh_timer:
            self.refresh_timer.stop()
        super().closeEvent(event)
