"""
签到管理窗口
"""
import json
import qrcode
import io
import requests
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QListWidget, QListWidgetItem, 
                            QMessageBox, QWidget, QSplitter)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap


class AttendanceWindow(QDialog):
    """签到管理窗口"""
    
    def __init__(self, parent=None, api_url=None, token=None):
        super().__init__(parent)
        self.parent_app = parent
        self.api_url = api_url
        self.token = token
        self.current_course_schedule_id = None
        
        self.setWindowTitle("签到管理")
        self.setFixedSize(800, 600)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        self.setup_ui()
        
        # 定时刷新学生列表
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_attendance_data)
        self.refresh_timer.start(30000)  # 30秒刷新一次
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：二维码区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 二维码标题
        qr_title = QLabel("签到二维码")
        qr_title.setAlignment(Qt.AlignCenter)
        qr_title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        left_layout.addWidget(qr_title)
        
        # 二维码显示区域
        self.qr_code_label = QLabel()
        self.qr_code_label.setFixedSize(200, 200)
        self.qr_code_label.setStyleSheet("border: 2px solid #ccc; background: #f9f9f9;")
        self.qr_code_label.setAlignment(Qt.AlignCenter)
        self.qr_code_label.setText("点击下方按钮\n生成签到二维码")
        left_layout.addWidget(self.qr_code_label)
        
        # 生成二维码按钮
        self.generate_qr_btn = QPushButton("生成签到二维码")
        self.generate_qr_btn.clicked.connect(self.generate_attendance_qr)
        self.generate_qr_btn.setStyleSheet("""
            QPushButton {
                background-color: #009688;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #00796b;
            }
        """)
        left_layout.addWidget(self.generate_qr_btn)
        
        # 二维码说明
        qr_info = QLabel("学生可以扫描此二维码进行签到")
        qr_info.setAlignment(Qt.AlignCenter)
        qr_info.setStyleSheet("color: #666; font-size: 12px; margin-top: 10px;")
        left_layout.addWidget(qr_info)
        
        left_layout.addStretch()
        
        # 右侧：学生列表区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 学生列表标题和控制按钮
        list_header = QHBoxLayout()
        list_title = QLabel("学生签到列表")
        list_title.setStyleSheet("font-size: 16px; font-weight: bold;")
        list_header.addWidget(list_title)
        list_header.addStretch()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新列表")
        self.refresh_btn.clicked.connect(self.load_attendance_data)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        list_header.addWidget(self.refresh_btn)
        
        right_layout.addLayout(list_header)
        
        # 学生列表
        self.attendance_list_widget = QListWidget()
        self.attendance_list_widget.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
            }
        """)
        right_layout.addWidget(self.attendance_list_widget)
        
        # 手动签到按钮
        self.manual_checkin_btn = QPushButton("手动签到选中学生")
        self.manual_checkin_btn.clicked.connect(self.manual_checkin)
        self.manual_checkin_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        right_layout.addWidget(self.manual_checkin_btn)
        
        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([300, 500])  # 设置初始比例
        
        layout.addWidget(splitter)
        
        # 底部状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("请先选择并开始一门课程")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        status_layout.addWidget(close_btn)
        
        layout.addLayout(status_layout)
    
    def set_course_info(self, course_schedule_id):
        """设置当前课程信息"""
        self.current_course_schedule_id = course_schedule_id
        if course_schedule_id:
            self.status_label.setText(f"当前课程ID: {course_schedule_id}")
            self.generate_qr_btn.setEnabled(True)
            self.load_attendance_data()
        else:
            self.status_label.setText("请先选择并开始一门课程")
            self.generate_qr_btn.setEnabled(False)
            self.qr_code_label.setText("点击下方按钮\n生成签到二维码")
            self.qr_code_label.setPixmap(QPixmap())
            self.attendance_list_widget.clear()
    
    def generate_attendance_qr(self):
        """生成签到二维码"""
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先选择并开始一门课程。")
            return
        
        try:
            qr_data = {
                "type": "attendance",
                "course_schedule_id": self.current_course_schedule_id,
                "timestamp": int(__import__('time').time())
            }
            
            # 生成二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(json.dumps(qr_data))
            qr.make(fit=True)
            
            # 创建二维码图片
            img = qr.make_image(fill_color="black", back_color="white")
            
            # 转换为QPixmap
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)
            
            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())
            
            # 缩放到合适大小
            scaled_pixmap = pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.qr_code_label.setPixmap(scaled_pixmap)
            
            self.status_label.setText("签到二维码已生成，学生可以扫码签到")
            
        except ImportError:
            QMessageBox.critical(self, "错误", "缺少二维码生成库，请安装 qrcode 和 Pillow 库。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成二维码失败: {str(e)}")
    
    def load_attendance_data(self):
        """加载签到数据"""
        if not self.current_course_schedule_id or not self.token:
            return
        
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            r = requests.get(f"{self.api_url}/teacher/get_attendance_status/{self.current_course_schedule_id}", 
                           headers=headers, verify=False)
            
            if r.status_code == 200:
                data = r.json()
                if data.get("status") == "success":
                    self.attendance_list_widget.clear()
                    students = data.get("students", [])
                    
                    signed_count = 0
                    total_count = len(students)
                    
                    for student in students:
                        item_text = f"{student['name']} ({student['student_id']})"
                        if student['signed_in']:
                            item_text += " - 已签到"
                            signed_count += 1
                        else:
                            item_text += " - 未签到"
                        
                        item = QListWidgetItem(item_text)
                        item.setData(Qt.UserRole, student)
                        
                        if student['signed_in']:
                            item.setForeground(Qt.green)
                        else:
                            item.setForeground(Qt.red)
                        
                        self.attendance_list_widget.addItem(item)
                    
                    self.status_label.setText(f"签到统计: {signed_count}/{total_count} 人已签到")
                else:
                    self.status_label.setText(f"加载失败: {data.get('message', '未知错误')}")
            else:
                self.status_label.setText(f"网络错误: {r.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.status_label.setText(f"连接失败: {str(e)}")
        except Exception as e:
            self.status_label.setText(f"加载错误: {str(e)}")
    
    def manual_checkin(self):
        """手动签到选中学生"""
        current_item = self.attendance_list_widget.currentItem()
        if not current_item:
            QMessageBox.warning(self, "提示", "请先选择一个学生。")
            return
        
        student = current_item.data(Qt.UserRole)
        if student['signed_in']:
            QMessageBox.information(self, "提示", "该学生已经签到。")
            return
        
        student_id = student['student_id']
        
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            payload = {'student_id': student_id}
            r = requests.post(f"{self.api_url}/teacher/manual_checkin/{self.current_course_schedule_id}", 
                            headers=headers, data=payload, verify=False)

            if r.status_code == 200:
                data = r.json()
                if data.get("status") == "success":
                    QMessageBox.information(self, "成功", data.get("message", "签到成功！"))
                    self.load_attendance_data()  # 刷新列表
                else:
                    QMessageBox.warning(self, "签到失败", data.get("message", "签到失败。"))
            else:
                QMessageBox.warning(self, "错误", f"签到请求失败: {r.text}")
                
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "网络错误", f"连接服务器失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.refresh_timer:
            self.refresh_timer.stop()
        super().closeEvent(event)
