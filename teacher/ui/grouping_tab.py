# teacher/ui/grouping_tab.py
import random
from functools import partial

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                             QFrame, QSplitter, QGridLayout, QMessageBox, QListWidgetItem)
from PyQt5.QtCore import Qt

from .widgets import DraggableListWidget, VideoFrame

class GroupingTab(QWidget):
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.group_panels = {}
        self.assigned_students = set()
        self.group_sharing_status = {}
        self.group_grid_position = (0, 0)
        self.grid_columns = 4

        self.initUI()

    def initUI(self):
        layout = QVBoxLayout(self)
        splitter = QSplitter(Qt.Horizontal)
        
        unassigned_frame = QFrame()
        unassigned_layout = QVBoxLayout()
        unassigned_layout.addWidget(QLabel("未分配的学生"))
        self.unassigned_students_list = DraggableListWidget()
        unassigned_layout.addWidget(self.unassigned_students_list)
        unassigned_frame.setLayout(unassigned_layout)
        splitter.addWidget(unassigned_frame)
        
        self.groups_area = QWidget()
        self.groups_layout = QGridLayout()
        self.groups_area.setLayout(self.groups_layout)
        splitter.addWidget(self.groups_area)
        
        splitter.setSizes([200, 900])
        layout.addWidget(splitter)
        
        buttons_layout = QHBoxLayout()
        
        self.random_assign_btn = QPushButton("随机分组")
        self.random_assign_btn.clicked.connect(self.randomly_assign_students)
        buttons_layout.addWidget(self.random_assign_btn)

        self.boot_all_btn = QPushButton("全部开机")
        self.boot_all_btn.clicked.connect(self.main_window.boot_all_groups)
        buttons_layout.addWidget(self.boot_all_btn)

        buttons_layout.addStretch()
        self.cancel_selection_btn = QPushButton("取消选中")
        self.split_screen_btn = QPushButton("分屏对比")
        self.cancel_selection_btn.setVisible(False)
        self.split_screen_btn.setVisible(False)
        self.cancel_selection_btn.clicked.connect(self.clear_all_selections)
        self.split_screen_btn.clicked.connect(self.apply_split_screen_layout)
        buttons_layout.addWidget(self.cancel_selection_btn)
        buttons_layout.addWidget(self.split_screen_btn)

        self.save_groups_btn = QPushButton("保存分组")
        self.save_groups_btn.clicked.connect(self.save_groups)
        buttons_layout.addWidget(self.save_groups_btn)

        self.load_groups_btn = QPushButton("加载已保存分组")
        self.load_groups_btn.clicked.connect(self.load_saved_groups)
        buttons_layout.addWidget(self.load_groups_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def reset_state(self):
        self.unassigned_students_list.clear()
        for ip in list(self.group_panels.keys()):
            panel = self.group_panels.pop(ip)
            if panel and panel.get('frame'):
                panel['frame'].setParent(None)
        self.assigned_students.clear()
        self.group_grid_position = (0, 0)
        self.update_dynamic_buttons()

    def populate_unassigned_list(self):
        """Populates the list of unassigned students."""
        self.unassigned_students_list.clear()
        all_students = self.main_window.all_students
        for sid, sdata in all_students.items():
            if sid not in self.assigned_students:
                display_name = sdata.get('name', sdata.get('username', sid))
                item = QListWidgetItem(display_name)
                item.setData(Qt.UserRole, sdata)
                self.unassigned_students_list.addItem(item)

    def handle_group_status_update(self, group_info):
        """Handles updates from the UDP listener."""
        ip = group_info.get("ip")
        if not ip: return
        
        if ip not in self.group_panels:
            self.add_group_panel(group_info)
        
        panel = self.group_panels.get(ip)
        if not panel: return

        is_sharing = group_info.get("is_sharing", False)
        was_sharing = self.group_sharing_status.get(ip, False)
        
        if is_sharing and not was_sharing:
            stream_name = group_info.get("stream_name")
            if stream_name:
                panel['video'].play(f"{self.main_window.rtsp_url}/{stream_name}")
                panel['broadcast_btn'].setDisabled(False)
        elif not is_sharing and was_sharing:
            panel['video'].stop()
            panel['broadcast_btn'].setDisabled(True)
            
        self.group_sharing_status[ip] = is_sharing

    def get_online_groups(self):
        """获取当前在线的小组列表"""
        online_groups = []
        
        for ip, panel in self.group_panels.items():
            if panel and 'hostname' in panel:
                hostname = panel['hostname']
                online_groups.append({
                    'ip': ip,
                    'hostname': hostname,
                    'status': 'online',
                    'is_sharing': self.group_sharing_status.get(ip, False)
                })
        
        return online_groups

    def add_group_panel(self, group_info):
        ip = group_info.get("ip")
        group_frame = QFrame()
        group_frame.setFrameShape(QFrame.StyledPanel)
        group_frame.setMinimumSize(280, 220)
        group_frame.setProperty("selected", False)
        group_frame.setStyleSheet("QFrame[selected='true'] { border: 2px solid #1E9FFF; }")
        
        group_layout = QVBoxLayout()
        hostname = group_info.get('hostname', '未知设备')
        group_label = QLabel(f"小组: {hostname} ({ip})")
        group_layout.addWidget(group_label)
        
        video_player = VideoFrame()
        stream_name = f"group_{ip.replace('.', '_')}"
        video_player.double_clicked.connect(
            partial(self.main_window.handle_group_view_focus, ip, hostname, stream_name)
        )
        group_layout.addWidget(video_player)
        
        list_widget = DraggableListWidget()
        list_widget.setObjectName(ip)
        group_layout.addWidget(list_widget)

        shutdown_btn = QPushButton("关机")
        shutdown_btn.clicked.connect(lambda: self.main_window.shutdown_group(ip))
        group_layout.addWidget(shutdown_btn)
        
        broadcast_btn = QPushButton("广播该小组画面")
        broadcast_btn.clicked.connect(partial(self.main_window.broadcast_stream, stream_name))
        broadcast_btn.setDisabled(True)
        group_layout.addWidget(broadcast_btn)
        
        group_frame.setLayout(group_layout)
        group_frame.mousePressEvent = partial(self.on_group_panel_clicked, group_frame)

        row, col = self.group_grid_position
        self.groups_layout.addWidget(group_frame, row, col)
        
        new_col = (col + 1) % self.grid_columns
        new_row = row + 1 if new_col == 0 else row
        self.group_grid_position = (new_row, new_col)

        self.group_panels[ip] = {'frame': group_frame, 'list': list_widget, 
                                 'video': video_player, 'broadcast_btn': broadcast_btn,
                                 'shutdown_btn': shutdown_btn, 'stream_name': stream_name, 
                                 'label': group_label, 'hostname': hostname, 'mac': group_info.get('mac')}

    def on_group_panel_clicked(self, frame, event):
        """Toggles the selection state of a group panel."""
        current_state = frame.property("selected")
        frame.setProperty("selected", not current_state)
        frame.style().polish(frame)
        frame.repaint()
        self.update_dynamic_buttons()

    def update_dynamic_buttons(self):
        """Shows or hides buttons based on selection."""
        selected_count = len([p for p in self.group_panels.values() if p['frame'].property("selected")])
        self.cancel_selection_btn.setVisible(selected_count > 0)
        self.split_screen_btn.setVisible(selected_count > 0)
        self.split_screen_btn.setText(f"{selected_count}分屏对比" if selected_count > 0 else "分屏对比")

    def apply_split_screen_layout(self):
        """Applies the multi-screen comparison layout to the main display."""
        selected_panels = [p for p in self.group_panels.values() if p['frame'].property("selected")]
        if not selected_panels:
            QMessageBox.warning(self, "提示", "请先选择要对比的小组。")
            return
        
        self.main_window.apply_split_screen_layout_from_panels(selected_panels)

    def clear_all_selections(self):
        """Deselects all group panels."""
        for panel_info in self.group_panels.values():
            if panel_info['frame'].property("selected"):
                panel_info['frame'].setProperty("selected", False)
                panel_info['frame'].style().polish(panel_info['frame'])
                panel_info['frame'].repaint()
        self.update_dynamic_buttons()

    def randomly_assign_students(self):
        """Randomly assigns all students to available groups."""
        group_lists = [p['list'] for p in self.group_panels.values()]
        if not group_lists:
            QMessageBox.warning(self, "提示", "没有在线小组，无法进行分组。")
            return
        
        all_items = []
        for glist in group_lists:
            while glist.count() > 0: all_items.append(glist.takeItem(0))
        while self.unassigned_students_list.count() > 0:
            all_items.append(self.unassigned_students_list.takeItem(0))
        
        random.shuffle(all_items)
        
        for i, item in enumerate(all_items):
            group_lists[i % len(group_lists)].addItem(item)
    def stop_all_videos(self):
        """停止所有小组视频播放器的播放。"""
        for panel in self.group_panels.values():
            if panel.get('video'):
                panel['video'].stop()

    def show_help_request(self, ip, hostname):
        """当收到求助信号时，高亮显示对应的小组面板。"""
        if ip in self.group_panels:
            QMessageBox.information(self, "小组求助", f"小组 '{hostname}' ({ip}) 正在请求帮助！")
            panel_frame = self.group_panels[ip]['frame']
            if not panel_frame.property("selected"):
                panel_frame.setProperty("selected", True)
                panel_frame.style().polish(panel_frame)
                panel_frame.repaint()
                self.update_dynamic_buttons()

    def stop_all_videos(self):
        """停止所有小组视频播放器的播放。"""
        for panel in self.group_panels.values():
            if panel.get('video'):
                panel['video'].stop()

    def get_student_group_map(self):
        """
        返回一个字典，将学生ID映射到他们所在的小组IP。
        e.g., {'S001': '*************', 'S002': '*************'}
        """
        student_map = {}
        for ip, panel in self.group_panels.items():
            list_widget = panel.get('list')
            if list_widget:
                for i in range(list_widget.count()):
                    item = list_widget.item(i)
                    student_data = item.data(Qt.UserRole)
                    if student_data and 'student_id' in student_data:
                        student_map[student_data['student_id']] = ip
        return student_map

    def get_groups(self):
        """
        返回所有小组的信息。
        e.g., {'*************': {'hostname': 'Group-1', ...}}
        """
        groups = {}
        for ip, panel in self.group_panels.items():
            # 从 QLabel 中提取小组名
            label = panel['frame'].findChild(QLabel)
            hostname = label.text().split('(')[0].replace('小组:', '').strip() if label else f"小组 {ip}"
            groups[ip] = {'hostname': hostname}
        return groups

    def get_all_mac_addresses(self):
        """获取所有已知小组的MAC地址列表"""
        mac_list = []
        for ip, panel_info in self.group_panels.items():
            # 假设MAC地址信息存储在panel_info中
            mac = panel_info.get('mac')
            if mac:
                mac_list.append(mac)
        return mac_list

    def save_groups(self):
        """保存分组到数据库"""
        if not self.main_window.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先选择一门课程。")
            return

        # 收集分组数据
        groups_data = []
        for ip, panel in self.group_panels.items():
            # 获取小组名称
            hostname = panel.get('hostname', f'小组_{ip}')

            # 获取小组中的学生
            students = []
            list_widget = panel.get('list')
            if list_widget:
                for i in range(list_widget.count()):
                    item = list_widget.item(i)
                    student_data = item.data(Qt.UserRole)
                    if student_data and 'student_id' in student_data:
                        students.append(student_data['student_id'])

            if students:  # 只保存有学生的分组
                groups_data.append({
                    'group_name': hostname,
                    'group_ip': ip,
                    'students': students
                })

        if not groups_data:
            QMessageBox.warning(self, "提示", "没有可保存的分组数据。")
            return

        # 发送API请求保存分组
        try:
            import requests
            headers = {'Authorization': f'Bearer {self.main_window.token}'}
            payload = {
                'course_schedule_id': self.main_window.current_course_schedule_id,
                'groups': groups_data
            }

            response = requests.post(
                f"{self.main_window.api_url}/api/teacher/groups/save",
                json=payload,
                headers=headers,
                verify=False
            )

            if response.status_code == 200 and response.json().get('success'):
                QMessageBox.information(self, "成功", "分组保存成功！")
            else:
                error_msg = response.json().get('message', '保存失败')
                QMessageBox.warning(self, "失败", f"保存分组失败: {error_msg}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存分组时发生错误: {str(e)}")

    def load_saved_groups(self):
        """加载已保存的分组数据"""
        if not self.main_window.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先选择一门课程。")
            return

        try:
            import requests
            headers = {'Authorization': f'Bearer {self.main_window.token}'}

            response = requests.get(
                f"{self.main_window.api_url}/api/teacher/groups/{self.main_window.current_course_schedule_id}",
                headers=headers,
                verify=False
            )

            if response.status_code == 200 and response.json().get('success'):
                groups_data = response.json().get('groups', [])

                if not groups_data:
                    QMessageBox.information(self, "提示", "当前课程没有已保存的分组数据。")
                    return

                # 清空现有分组中的学生
                for ip, panel in self.group_panels.items():
                    list_widget = panel.get('list')
                    if list_widget:
                        # 将学生移回未分配列表
                        while list_widget.count() > 0:
                            item = list_widget.takeItem(0)
                            self.unassigned_students_list.addItem(item)

                # 加载保存的分组数据
                loaded_count = 0
                for group_data in groups_data:
                    group_ip = None
                    if group_data.get('group_description') and 'IP:' in group_data['group_description']:
                        group_ip = group_data['group_description'].split('IP:')[1].strip()

                    if group_ip and group_ip in self.group_panels:
                        # 找到对应的小组面板
                        panel = self.group_panels[group_ip]
                        list_widget = panel.get('list')

                        if list_widget:
                            # 将学生分配到小组
                            for student_data in group_data.get('students', []):
                                student_id = student_data['student_id']
                                student_name = student_data['student_name']

                                # 从未分配列表中找到并移动学生
                                for i in range(self.unassigned_students_list.count()):
                                    item = self.unassigned_students_list.item(i)
                                    item_data = item.data(Qt.UserRole)
                                    if item_data and item_data.get('student_id') == student_id:
                                        # 移动到小组
                                        taken_item = self.unassigned_students_list.takeItem(i)
                                        list_widget.addItem(taken_item)
                                        loaded_count += 1
                                        break

                QMessageBox.information(self, "成功", f"已加载 {len(groups_data)} 个分组，共 {loaded_count} 名学生。")

            else:
                error_msg = response.json().get('message', '加载失败')
                QMessageBox.warning(self, "失败", f"加载分组失败: {error_msg}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载分组时发生错误: {str(e)}")