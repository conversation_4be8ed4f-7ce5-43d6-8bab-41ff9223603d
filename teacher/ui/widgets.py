# teacher/ui/widgets.py
import vlc
import json
import requests
import cv2
import numpy as np
from pyzbar import pyzbar
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QMessageBox, QListWidget, QFrame, QTabWidget,
                            QWidget, QTextEdit, QTimer)
from PyQt5.QtCore import pyqtSignal, Qt, QThread, QTimer
from PyQt5.QtGui import QPalette, QColor, QPixmap, QImage


class QRScannerThread(QThread):
    """二维码扫描线程"""
    qr_detected = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.cap = None

    def run(self):
        try:
            self.cap = cv2.VideoCapture(0)
            if not self.cap.isOpened():
                self.error_occurred.emit("无法打开摄像头")
                return

            self.running = True
            while self.running:
                ret, frame = self.cap.read()
                if not ret:
                    continue

                # 检测二维码
                barcodes = pyzbar.decode(frame)
                for barcode in barcodes:
                    barcode_data = barcode.data.decode('utf-8')
                    self.qr_detected.emit(barcode_data)
                    self.stop()
                    return

                self.msleep(100)  # 100ms延迟

        except Exception as e:
            self.error_occurred.emit(f"扫描错误: {str(e)}")
        finally:
            if self.cap:
                self.cap.release()

    def stop(self):
        self.running = False
        if self.cap:
            self.cap.release()

class DraggableListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.DragDrop)
        self.setDefaultDropAction(Qt.MoveAction)
        self.setSelectionMode(QListWidget.ExtendedSelection)

class VideoFrame(QFrame):
    double_clicked = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.Box)
        self.setMinimumSize(240, 135)
        self.setMaximumSize(480, 270)
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(0,0,0))
        self.setPalette(palette)
        self.setAutoFillBackground(True)
        self.vlc_instance = vlc.Instance("--no-xlib")
        self.media_player = self.vlc_instance.media_player_new()
        self.media_player.set_xwindow(int(self.winId()))

    def mouseDoubleClickEvent(self, event):
        self.double_clicked.emit()
        super().mouseDoubleClickEvent(event)

    def play(self, url):
        media = self.vlc_instance.media_new(url)
        media.add_option('network-caching=1000')
        self.media_player.set_media(media)
        self.media_player.play()

        # 立即设置一次宽高比，以处理主屏幕等预先创建的窗口
        w = self.width()
        h = self.height()
        if h > 0:
            self.media_player.video_set_aspect_ratio(f"{w}:{h}")

    def resizeEvent(self, event):
        """在控件大小改变时，自动更新视频宽高比以填充。"""
        super().resizeEvent(event)
        w = self.width()
        h = self.height()
        # 播放期间，因窗口缩放再次调整
        if self.media_player.is_playing() and h > 0:
            self.media_player.video_set_aspect_ratio(f"{w}:{h}")

    def stop(self):
        self.media_player.stop()

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("教师登录")
        self.setFixedSize(400, 350)

        # 主布局
        main_layout = QVBoxLayout(self)

        # 创建选项卡
        self.tab_widget = QTabWidget()

        # 账号密码登录选项卡
        self.password_tab = QWidget()
        self.setup_password_tab()
        self.tab_widget.addTab(self.password_tab, "账号密码登录")

        # 扫码登录选项卡
        self.qr_tab = QWidget()
        self.setup_qr_tab()
        self.tab_widget.addTab(self.qr_tab, "扫码登录")

        main_layout.addWidget(self.tab_widget)

        # 登录结果
        self.login_result = None
        self.qr_scanner = None

    def setup_password_tab(self):
        """设置账号密码登录选项卡"""
        layout = QVBoxLayout(self.password_tab)

        self.username_label = QLabel("用户名:")
        self.username_input = QLineEdit()
        self.username_input.setText("T001")  # 测试

        self.password_label = QLabel("密码:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("123456")  # 测试

        self.login_button = QPushButton("登录")
        self.login_button.clicked.connect(self.password_login)

        layout.addWidget(self.username_label)
        layout.addWidget(self.username_input)
        layout.addWidget(self.password_label)
        layout.addWidget(self.password_input)
        layout.addWidget(self.login_button)
        layout.addStretch()

    def setup_qr_tab(self):
        """设置扫码登录选项卡"""
        layout = QVBoxLayout(self.qr_tab)

        # 说明文字
        info_label = QLabel("请在网页端生成登录二维码，然后点击下方按钮扫描")
        info_label.setWordWrap(True)
        info_label.setAlignment(Qt.AlignCenter)

        # 扫码按钮
        self.scan_button = QPushButton("开始扫码")
        self.scan_button.clicked.connect(self.start_qr_scan)

        # 状态显示
        self.scan_status = QLabel("点击按钮开始扫码")
        self.scan_status.setAlignment(Qt.AlignCenter)
        self.scan_status.setStyleSheet("color: #666; padding: 10px;")

        layout.addWidget(info_label)
        layout.addWidget(self.scan_button)
        layout.addWidget(self.scan_status)
        layout.addStretch()

    def password_login(self):
        """账号密码登录"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username or not password:
            QMessageBox.warning(self, "提示", "请输入用户名和密码")
            return

        self.login_result = ("password", username, password)
        self.accept()

    def start_qr_scan(self):
        """开始二维码扫描"""
        try:
            if self.qr_scanner and self.qr_scanner.isRunning():
                self.qr_scanner.stop()
                self.qr_scanner.wait()

            self.qr_scanner = QRScannerThread()
            self.qr_scanner.qr_detected.connect(self.on_qr_detected)
            self.qr_scanner.error_occurred.connect(self.on_scan_error)

            self.scan_button.setText("扫描中...")
            self.scan_button.setEnabled(False)
            self.scan_status.setText("正在扫描二维码，请将二维码对准摄像头...")
            self.scan_status.setStyleSheet("color: #009688;")

            self.qr_scanner.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动扫描失败: {str(e)}")
            self.reset_scan_ui()

    def on_qr_detected(self, qr_data):
        """处理检测到的二维码"""
        try:
            # 解析二维码数据
            data = json.loads(qr_data)

            if data.get("type") == "teacher_login":
                teacher_id = data.get("teacher_id")
                token = data.get("token")

                if teacher_id and token:
                    self.scan_status.setText("扫码成功，正在登录...")
                    self.scan_status.setStyleSheet("color: #5FB878;")

                    self.login_result = ("qr", teacher_id, token)
                    self.accept()
                else:
                    self.scan_status.setText("二维码数据不完整")
                    self.scan_status.setStyleSheet("color: #FF5722;")
                    self.reset_scan_ui()
            else:
                self.scan_status.setText("不是有效的登录二维码")
                self.scan_status.setStyleSheet("color: #FF5722;")
                self.reset_scan_ui()

        except json.JSONDecodeError:
            self.scan_status.setText("二维码格式错误")
            self.scan_status.setStyleSheet("color: #FF5722;")
            self.reset_scan_ui()
        except Exception as e:
            self.scan_status.setText(f"处理二维码失败: {str(e)}")
            self.scan_status.setStyleSheet("color: #FF5722;")
            self.reset_scan_ui()

    def on_scan_error(self, error_msg):
        """处理扫描错误"""
        self.scan_status.setText(f"扫描失败: {error_msg}")
        self.scan_status.setStyleSheet("color: #FF5722;")
        self.reset_scan_ui()

    def reset_scan_ui(self):
        """重置扫描界面"""
        self.scan_button.setText("重新扫码")
        self.scan_button.setEnabled(True)

        # 3秒后重置状态文字
        QTimer.singleShot(3000, lambda: (
            self.scan_status.setText("点击按钮开始扫码"),
            self.scan_status.setStyleSheet("color: #666;")
        ))

    def get_credentials(self):
        """获取登录凭据"""
        if self.login_result:
            return self.login_result
        return None

    def closeEvent(self, event):
        """关闭事件"""
        if self.qr_scanner and self.qr_scanner.isRunning():
            self.qr_scanner.stop()
            self.qr_scanner.wait()
        super().closeEvent(event)
