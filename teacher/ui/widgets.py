# teacher/ui/widgets.py
import vlc
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox, QListWidget, QFrame
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtGui import QPalette, QColor

class DraggableListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.DragDrop)
        self.setDefaultDropAction(Qt.MoveAction)
        self.setSelectionMode(QListWidget.ExtendedSelection)

class VideoFrame(QFrame):
    double_clicked = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.Box)
        self.setMinimumSize(240, 135)
        self.setMaximumSize(480, 270)
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(0,0,0))
        self.setPalette(palette)
        self.setAutoFillBackground(True)
        self.vlc_instance = vlc.Instance("--no-xlib")
        self.media_player = self.vlc_instance.media_player_new()
        self.media_player.set_xwindow(int(self.winId()))

    def mouseDoubleClickEvent(self, event):
        self.double_clicked.emit()
        super().mouseDoubleClickEvent(event)

    def play(self, url):
        media = self.vlc_instance.media_new(url)
        media.add_option('network-caching=1000')
        self.media_player.set_media(media)
        self.media_player.play()

        # 立即设置一次宽高比，以处理主屏幕等预先创建的窗口
        w = self.width()
        h = self.height()
        if h > 0:
            self.media_player.video_set_aspect_ratio(f"{w}:{h}")

    def resizeEvent(self, event):
        """在控件大小改变时，自动更新视频宽高比以填充。"""
        super().resizeEvent(event)
        w = self.width()
        h = self.height()
        # 播放期间，因窗口缩放再次调整
        if self.media_player.is_playing() and h > 0:
            self.media_player.video_set_aspect_ratio(f"{w}:{h}")

    def stop(self):
        self.media_player.stop()

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("教师登录")
        self.layout = QVBoxLayout(self)

        self.username_label = QLabel("用户名:")
        self.username_input = QLineEdit(self)
        self.username_input.setText("T001") # 测试

        self.password_label = QLabel("密码:")
        self.password_input = QLineEdit(self)
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("123456") # 测试

        self.login_button = QPushButton("登录", self)
        self.login_button.clicked.connect(self.accept)

        self.layout.addWidget(self.username_label)
        self.layout.addWidget(self.username_input)
        self.layout.addWidget(self.password_label)
        self.layout.addWidget(self.password_input)
        self.layout.addWidget(self.login_button)

    def get_credentials(self):
        return self.username_input.text(), self.password_input.text()
