# teacher/ui/widgets.py
import vlc
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QMessageBox, QListWidget, QFrame)
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtGui import QPalette, QColor




class DraggableListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.DragDrop)
        self.setDefaultDropAction(Qt.MoveAction)
        self.setSelectionMode(QListWidget.ExtendedSelection)

class VideoFrame(QFrame):
    double_clicked = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.Box)
        self.setMinimumSize(240, 135)
        self.setMaximumSize(480, 270)
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(0,0,0))
        self.setPalette(palette)
        self.setAutoFillBackground(True)
        self.vlc_instance = vlc.Instance("--no-xlib")
        self.media_player = self.vlc_instance.media_player_new()
        self.media_player.set_xwindow(int(self.winId()))

    def mouseDoubleClickEvent(self, event):
        self.double_clicked.emit()
        super().mouseDoubleClickEvent(event)

    def play(self, url):
        media = self.vlc_instance.media_new(url)
        media.add_option('network-caching=1000')
        self.media_player.set_media(media)
        self.media_player.play()

        # 立即设置一次宽高比，以处理主屏幕等预先创建的窗口
        w = self.width()
        h = self.height()
        if h > 0:
            self.media_player.video_set_aspect_ratio(f"{w}:{h}")

    def resizeEvent(self, event):
        """在控件大小改变时，自动更新视频宽高比以填充。"""
        super().resizeEvent(event)
        w = self.width()
        h = self.height()
        # 播放期间，因窗口缩放再次调整
        if self.media_player.is_playing() and h > 0:
            self.media_player.video_set_aspect_ratio(f"{w}:{h}")

    def stop(self):
        self.media_player.stop()

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("教师登录")
        self.setFixedSize(350, 250)

        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("教师端登录")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title_label)

        # 用户名
        self.username_label = QLabel("用户名:")
        self.username_input = QLineEdit()
        self.username_input.setText("T001")  # 测试
        self.username_input.setPlaceholderText("请输入用户名")

        # 密码
        self.password_label = QLabel("密码:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("123456")  # 测试
        self.password_input.setPlaceholderText("请输入密码")

        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.clicked.connect(self.accept)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #009688;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #00796b;
            }
        """)

        layout.addWidget(self.username_label)
        layout.addWidget(self.username_input)
        layout.addWidget(self.password_label)
        layout.addWidget(self.password_input)
        layout.addWidget(self.login_button)

    def get_credentials(self):
        """获取登录凭据"""
        return self.username_input.text(), self.password_input.text()
