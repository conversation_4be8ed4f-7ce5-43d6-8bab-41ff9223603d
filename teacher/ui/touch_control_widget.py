# teacher/ui/touch_control_widget.py
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QComboBox, QTextEdit, QGroupBox, QCheckBox,
                             QSlider, QSpinBox, QMessageBox, QLineEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor
import json

class TouchControlWidget(QWidget):
    # 信号
    show_keyboard_signal = pyqtSignal(str)  # group_id
    hide_keyboard_signal = pyqtSignal(str)  # group_id
    start_control_signal = pyqtSignal(str)  # group_id
    stop_control_signal = pyqtSignal(str)   # group_id
    send_touch_signal = pyqtSignal(str, str, dict, float)  # group_id, event_type, coordinates, pressure
    send_text_signal = pyqtSignal(str, str)  # group_id, text
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.current_group_id = None
        self.control_active = False
        self.available_groups = []
        self.mouse_pressed = False
        self.online_groups = {}  # 存储在线小组信息 {group_id: {hostname, ip, status}}
        
        self.init_ui()
        self.setup_connections()
        
        # 设置自动刷新定时器
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.auto_refresh_groups)
        self.auto_refresh_timer.start(5000)  # 每5秒自动刷新一次
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("触控回传控制")
        self.setMinimumSize(400, 600)
        
        layout = QVBoxLayout()
        
        # 小组选择区域
        group_box = QGroupBox("小组选择")
        group_layout = QVBoxLayout()
        
        self.group_combo = QComboBox()
        self.group_combo.setMinimumHeight(30)
        group_layout.addWidget(QLabel("选择小组:"))
        group_layout.addWidget(self.group_combo)
        
        self.refresh_groups_btn = QPushButton("刷新小组列表")
        group_layout.addWidget(self.refresh_groups_btn)
        
        group_box.setLayout(group_layout)
        layout.addWidget(group_box)
        
        # 虚拟键盘控制区域
        keyboard_box = QGroupBox("虚拟键盘控制")
        keyboard_layout = QHBoxLayout()
        
        self.show_keyboard_btn = QPushButton("显示虚拟键盘")
        self.hide_keyboard_btn = QPushButton("隐藏虚拟键盘")
        
        keyboard_layout.addWidget(self.show_keyboard_btn)
        keyboard_layout.addWidget(self.hide_keyboard_btn)
        
        keyboard_box.setLayout(keyboard_layout)
        layout.addWidget(keyboard_box)
        
        # 触控控制区域
        control_box = QGroupBox("触控控制")
        control_layout = QVBoxLayout()
        
        control_buttons_layout = QHBoxLayout()
        self.start_control_btn = QPushButton("开始触控控制")
        self.stop_control_btn = QPushButton("停止触控控制")
        self.stop_control_btn.setEnabled(False)
        
        control_buttons_layout.addWidget(self.start_control_btn)
        control_buttons_layout.addWidget(self.stop_control_btn)
        control_layout.addLayout(control_buttons_layout)
        
        # 触控模拟区域
        self.touch_area = TouchSimulationArea()
        self.touch_area.setMinimumHeight(200)
        self.touch_area.setStyleSheet("border: 2px solid gray; background-color: lightgray;")
        control_layout.addWidget(QLabel("触控模拟区域 (在此区域操作会传输到小组端):"))
        control_layout.addWidget(self.touch_area)
        
        control_box.setLayout(control_layout)
        layout.addWidget(control_box)
        
        # 文本输入区域
        text_box = QGroupBox("文本输入")
        text_layout = QVBoxLayout()
        
        self.text_input = QLineEdit()
        self.text_input.setPlaceholderText("输入要发送的文本...")
        self.send_text_btn = QPushButton("发送文本")
        
        text_layout.addWidget(self.text_input)
        text_layout.addWidget(self.send_text_btn)
        
        text_box.setLayout(text_layout)
        layout.addWidget(text_box)
        
        # 状态显示区域
        status_box = QGroupBox("状态信息")
        status_layout = QVBoxLayout()
        
        self.status_label = QLabel("状态: 未连接")
        self.status_label.setStyleSheet("color: red;")
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(QLabel("操作日志:"))
        status_layout.addWidget(self.log_text)
        
        status_box.setLayout(status_layout)
        layout.addWidget(status_box)
        
        self.setLayout(layout)
        
    def setup_connections(self):
        """设置信号连接"""
        self.group_combo.currentTextChanged.connect(self.on_group_changed)
        self.refresh_groups_btn.clicked.connect(self.refresh_groups)
        
        self.show_keyboard_btn.clicked.connect(self.on_show_keyboard)
        self.hide_keyboard_btn.clicked.connect(self.on_hide_keyboard)
        
        self.start_control_btn.clicked.connect(self.on_start_control)
        self.stop_control_btn.clicked.connect(self.on_stop_control)
        
        self.send_text_btn.clicked.connect(self.on_send_text)
        self.text_input.returnPressed.connect(self.on_send_text)
        
        # 触控区域信号连接
        self.touch_area.touch_event.connect(self.on_touch_event)
        
    def refresh_groups(self):
        """从主程序获取当前在线的小组列表"""
        self.group_combo.clear()
        self.available_groups = []
        
        # 从主程序获取在线小组信息
        if hasattr(self.parent, 'grouping_tab_widget') and self.parent.grouping_tab_widget:
            online_groups = self.parent.grouping_tab_widget.get_online_groups()
            
            for group_info in online_groups:
                hostname = group_info.get('hostname', 'Unknown')
                ip = group_info.get('ip', 'Unknown')
                status = group_info.get('status', 'offline')
                
                if status == 'online':
                    # 使用与Socket事件中一致的group_id格式
                    group_id = f"group_{hostname.replace(' ', '_')}_{ip.replace('.', '_')}"
                    display_name = f"{hostname} ({ip})"
                    
                    self.online_groups[group_id] = {
                        'hostname': hostname,
                        'ip': ip,
                        'status': status,
                        'display_name': display_name
                    }
                    
                    self.available_groups.append(group_id)
                    self.group_combo.addItem(display_name, group_id)
            
            if self.available_groups:
                self.add_log(f"已刷新小组列表，找到 {len(self.available_groups)} 个在线小组")
            else:
                self.add_log("未找到在线小组")
        else:
            self.add_log("无法获取小组信息，请确保系统已正确初始化")
    
    def auto_refresh_groups(self):
        """自动刷新小组列表（静默模式）"""
        if not hasattr(self.parent, 'grouping_tab_widget') or not self.parent.grouping_tab_widget:
            return
            
        # 获取当前选中的小组
        current_selection = self.current_group_id
        
        # 静默刷新，不输出日志
        old_count = len(self.available_groups)
        self.group_combo.clear()
        self.available_groups = []
        
        online_groups = self.parent.grouping_tab_widget.get_online_groups()
        
        for group_info in online_groups:
            hostname = group_info.get('hostname', 'Unknown')
            ip = group_info.get('ip', 'Unknown')
            status = group_info.get('status', 'offline')
            
            if status == 'online':
                group_id = f"group_{hostname.replace(' ', '_')}_{ip.replace('.', '_')}"
                display_name = f"{hostname} ({ip})"
                
                self.online_groups[group_id] = {
                    'hostname': hostname,
                    'ip': ip,
                    'status': status,
                    'display_name': display_name
                }
                
                self.available_groups.append(group_id)
                self.group_combo.addItem(display_name, group_id)
        
        # 恢复之前的选择
        if current_selection and current_selection in self.available_groups:
            for i in range(self.group_combo.count()):
                if self.group_combo.itemData(i) == current_selection:
                    self.group_combo.setCurrentIndex(i)
                    break
        
        # 如果小组数量发生变化，显示日志
        new_count = len(self.available_groups)
        if new_count != old_count:
            if new_count > old_count:
                self.add_log(f"检测到新的在线小组，当前共 {new_count} 个")
            else:
                self.add_log(f"部分小组已离线，当前共 {new_count} 个")
        
    def on_group_changed(self, text):
        """小组选择变化"""
        # 从combo box获取当前选中项的数据
        current_index = self.group_combo.currentIndex()
        if current_index >= 0:
            self.current_group_id = self.group_combo.itemData(current_index)
            if self.current_group_id and self.current_group_id in self.online_groups:
                group_info = self.online_groups[self.current_group_id]
                display_name = group_info['display_name']
                self.add_log(f"已选择小组: {display_name}")
                self.update_status(f"已选择小组: {display_name}")
            else:
                self.current_group_id = None
        else:
            self.current_group_id = None
        
    def on_show_keyboard(self):
        """显示虚拟键盘"""
        if not self.current_group_id:
            QMessageBox.warning(self, "警告", "请先选择一个小组")
            return
            
        self.show_keyboard_signal.emit(self.current_group_id)
        self.add_log(f"正在显示小组 {self.current_group_id} 的虚拟键盘...")
        
    def on_hide_keyboard(self):
        """隐藏虚拟键盘"""
        if not self.current_group_id:
            QMessageBox.warning(self, "警告", "请先选择一个小组")
            return
            
        self.hide_keyboard_signal.emit(self.current_group_id)
        self.add_log(f"正在隐藏小组 {self.current_group_id} 的虚拟键盘...")
        
    def on_start_control(self):
        """开始触控控制"""
        if not self.current_group_id:
            QMessageBox.warning(self, "警告", "请先选择一个小组")
            return
            
        self.start_control_signal.emit(self.current_group_id)
        self.control_active = True
        self.start_control_btn.setEnabled(False)
        self.stop_control_btn.setEnabled(True)
        self.touch_area.setEnabled(True)
        
        self.update_status(f"正在控制小组: {self.current_group_id}")
        self.add_log(f"已开始对小组 {self.current_group_id} 的触控控制")
        
    def on_stop_control(self):
        """停止触控控制"""
        if not self.current_group_id:
            return
            
        self.stop_control_signal.emit(self.current_group_id)
        self.control_active = False
        self.start_control_btn.setEnabled(True)
        self.stop_control_btn.setEnabled(False)
        self.touch_area.setEnabled(False)
        
        self.update_status(f"已停止控制")
        self.add_log(f"已停止对小组 {self.current_group_id} 的触控控制")
        
    def on_send_text(self):
        """发送文本"""
        if not self.current_group_id:
            QMessageBox.warning(self, "警告", "请先选择一个小组")
            return
            
        text = self.text_input.text().strip()
        if not text:
            return
            
        self.send_text_signal.emit(self.current_group_id, text)
        self.add_log(f"已发送文本到小组 {self.current_group_id}: {text}")
        self.text_input.clear()
        
    def on_touch_event(self, event_type, x, y, pressure):
        """处理触控事件"""
        if not self.control_active or not self.current_group_id:
            return
            
        coordinates = {'x': x, 'y': y}
        self.send_touch_signal.emit(self.current_group_id, event_type, coordinates, pressure)
        
        # 只记录触控开始和结束，避免日志过多
        if event_type in ['touch_down', 'touch_up']:
            self.add_log(f"触控事件: {event_type} at ({x}, {y})")
    
    def handle_keyboard_status(self, data):
        """处理虚拟键盘状态反馈"""
        group_id = data.get('group_id')
        status = data.get('status')
        
        if group_id == self.current_group_id:
            if status == 'shown':
                self.add_log(f"小组 {group_id} 虚拟键盘已显示")
            elif status == 'hidden':
                self.add_log(f"小组 {group_id} 虚拟键盘已隐藏")
            elif status == 'error':
                self.add_log(f"小组 {group_id} 虚拟键盘操作失败")
                QMessageBox.warning(self, "错误", f"小组 {group_id} 虚拟键盘操作失败")
    
    def handle_control_response(self, data):
        """处理触控控制响应"""
        group_id = data.get('group_id')
        response_type = data.get('response_type')
        success = data.get('success')
        
        if group_id == self.current_group_id:
            if success:
                self.add_log(f"小组 {group_id}: {response_type} 成功")
            else:
                error = data.get('error', '未知错误')
                self.add_log(f"小组 {group_id}: {response_type} 失败 - {error}")
                QMessageBox.warning(self, "错误", f"操作失败: {error}")
    
    def update_status(self, message):
        """更新状态显示"""
        self.status_label.setText(f"状态: {message}")
        if "控制" in message:
            self.status_label.setStyleSheet("color: green;")
        else:
            self.status_label.setStyleSheet("color: blue;")
    
    def add_log(self, message):
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


class TouchSimulationArea(QWidget):
    """触控模拟区域"""
    touch_event = pyqtSignal(str, int, int, float)  # event_type, x, y, pressure
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMouseTracking(True)
        self.mouse_pressed = False
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.mouse_pressed = True
            x, y = event.x(), event.y()
            self.touch_event.emit('touch_down', x, y, 1.0)
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.mouse_pressed:
            x, y = event.x(), event.y()
            self.touch_event.emit('touch_move', x, y, 1.0)
            
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.mouse_pressed:
            self.mouse_pressed = False
            x, y = event.x(), event.y()
            self.touch_event.emit('touch_up', x, y, 1.0)