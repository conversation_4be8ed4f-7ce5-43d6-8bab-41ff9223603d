# teacher/main.py
import sys
import random
import requests
import subprocess
import os
from functools import partial
import configparser

from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QMessageBox, QFrame, QSplitter,
                             QTabWidget, QTextEdit, QLineEdit, QComboBox,
                             QCheckBox, QFileDialog, QListWidgetItem, QListWidget, QDialog,
                             QSizePolicy, QGridLayout, QDialogButtonBox, QTableWidget,
                             QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QPixmap

from network.udp_listener import UdpListener
from network.socket_manager import SocketIOManager

from ui.widgets import DraggableListWidget, VideoFrame, LoginDialog
from ui.whiteboard import Whiteboard
from ui.grouping_tab import GroupingTab
from ui.chat_widget import ChatWidget
from ui.touch_control_widget import Touch<PERSON>ontrolWidget
from ui.attendance_window import AttendanceWindow
from ui.login_qr_window import LoginQRWindow

from toolbar import ToolbarWindow

from features.screen_dlan import ScreenDLAN
from features.file_sharing import FileSharingManager
from features.student_picker import StudentPicker
from features.remote_control import RemoteControlManager
from features.interactive_quiz import InteractiveQuizTab
from features.group_timer import TimerSetupDialog, FloatingTimerWindow
from features.group_topic import TopicSetupDialog
from features.touch_control import TouchControlManager

class BroadcastModeDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择广播模式")
        self.layout = QVBoxLayout(self)
        
        self.mode_combo = QComboBox()
        self.mode_combo.addItem("开始广播", "manual")
        self.mode_combo.addItem("开课后自动广播", "auto_login")
        
        self.buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.buttons.accepted.connect(self.accept)
        self.buttons.rejected.connect(self.reject)
        
        self.layout.addWidget(QLabel("请选择广播操作模式："))
        self.layout.addWidget(self.mode_combo)
        self.layout.addWidget(self.buttons)

    def get_selected_mode(self):
        return self.mode_combo.currentData()

# 读取配置
config = configparser.ConfigParser()
# config.ini 位于项目根目录, main.py 在 teacher/ 目录下
config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.ini')
config.read(config_path)
network_config = config['Network']


class TeacherApp(QWidget):
    def __init__(self, token): 
        super().__init__()
        # 从配置初始化
        server_host = network_config.get('SERVER_HOST')
        server_port = network_config.getint('SERVER_PORT')
        rtsp_host = network_config.get('RTSP_HOST')
        rtsp_port = network_config.getint('RTSP_PORT')
        discovery_port = network_config.getint('DISCOVERY_PORT')

        self.api_url = f"https://{server_host}:{server_port}"
        self.rtsp_url = f"rtsp://{rtsp_host}:{rtsp_port}"
        self.token = token
        self.file_sharing_manager = FileSharingManager(self.api_url, self.token)
        self.student_picker = StudentPicker(parent=self)
        self.remote_control_manager = RemoteControlManager(parent=self)
        self.touch_control_manager = TouchControlManager(parent=self)
        self.touch_control_widget = TouchControlWidget(parent=self)
        
        self.current_course_schedule_id = None
        self.is_class_active = False
        self.focused_group_ip = None
        
        self.all_students = {}
        self.ffmpeg_process = None
        self.current_broadcast_source = "teacher"
        self.broadcast_mode = 'manual'
        self.whiteboard = None
        self.toolbar = None
        self.dlan_window = None
        self.floating_timer = None
        self.attendance_window = None
        self.login_qr_window = None

        self.main_video_player = VideoFrame()
        self.main_control_btn = QPushButton("开始屏幕分享")
        self.grouping_tab_widget = GroupingTab(main_window=self)
        self.interactive_quiz_tab = InteractiveQuizTab(self)

        self.initUI()

        self.listener = UdpListener(listen_port=discovery_port)
        self.listener.start()
        
        self.sio_manager = SocketIOManager(self.api_url)
        self.sio_thread = QThread()
        self.sio_manager.moveToThread(self.sio_thread)
        self.sio_thread.started.connect(lambda: self.sio_manager.connect(self.token))
        self.sio_manager.help_request_received_signal.connect(self.handle_help_request)
        self.sio_manager.answer_submitted_signal.connect(self.interactive_quiz_tab.update_answer)
        self.sio_manager.timer_started_signal.connect(self.on_timer_started)
        self.sio_manager.chat_message_received_signal.connect(self.chat_widget.add_message)
        self.chat_widget.send_message_signal.connect(self.send_chat_message)
        
        # 连接触控回传管理器
        self.touch_control_manager.socket_manager = self.sio_manager
        
        # 连接触控回传UI信号
        self.touch_control_widget.show_keyboard_signal.connect(self.touch_control_manager.show_virtual_keyboard)
        self.touch_control_widget.hide_keyboard_signal.connect(self.touch_control_manager.hide_virtual_keyboard)
        self.touch_control_widget.start_control_signal.connect(self.touch_control_manager.start_touch_control_session)
        self.touch_control_widget.stop_control_signal.connect(self.touch_control_manager.stop_touch_control_session)
        self.touch_control_widget.send_touch_signal.connect(self.touch_control_manager.send_touch_event)
        self.touch_control_widget.send_text_signal.connect(self.touch_control_manager.send_text_input)
        
        # 连接触控回传响应信号
        self.sio_manager.touch_control_response_signal.connect(self.touch_control_widget.handle_control_response)
        self.sio_manager.virtual_keyboard_status_signal.connect(self.touch_control_widget.handle_keyboard_status)
        self.sio_manager.touch_control_error_signal.connect(self.handle_touch_control_error)
        self.sio_manager.group_drawing_signal.connect(self.on_group_drawing)
        self.sio_manager.whiteboard_state_from_group_signal.connect(self.on_whiteboard_state_from_group)
        
        self.sio_thread.start()

        # 将UDP监听信号直接连接到分组标签页的处理器
        self.listener.group_status_signal.connect(self.grouping_tab_widget.handle_group_status_update)

        QTimer.singleShot(100, self.initial_data_load)

    def initUI(self):
        self.setWindowTitle('教师端')
        self.setGeometry(100, 100, 1200, 700)
        self.main_layout = QVBoxLayout()
        self.tabs = QTabWidget()
        self.broadcast_tab = QWidget()

        self.quiz_tab = self.interactive_quiz_tab
        self.chat_widget = ChatWidget(self)

        self.tabs.addTab(self.broadcast_tab, "主屏幕控制")
        self.tabs.addTab(self.grouping_tab_widget, "分组与监看")
        self.tabs.addTab(self.quiz_tab, "互动答题")
        self.tabs.addTab(self.touch_control_widget, "触控回传")
        self.tabs.addTab(self.chat_widget, "课堂聊天")

        self.setup_broadcast_tab()

        self.main_layout.addWidget(self.tabs)

        session_bar = QHBoxLayout()
        self.session_status_label = QLabel("状态：未选择课程")
        self.focus_status_label = QLabel("视角：教师本人")
        
        self.course_button = QPushButton("选择课程")
        self.course_button.clicked.connect(self.show_course_selection_dialog)
        self.selected_course_label = QLabel("未选择课程")

        self.class_session_btn = QPushButton("开始上课")
        self.class_session_btn.clicked.connect(self.toggle_class_session)

        self.launch_toolbar_btn = QPushButton("启动工具栏")
        self.launch_toolbar_btn.clicked.connect(self.launch_toolbar)

        session_bar.addWidget(QLabel("当前课程:"))
        session_bar.addWidget(self.course_button)
        session_bar.addWidget(self.selected_course_label)
        session_bar.addWidget(self.session_status_label)
        session_bar.addWidget(self.focus_status_label)
        session_bar.addStretch()
        session_bar.addWidget(self.launch_toolbar_btn)
        session_bar.addWidget(self.class_session_btn)
        self.main_layout.addLayout(session_bar)

        self.setLayout(self.main_layout)
        
        self.update_ui_for_session_state(False)

    def launch_toolbar(self):
        """启动或关闭核心工具栏窗口"""
        try:
            # 检查工具栏实例是否存在并且可见
            if self.toolbar and self.toolbar.isVisible():
                # 如果可见，则关闭它
                self.toolbar.close()
                self.toolbar = None
                self.launch_toolbar_btn.setText("启动工具栏")
            else:
                # 如果不可见或不存在，则创建并显示它
                self.toolbar = ToolbarWindow(parent=None, on_close_callback=self.on_toolbar_closed)
                # 连接信号到槽
                self.toolbar.button_clicked_signal.connect(self.handle_toolbar_action)
                self.toolbar.show()
                self.toolbar.raise_()
                self.toolbar.activateWindow()
                self.launch_toolbar_btn.setText("关闭工具栏")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作工具栏失败: {e}")

    def handle_toolbar_action(self, action_text):
        """处理来自工具栏的按钮点击事件"""
        if action_text == "广播":            
             dialog = BroadcastModeDialog(self)            
             if dialog.exec_() == QDialog.Accepted:               
                mode = dialog.get_selected_mode()                
                if mode == "manual": # 手动模式：切换广播状态                    
                    self.handle_main_control_click()                
                elif mode == "auto_login": # 设置为自动广播模式                    
                    self.broadcast_mode = 'auto_login'                    
                    QMessageBox.information(self, "设置成功", "开始上课后自动广播")           
        elif action_text == "分享":
            self.file_sharing_manager.distribute_file()
        elif action_text == "投屏":
            self.launch_dlan_window()
        elif action_text == "白板":
            self.toggle_annotation()
        elif action_text == "计时":
            self.setup_group_timer()
        elif action_text == "主题":
            self.set_group_topic()
        elif action_text == "聊天":
            self.show_chat_tab()
        elif action_text == "选人":
            self.student_picker.pick()
        elif action_text == "签到管理":
            self.show_attendance_window()
        elif action_text == "登录二维码":
            self.show_login_qr_window()

    def show_attendance_window(self):
        """显示签到管理窗口"""
        if self.attendance_window and self.attendance_window.isVisible():
            # 如果窗口已经打开，则激活它
            self.attendance_window.raise_()
            self.attendance_window.activateWindow()
        else:
            # 创建新的签到管理窗口
            self.attendance_window = AttendanceWindow(
                parent=self,
                api_url=self.api_url,
                token=self.token
            )
            # 设置当前课程信息
            self.attendance_window.set_course_info(self.current_course_schedule_id)
            self.attendance_window.show()

    def show_login_qr_window(self):
        """显示登录二维码窗口"""
        if self.login_qr_window and self.login_qr_window.isVisible():
            # 如果窗口已经打开，则激活它
            self.login_qr_window.raise_()
            self.login_qr_window.activateWindow()
        else:
            # 创建新的登录二维码窗口
            self.login_qr_window = LoginQRWindow(
                parent=self,
                teacher_id=getattr(self, 'teacher_id', 'unknown'),
                token=self.token
            )
            self.login_qr_window.show()

    def on_toolbar_closed(self):
        """当工具栏窗口被关闭时调用的回调函数"""
        self.launch_toolbar_btn.setText("启动工具栏")
        self.toolbar = None

    def on_timer_started(self, data):
        """处理从服务器收到的计时器开始事件。"""
        duration = data.get('duration')
        if duration:
            if self.floating_timer and self.floating_timer.isVisible():
                self.floating_timer.close()
            self.floating_timer = FloatingTimerWindow(duration)
            self.floating_timer.start()

    def launch_dlan_window(self):
        """启动或关闭DLNA投屏窗口"""
        if self.dlan_window and self.dlan_window.isVisible():
            self.dlan_window.close()
            self.dlan_window = None
        else:
            self.dlan_window = ScreenDLAN()
            self.dlan_window.show()

    def setup_group_timer(self):
        """设置并广播分组计时器。"""
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先开始一门课程才能使用计时器。")
            return

        dialog = TimerSetupDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            duration = dialog.get_duration_seconds()
            
            # 发送API请求来广播计时器
            try:
                headers = {'Authorization': f'Bearer {self.token}'}
                payload = {
                    'duration': duration,
                    'course_schedule_id': self.current_course_schedule_id
                }
                r = requests.post(f"{self.api_url}/api/teacher/class/timer/start", json=payload, headers=headers, verify=False)
                if r.status_code == 200 and r.json().get('success'):
                    QMessageBox.information(self, "成功", "计时器已成功广播给所有学生端。")
                    self.on_timer_started({'duration': duration})
                else:
                    QMessageBox.warning(self, "失败", f"广播计时器失败: {r.json().get('message', r.text)}")
            except requests.exceptions.RequestException as e:
                QMessageBox.critical(self, "网络错误", f"连接服务器失败: {e}")

    def set_group_topic(self):
        """设置并广播分组讨论主题。"""
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先开始一门课程才能发送主题。")
            return

        dialog = TopicSetupDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            topic = dialog.get_topic_text()
            if not topic:
                QMessageBox.warning(self, "提示", "主题内容不能为空。")
                return
            
            try:
                headers = {'Authorization': f'Bearer {self.token}'}
                payload = {
                    'topic_text': topic,
                    'course_schedule_id': self.current_course_schedule_id
                }
                r = requests.post(f"{self.api_url}/api/teacher/group/set_topic", json=payload, headers=headers, verify=False)
                if r.status_code == 200 and r.json().get('success'):
                    QMessageBox.information(self, "成功", "讨论主题已成功发送给所有小组。")
                else:
                    QMessageBox.warning(self, "失败", f"发送主题失败: {r.json().get('message', r.text)}")
            except requests.exceptions.RequestException as e:
                QMessageBox.critical(self, "网络错误", f"连接服务器失败: {e}")

    def send_chat_message(self, message):
        """通过Socket.IO发送聊天消息"""
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先开始一门课程才能发送聊天消息。")
            return

        if self.sio_manager and self.sio_manager.is_connected():
            # 使用Socket.IO的emit来发送消息，与学生端保持一致
            self.sio_manager.sio.emit('send_message', {'message': message})
        else:
            QMessageBox.warning(self, "错误", "未连接到实时互动服务器，无法发送消息。")

    def get_teacher_name(self):
        """获取教师姓名"""
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            r = requests.get(f"{self.api_url}/api/teacher/profile", headers=headers, verify=False)
            if r.status_code == 200:
                profile = r.json()
                return profile.get('name', '教师')
        except:
            pass
        return '教师'

    def shutdown_group(self, ip):
        """远程关闭指定IP的小组端"""
        reply = QMessageBox.question(self, '确认操作', 
                                     f'您确定要关闭IP为 {ip} 的小组电脑吗？',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            print(f"准备关闭小组: {ip}")
            self.remote_control_manager.shutdown_computer(ip)

    def boot_all_groups(self):
        """远程启动所有已知的小组端"""
        reply = QMessageBox.question(self, '确认操作', 
                                     '您确定要启动所有小组电脑吗？\n(此功能依赖网络唤醒WoL配置)',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            print("准备启动所有小组")
            mac_addresses = self.grouping_tab_widget.get_all_mac_addresses()
            self.remote_control_manager.wake_computers(mac_addresses)

    def handle_help_request(self, data):
        """处理求助请求信号，提取IP和主机名"""
        ip = data.get('ip')
        hostname = data.get('hostname')
        if ip and hostname:
            self.grouping_tab_widget.show_help_request(ip, hostname)

    def handle_touch_control_error(self, data):
        """处理触控控制错误"""
        error_msg = data.get('error', '未知错误')
        group_id = data.get('group_id', '未知小组')
        QMessageBox.critical(self, "触控控制错误", f"小组 {group_id} 触控控制失败:\n{error_msg}")

    def show_chat_tab(self):
        """切换到聊天标签页"""
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先开始一门课程才能使用聊天功能。")
            return
        
        # 切换到聊天标签页
        self.tabs.setCurrentWidget(self.chat_widget)

    def toggle_annotation(self):
        """切换白板的显示状态"""
        if self.whiteboard and self.whiteboard.isVisible():
            self.whiteboard.close()
        else:
            if not self.current_course_schedule_id:
                QMessageBox.warning(self, "提示", "先开始一门课程才能使用白板。")
                return

            self.whiteboard = Whiteboard(
                sio_manager=self.sio_manager,
                course_schedule_id=self.current_course_schedule_id,
                on_close_callback=self.on_whiteboard_closed
            )
            # 连接信号
            self.whiteboard.drawing_event.connect(self.on_teacher_drawing)
            self.whiteboard.collaboration_toggled.connect(self.handle_collaboration_toggle)
            self.sio_manager.student_drawing_received_signal.connect(self.whiteboard.on_student_drawing_received)
            self.whiteboard.showFullScreen()

    def on_whiteboard_closed(self):
        """白板关闭时的回调"""
        self.whiteboard = None

    def setup_broadcast_tab(self):
        layout = QVBoxLayout()
        
        # 主显示区，使用网格布局以支持分屏
        self.main_display_area = QFrame()
        self.main_display_layout = QGridLayout()
        self.main_display_layout.setSpacing(2)
        self.main_display_layout.setContentsMargins(0, 0, 0, 0)
        self.main_display_area.setLayout(self.main_display_layout)
        
        # 初始状态下，可以先放一个主播放器
        self.main_video_player = VideoFrame()
        self.main_video_player.double_clicked.connect(self.reset_main_view_to_teacher)
        self.main_display_layout.addWidget(self.main_video_player, 0, 0)

        layout.addWidget(self.main_display_area)

        self.main_control_btn = QPushButton("开始屏幕分享")
        self.main_control_btn.clicked.connect(self.handle_main_control_click)

        controls_layout = QHBoxLayout()
        controls_layout.addWidget(self.main_control_btn)
        layout.addLayout(controls_layout)

        self.broadcast_tab.setLayout(layout)



    def on_tab_changed(self, index):
        # 当切换到“签到管理”标签页时，自动加载数据
        if self.tabs.tabText(index) == "签到管理" and self.current_course_schedule_id:
            self.load_attendance_data()

    def load_attendance_data(self):
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先开始一门课程。")
            return

        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            r = requests.get(f"{self.api_url}/teacher/get_attendance_status/{self.current_course_schedule_id}", headers=headers, verify=False)
            
            if r.status_code == 200:
                data = r.json()
                if data.get("status") == "success":
                    self.attendance_list_widget.clear()
                    students = data.get("students", [])
                    for student in students:
                        item_text = f"{student['name']} ({student['student_id']}) - {student['attendance_status']}"
                        item = QListWidgetItem(item_text)
                        item.setData(Qt.UserRole, student)
                        
                        if student['signed_in']:
                            item.setForeground(Qt.green)
                        else:
                            item.setForeground(Qt.red)
                        self.attendance_list_widget.addItem(item)
                else:
                    QMessageBox.warning(self, "加载失败", data.get("message", "无法加载签到数据。"))
            else:
                QMessageBox.warning(self, "错误", f"无法获取签到状态: {r.text}")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "网络错误", f"连接服务器失败: {e}")

    def manual_checkin(self):
        selected_item = self.attendance_list_widget.currentItem()
        if not selected_item:
            QMessageBox.warning(self, "提示", "请先在列表中选择一名学生。")
            return

        student_data = selected_item.data(Qt.UserRole)
        student_id = student_data.get('student_id')

        if student_data.get('signed_in'):
            QMessageBox.information(self, "提示", "该学生已经签到。")
            return

        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            payload = {'student_id': student_id}
            r = requests.post(f"{self.api_url}/teacher/manual_checkin/{self.current_course_schedule_id}", headers=headers, data=payload, verify=False)

            if r.status_code == 200:
                data = r.json()
                if data.get("status") == "success":
                    QMessageBox.information(self, "成功", data.get("message", "签到成功！"))
                    self.load_attendance_data() # Refresh the list
                else:
                    QMessageBox.warning(self, "签到失败", data.get("message", "签到失败。"))
            else:
                QMessageBox.warning(self, "错误", f"签到请求失败: {r.text}")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "网络错误", f"连接服务器失败: {e}")

    def generate_attendance_qr(self):
        """生成签到二维码"""
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先选择并开始一门课程。")
            return

        try:
            # 生成签到二维码数据
            import json
            import qrcode
            import io
            import base64
            from PIL import Image

            qr_data = {
                "type": "attendance",
                "course_schedule_id": self.current_course_schedule_id,
                "timestamp": int(__import__('time').time())
            }

            # 生成二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(json.dumps(qr_data))
            qr.make(fit=True)

            # 创建二维码图片
            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为QPixmap
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)

            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())

            # 缩放到合适大小
            scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.qr_code_label.setPixmap(scaled_pixmap)

            QMessageBox.information(self, "成功", "签到二维码已生成！学生可以扫码签到。")

        except ImportError:
            QMessageBox.critical(self, "错误", "缺少二维码生成库，请安装 qrcode 和 Pillow 库。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成二维码失败: {str(e)}")

    def show_course_selection_dialog(self):
        """显示课程选择对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("选择课程")
        dialog.setFixedSize(600, 400)

        layout = QVBoxLayout(dialog)

        # 课程表格
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["课程名称", "班级", "时间", "状态"])

        # 设置表格列宽
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)

        # 加载课程数据
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            r = requests.get(f"{self.api_url}/api/teacher/courses", headers=headers, verify=False)
            if r.status_code == 200:
                courses = r.json().get("courses", [])
                table.setRowCount(len(courses))

                for row, course in enumerate(courses):
                    # 课程名称
                    table.setItem(row, 0, QTableWidgetItem(course['course_name']))
                    # 班级
                    table.setItem(row, 1, QTableWidgetItem(course['class_name']))
                    # 时间
                    time_text = f"{course['day_of_week']} {course['start_time']}-{course['end_time']}"
                    table.setItem(row, 2, QTableWidgetItem(time_text))
                    # 状态
                    status_map = {
                        'scheduled': '待上课',
                        'in_progress': '进行中',
                        'completed': '已结束'
                    }
                    status_text = status_map.get(course.get('status', 'scheduled'), '未知')
                    table.setItem(row, 3, QTableWidgetItem(status_text))

                    # 存储课程ID到第一列的item中
                    table.item(row, 0).setData(Qt.UserRole, course['id'])
            else:
                QMessageBox.warning(dialog, "错误", f"无法加载课程列表: {r.text}")
                return
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(dialog, "网络错误", f"连接服务器失败: {e}")
            return

        layout.addWidget(table)

        # 按钮
        button_layout = QHBoxLayout()
        select_btn = QPushButton("选择")
        cancel_btn = QPushButton("取消")

        def on_select():
            current_row = table.currentRow()
            if current_row >= 0:
                course_name = table.item(current_row, 0).text()
                class_name = table.item(current_row, 1).text()
                course_id = table.item(current_row, 0).data(Qt.UserRole)

                self.current_course_schedule_id = course_id
                self.selected_course_label.setText(f"{course_name} ({class_name})")
                self.load_class_details()
                self.update_ui_for_session_state(False)
                dialog.accept()
            else:
                QMessageBox.warning(dialog, "提示", "请选择一门课程")

        select_btn.clicked.connect(on_select)
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addStretch()
        button_layout.addWidget(select_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # 双击选择
        table.itemDoubleClicked.connect(lambda: on_select())

        dialog.exec_()

    def toggle_class_session(self):
        if self.is_class_active:
            self.end_class()
        else:
            self.start_class()

    def update_ui_for_session_state(self, is_active):
        """根据会话状态启用/禁用UI组件"""
        self.is_class_active = is_active
        self.tabs.setEnabled(is_active)
        self.interactive_quiz_tab.set_session_active(is_active)
        self.chat_widget.set_enabled_state(is_active)
        self.course_button.setEnabled(not is_active)

        # 更新合并后的按钮状态
        self.class_session_btn.setEnabled(is_active or (self.current_course_schedule_id is not None))

        # 更新签到窗口的课程信息
        if self.attendance_window and self.attendance_window.isVisible():
            self.attendance_window.set_course_info(self.current_course_schedule_id if is_active else None)

        if is_active:
            self.class_session_btn.setText("结束上课")
            self.session_status_label.setText(f"状态：上课中")
            self.session_status_label.setStyleSheet("color: green;")
        else:
            self.class_session_btn.setText("开始上课")
            if self.current_course_schedule_id:
                self.session_status_label.setText("状态：未开始")
            else:
                self.session_status_label.setText("状态：未选择课程")
            self.session_status_label.setStyleSheet("")

    def start_class(self):
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先选择一门课程。")
            return
        try:
            # 在请求头中加入认证信息
            headers = {'Authorization': f'Bearer {self.sio_manager.token}'}
            r = requests.post(f"{self.api_url}/api/teacher/class/{self.current_course_schedule_id}/start", headers=headers, verify=False)
            if r.status_code == 200:
                self.update_ui_for_session_state(True)
                # 开始课堂后，加入socket room
                if self.sio_manager:
                    self.sio_manager.join_course(self.current_course_schedule_id)
                QMessageBox.information(self, "成功", f"课程 {self.current_course_schedule_id} 已成功开始。")

                # 检查是否处于'登录后自动广播'模式
                if self.broadcast_mode == 'auto_login':
                    self.start_broadcast_and_set_source()
                    print("'登录后自动广播'模式已激活，自动开始广播...")
            else:
                QMessageBox.critical(self, "失败", f"无法开始课程: {r.text}")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "错误", f"连接服务器失败: {e}")

    def end_class(self):
        if not self.current_course_schedule_id:
            return
        
        reply = QMessageBox.question(self, '确认', '您确定要结束当前课程吗？',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                headers = {'Authorization': f'Bearer {self.sio_manager.token}'}
                r = requests.post(f"{self.api_url}/api/teacher/class/{self.current_course_schedule_id}/end", headers=headers, verify=False)
                if r.status_code == 200:
                    QMessageBox.information(self, "成功", f"课程 {self.current_course_schedule_id} 已成功结束。")
                    self.update_ui_for_session_state(False)
                    self.reset_class_state()
                else:
                    QMessageBox.warning(self, "失败", f"结束课程失败: {r.text}")
            except requests.exceptions.RequestException as e:
                QMessageBox.critical(self, "错误", f"连接服务器失败: {e}")

    def reset_class_state(self):
        """清空所有与课堂相关的数据和UI"""
        # 重置分组状态
        self.grouping_tab_widget.reset_state()
        
        # 停止并清空广播
        self.stop_broadcast()
        self.main_video_player.stop()
        
        
        # 清空签到列表
        self.attendance_list_widget.clear()
        
        print("课堂状态已重置。")

    def get_screen_resolution(self):
        try:
            cmd = "xrandr | grep '*'"
            process = subprocess.run(cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            return process.stdout.split()[0]
        except: return "1920x1080"
    def handle_main_control_click(self):
        if not self.ffmpeg_process: self.start_broadcast_and_set_source()
        elif self.current_broadcast_source == "teacher": self.stop_broadcast()
        elif self.current_broadcast_source != "teacher":
            if not self.ffmpeg_process: self.start_broadcast_and_set_source()
            else: self.broadcast_stream("teacher")
    def start_broadcast_and_set_source(self):
        if self.ffmpeg_process: return
        resolution = self.get_screen_resolution()
        command = ['ffmpeg', '-f', 'x11grab', '-s', resolution, '-i', ':0.0',
                   '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
                   '-pix_fmt', 'yuv420p', '-f', 'rtsp', f'{self.rtsp_url}/teacher']
        try:
            self.ffmpeg_process = subprocess.Popen(command)
            QTimer.singleShot(500, lambda: self.broadcast_stream("teacher"))
            QTimer.singleShot(1000, lambda: self.main_video_player.play(f"{self.rtsp_url}/teacher"))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动广播失败: {e}")
            self.ffmpeg_process = None
    def stop_broadcast(self):
        if self.ffmpeg_process:
            self.ffmpeg_process.terminate()
            self.ffmpeg_process = None
        self.main_video_player.stop()
        self.update_main_control_button()
    def broadcast_stream(self, stream_name):
        try:
            # 使用 Socket.IO 发送广播指令
            if self.sio_manager and self.sio_manager.is_connected():
                self.sio_manager.sio.emit('broadcast_stream', {'stream_name': stream_name, 'course_id': self.current_course_schedule_id})
                self.current_broadcast_source = stream_name
                self.update_main_control_button()
                
                # 如果广播源被设置为了教师本人，则强制将主屏幕也切换回教师视角
                if stream_name == "teacher":
                    # 使用定时器确保RTSP流有足够时间准备好
                    QTimer.singleShot(200, self.reset_main_view_to_teacher)
            else:
                QMessageBox.warning(self, "错误", "未连接到服务器，无法发送广播指令。")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"广播指令发送失败: {e}")
    def update_main_control_button(self):
        if self.current_broadcast_source != "teacher": self.main_control_btn.setText("夺回画面主导权")
        else:
            if self.ffmpeg_process: self.main_control_btn.setText("停止屏幕分享")
            else: self.main_control_btn.setText("开始屏幕分享")
    def initial_data_load(self):
        # 初始化时不再自动加载课程列表，改为按需加载
        pass

    def load_class_details(self):
        if not self.current_course_schedule_id:
            return
        try:
            headers = {'Authorization': f'Bearer {self.sio_manager.token}'}
            r = requests.get(f"{self.api_url}/api/teacher/class/{self.current_course_schedule_id}/details", headers=headers, verify=False)
            if r.status_code == 200:
                details = r.json().get("details", {})
                students = details.get("students", [])
                self.all_students = {s['student_id']: s for s in students}
                self.student_picker.update_student_list(self.all_students)
                self.grouping_tab_widget.populate_unassigned_list()
            else:
                QMessageBox.warning(self, "错误", f"无法加载课堂详情: {r.text}")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "严重错误", f"连接后端失败: {e}")

    def handle_group_status_update(self, group_info):
        ip = group_info.get("ip")
        if not ip: return
        if ip not in self.group_panels:
            self.add_group_panel(group_info)
        
        panel = self.group_panels.get(ip)
        if not panel:
            return

        is_sharing = group_info.get("is_sharing", False)
        was_sharing = self.group_sharing_status.get(ip, False)
        
        if is_sharing and not was_sharing:
            stream_name = group_info.get("stream_name")
            if stream_name:
                panel['video'].play(f"{self.rtsp_url}/{stream_name}")
                panel['broadcast_btn'].setDisabled(False)
        elif not is_sharing and was_sharing:
            panel['video'].stop()
            panel['broadcast_btn'].setDisabled(True)
            
        self.group_sharing_status[ip] = is_sharing
    def add_group_panel(self, group_info):
        ip = group_info.get("ip")
        group_frame = QFrame()
        group_frame.setFrameShape(QFrame.StyledPanel)
        group_frame.setMinimumSize(280, 220) # 设置最小尺寸
        group_frame.setProperty("selected", False) # 自定义属性用于标记选中
        group_frame.setStyleSheet("QFrame[selected='true'] { border: 2px solid #1E9FFF; }")
        
        group_layout = QVBoxLayout()
        hostname = group_info.get('hostname', '未知设备')
        group_label = QLabel(f"小组: {hostname} ({ip})")
        group_layout.addWidget(group_label)
        video_player = VideoFrame()
        video_player.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        stream_name = f"group_{ip.replace('.', '_')}"
        video_player.double_clicked.connect(
            partial(self.handle_group_view_focus, ip, hostname, stream_name)
        )
        group_layout.addWidget(video_player)
        list_widget = DraggableListWidget()
        list_widget.setObjectName(ip)
        group_layout.addWidget(list_widget)
        broadcast_btn = QPushButton("广播该小组画面")
        broadcast_btn.clicked.connect(partial(self.broadcast_stream, stream_name))
        broadcast_btn.setDisabled(True)
        group_layout.addWidget(broadcast_btn)
        group_frame.setLayout(group_layout)

        # 为整个面板添加点击事件
        group_frame.mousePressEvent = partial(self.on_group_panel_clicked, group_frame)

        # 使用网格布局添加
        row, col = self.group_grid_position
        self.groups_layout.addWidget(group_frame, row, col)
        
        # 更新下一个位置
        new_col = (col + 1) % self.grid_columns
        new_row = row + 1 if new_col == 0 else row
        self.group_grid_position = (new_row, new_col)

        self.group_panels[ip] = {'frame': group_frame, 'list': list_widget, 
                                 'video': video_player, 'broadcast_btn': broadcast_btn,
                                 'stream_name': stream_name}

    def on_group_panel_clicked(self, frame, event):
        # 默认就是多选切换模式
        current_state = frame.property("selected")
        frame.setProperty("selected", not current_state)
        frame.style().polish(frame)
        frame.repaint()
        
        # 更新动态按钮的状态
        self.update_dynamic_buttons()

    def update_dynamic_buttons(self):
        selected_count = len([p for p in self.group_panels.values() if p['frame'].property("selected")])

        if selected_count > 0:
            self.cancel_selection_btn.setVisible(True)
            self.split_screen_btn.setVisible(True)
            self.split_screen_btn.setText(f"{selected_count}分屏对比")
        else:
            self.cancel_selection_btn.setVisible(False)
            self.split_screen_btn.setVisible(False)

    def apply_split_screen_layout_from_panels(self, selected_panels):
        if not selected_panels:
            # 理论上不应该发生，因为按钮是隐藏的
            QMessageBox.warning(self, "提示", "请先选择要对比的小组。")
            return
        
        num_splits = len(selected_panels)

        # 清空主显示区
        for i in reversed(range(self.main_display_layout.count())): 
            self.main_display_layout.itemAt(i).widget().setParent(None)

        # 根据分屏数量确定网格布局
        if num_splits <= 1:
            positions = [(0, 0)]
        elif num_splits == 2:
            positions = [(0, 0), (0, 1)]
        elif num_splits <= 4:
            positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
        elif num_splits <= 6:
            positions = [(0, 0), (0, 1), (0, 2), (1, 0), (1, 1), (1, 2)]
        else: # 最多支持9分屏
            positions = [(i // 3, i % 3) for i in range(min(num_splits, 9))]

        # 将选中的小组画面添加到主显示区
        for i, panel_info in enumerate(selected_panels):
            if i < len(positions):
                row, col = positions[i]
                video_player = VideoFrame()
                stream_url = f"{self.rtsp_url}/{panel_info['stream_name']}"
                video_player.play(stream_url)
                self.main_display_layout.addWidget(video_player, row, col)

        # 切换到主显示标签页
        self.tabs.setCurrentWidget(self.broadcast_tab)

    def clear_all_selections(self):
        for panel_info in self.group_panels.values():
            if panel_info['frame'].property("selected"):
                panel_info['frame'].setProperty("selected", False)
                panel_info['frame'].style().polish(panel_info['frame'])
                panel_info['frame'].repaint()
        self.update_dynamic_buttons()

    def handle_group_view_focus(self, ip, hostname, stream_name):
        """Handles the logic when a group's video is double-clicked."""
        if self.focused_group_ip == ip:
            # If already focused, unfocus (return to teacher's view)
            self.reset_main_view_to_teacher()
        else:
            # Focus on this group
            self.focused_group_ip = ip
            self.main_video_player.play(f"{self.rtsp_url}/{stream_name}")
            self.focus_status_label.setText(f"视角：小组 '{hostname}'")
            self.tabs.setCurrentWidget(self.broadcast_tab) # Switch to the main broadcast tab

    def reset_main_view_to_teacher(self):
        """Resets the main video player to the teacher's stream."""
        self.focused_group_ip = None
        # If teacher is broadcasting, show their stream, otherwise show a blank screen
        if self.ffmpeg_process:
            self.main_video_player.play(f"{self.rtsp_url}/teacher")
        else:
            self.main_video_player.stop()
        self.focus_status_label.setText("视角：教师本人")

    def closeEvent(self, event):
        if self.dlan_window:
            self.dlan_window.close()
        self.stop_broadcast()
        self.listener.stop()
        self.sio_manager.disconnect()
        self.sio_thread.quit()
        self.sio_thread.wait()
        if hasattr(self, 'grouping_tab_widget'):
            self.grouping_tab_widget.stop_all_videos()
        if hasattr(self, 'webrtc_thread') and self.webrtc_thread.is_alive():
            self.webrtc_thread.join(timeout=5)
        event.accept()

    def on_group_drawing(self, data):
        """处理从小组端接收到的绘图事件"""
        if self.whiteboard and self.whiteboard.isVisible():
            self.whiteboard.handle_remote_drawing_event(data)

    def on_whiteboard_state_from_group(self, data):
        """处理从小组端接收到的完整白板状态"""
        if self.whiteboard and self.whiteboard.isVisible():
            self.whiteboard.handle_full_state_update(data)

    def on_teacher_drawing(self, data):
        """处理教师白板的绘图事件，并转发给小组端"""
        if self.sio_manager and self.sio_manager.is_connected():
            self.sio_manager.sio.emit('teacher_drawing_event', data)

    def handle_collaboration_toggle(self, is_enabled):
        """处理白板协作模式的切换"""
        if not self.current_course_schedule_id:
            QMessageBox.warning(self, "提示", "请先开始一门课程才能开启协作。")
            # 将按钮状态恢复
            if self.whiteboard:
                self.whiteboard.collaboration_button.setChecked(False)
            return

        event_name = 'start_collaboration' if is_enabled else 'stop_collaboration'
        self.sio_manager.sio.emit(event_name, {'course_id': self.current_course_schedule_id})
        
        mode_text = "开启" if is_enabled else "关闭"
        print(f"已向课程 {self.current_course_schedule_id} 发送 {mode_text}协作 指令")

    def on_teacher_drawing(self, data):
        """处理教师白板的绘图事件，并转发给小组端"""
        if self.sio_manager and self.sio_manager.is_connected():
            self.sio_manager.sio.emit('teacher_drawing_event', data)

def perform_login(api_url, app):
    """执行登录流程，返回成功的TeacherApp实例或None"""
    # 登录重试循环
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        login_dialog = LoginDialog()
        if login_dialog.exec_() != QDialog.Accepted:
            sys.exit(0)

        username, password = login_dialog.get_credentials()

        if not username.strip() or not password.strip():
            QMessageBox.warning(None, "输入错误", "用户名和密码不能为空，请重新输入。")
            retry_count += 1
            continue

        token = None
        login_success = False

        try:
            r = requests.post(f"{api_url}/api/login",
                            json={'user_id': username, 'password': password},
                            verify=False, timeout=10)

            if r.status_code == 200:
                response_data = r.json()
                token = response_data.get('token')
                if token:
                    login_success = True
                    QMessageBox.information(None, "登录成功", f"欢迎，{response_data.get('user_info', {}).get('name', username)}！")
                else:
                    QMessageBox.critical(None, "登录失败", "服务器未返回有效的登录令牌，请重试。")
            elif r.status_code == 401:
                QMessageBox.warning(None, "登录失败", "用户名或密码错误，请检查后重试。")
            elif r.status_code == 403:
                QMessageBox.critical(None, "登录失败", "账户被禁用或无权限，请联系管理员。")
            elif r.status_code >= 500:
                QMessageBox.critical(None, "服务器错误", f"服务器内部错误 ({r.status_code})，请稍后重试。")
            else:
                error_msg = "未知错误"
                try:
                    error_data = r.json()
                    error_msg = error_data.get('message', f"HTTP {r.status_code}")
                except:
                    error_msg = f"HTTP {r.status_code}"
                QMessageBox.critical(None, "登录失败", f"登录失败: {error_msg}")

        except requests.exceptions.Timeout:
            QMessageBox.critical(None, "连接超时", "连接服务器超时，请检查网络连接后重试。")
        except requests.exceptions.ConnectionError:
            QMessageBox.critical(None, "连接失败", "无法连接到服务器，请检查网络连接和服务器地址。")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(None, "网络错误", f"网络请求失败: {str(e)}")

        if login_success and token:
            try:
                ex = TeacherApp(token)
                ex.show()
                return ex  # 返回TeacherApp实例
            except Exception as e:
                QMessageBox.critical(None, "启动失败", f"应用程序启动失败: {str(e)}")
                retry_count += 1
                continue
        else:
            retry_count += 1

        # except Exception as e:
        #     QMessageBox.critical(None, "未知错误", f"登录过程中发生未知错误: {str(e)}")
        #     retry_count += 1

        if retry_count >= max_retries:
            break

    # 超过最大重试次数
    if retry_count >= max_retries:
        reply = QMessageBox.question(None, "登录失败",
                                   f"已尝试 {max_retries} 次登录失败。\n\n可能的解决方案：\n"
                                   "1. 检查网络连接\n"
                                   "2. 确认服务器地址正确\n"
                                   "3. 验证用户名和密码\n"
                                   "4. 联系系统管理员\n\n"
                                   "是否要重新尝试登录？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            # 递归调用，重新开始登录流程
            return perform_login(api_url, app)

    return None


if __name__ == '__main__':
    app = QApplication(sys.argv)

    # 读取配置
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.ini')

    config = configparser.ConfigParser()
    config.read(config_path)
    server_host = config['Network'].get('SERVER_HOST')
    server_port = config['Network'].getint('SERVER_PORT')
    api_url = f"https://{server_host}:{server_port}"

    # 执行登录流程
    teacher_app = perform_login(api_url, app)
    if teacher_app:
        sys.exit(app.exec_())
    else:
        sys.exit(0)
