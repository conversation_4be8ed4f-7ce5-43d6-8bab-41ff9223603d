# teacher/simulate_groups.py
import sys
import time
import argparse
import socket
from PyQt5.QtCore import QCoreApplication

# 确保 QApplication 或 QCoreApplication 实例存在
app = QCoreApplication.instance()
if app is None:
    app = QCoreApplication(sys.argv)

from group.network.udp_broadcaster import UdpBroadcaster

class SimulatedGroup:
    """
    一个模拟的小组端，它只做一件事：
    通过UDP广播自己的状态，让教师端能够发现它。
    """
    def __init__(self, simulated_ip, hostname, discovery_port=6000):
        self.simulated_ip = simulated_ip
        self.hostname = hostname
        
        # 初始化UDP广播器
        self.broadcaster = UdpBroadcaster(broadcast_port=discovery_port)
        
        # 重写广播器默认的IP和主机名，以使用我们的模拟值
        self.broadcaster.ip_address = self.simulated_ip
        self.broadcaster.hostname = self.hostname

    def start(self):
        """启动广播线程"""
        print(f"模拟小组 {self.hostname} ({self.simulated_ip}) 开始广播...")
        self.broadcaster.start()

    def stop(self):
        """停止广播线程"""
        self.broadcaster.stop()
        print(f"模拟小组 {self.hostname} ({self.simulated_ip}) 已停止。")


def run_simulation(num_groups, discovery_port):
    """
    启动并运行指定数量的模拟小组。
    """
    if num_groups <= 0:
        print("错误：小组数量必须大于0。")
        return

    simulated_groups = []
    for i in range(num_groups):
        # 为了确保IP的唯一性，我们从 ************* 开始递增
        simulated_ip = f"192.168.1.{100 + i}"
        hostname = f"SimGroup-{i+1}"
        
        group = SimulatedGroup(simulated_ip, hostname, discovery_port)
        group.start()
        simulated_groups.append(group)
        # 短暂延时以错开启动时间
        time.sleep(0.1)

    print(f"已成功启动 {num_groups} 个模拟小组。")
    print("教师端现在应该能发现它们了。按 Ctrl+C 停止模拟。")

    try:
        # 让主线程保持运行，等待中断信号
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止所有模拟小组...")
        for group in simulated_groups:
            group.stop()
        print("模拟已停止。")
        # 退出Qt事件循环
        QCoreApplication.quit()

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="模拟多个小组端在线，用于测试教师端功能。")
    parser.add_argument(
        "num_groups",
        type=int,
        help="要模拟的小组数量。"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=6000,
        help="教师端用于发现的UDP端口号 (必须与教师端配置一致)。"
    )
    args = parser.parse_args()

    # 从教师端配置中读取正确的发现端口
    try:
        import configparser
        import os
        config = configparser.ConfigParser()
        # 路径修正，以适应从不同位置运行脚本
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, '..', 'config.ini')
        if not os.path.exists(config_path):
             config_path = os.path.join(script_dir, '..', '..', 'config.ini') # 如果在 teacher/features
             if not os.path.exists(config_path):
                config_path = 'config.ini' # 如果在项目根目录

        config.read(config_path)
        discovery_port = config['Network'].getint('DISCOVERY_PORT', 6000)
        print(f"使用配置文件中的发现端口: {discovery_port}")
    except Exception as e:
        print(f"无法读取配置文件，将使用命令行或默认端口: {args.port}。错误: {e}")
        discovery_port = args.port

    run_simulation(args.num_groups, discovery_port)
    
    # 启动Qt事件循环（对于非GUI线程是必要的）
    sys.exit(app.exec_())