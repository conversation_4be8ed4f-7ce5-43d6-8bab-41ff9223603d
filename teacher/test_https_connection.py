#!/usr/bin/env python3
"""
测试HTTPS连接脚本
用于验证客户端是否能正常连接到HTTPS服务器
"""
import requests
import urllib3
import configparser
import os

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_https_connection():
    """测试HTTPS连接"""
    # 读取配置
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.ini')
    config = configparser.ConfigParser()
    config.read(config_path)
    
    server_host = config['Network'].get('SERVER_HOST')
    server_port = config['Network'].getint('SERVER_PORT')
    https_url = f"https://{server_host}:{server_port}"
    http_url = f"http://{server_host}:{server_port}"

    print(f"测试HTTPS连接到: {https_url}")
    print(f"测试HTTP连接到: {http_url}")

    # 先测试HTTP
    print("\n=== HTTP测试 ===")
    test_url_connection(http_url, "HTTP")

    # 再测试HTTPS
    print("\n=== HTTPS测试 ===")
    test_url_connection(https_url, "HTTPS")

def test_url_connection(api_url, protocol):

    try:
        # 测试基本连接
        print(f"1. 测试基本{protocol}连接...")
        response = requests.get(f"{api_url}/", verify=False, timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应时间: {response.elapsed.total_seconds():.2f}秒")

        # 测试登录端点
        print(f"\n2. 测试{protocol}登录端点...")
        login_data = {'user_id': 'T001', 'password': '123456'}
        response = requests.post(f"{api_url}/api/login", json=login_data, verify=False, timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text[:200]}...")

        # 如果登录成功，测试其他端点
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get('token')
            if token:
                print(f"\n3. 测试{protocol}认证端点...")
                headers = {'Authorization': f'Bearer {token}'}
                response = requests.get(f"{api_url}/api/teacher/courses", headers=headers, verify=False, timeout=5)
                print(f"   状态码: {response.status_code}")
                print(f"   响应内容: {response.text[:200]}...")

        print(f"\n✅ {protocol}连接测试完成")
        
    except requests.exceptions.SSLError as e:
        print(f"❌ SSL错误: {e}")
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
    except requests.exceptions.Timeout as e:
        print(f"❌ 超时错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_socketio_connection():
    """测试SocketIO连接"""
    import socketio
    
    # 读取配置
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.ini')
    config = configparser.ConfigParser()
    config.read(config_path)
    
    server_host = config['Network'].get('SERVER_HOST')
    server_port = config['Network'].getint('SERVER_PORT')
    api_url = f"https://{server_host}:{server_port}"
    
    print(f"\n测试SocketIO连接到: {api_url}")
    
    try:
        # 配置HTTP会话
        http_session = requests.Session()
        http_session.verify = False
        
        # 创建SocketIO客户端
        sio = socketio.Client(
            http_session=http_session,
            reconnection=True,
            reconnection_attempts=3,
            reconnection_delay=1
        )
        
        @sio.event
        def connect():
            print("✅ SocketIO连接成功")
        
        @sio.event
        def connect_error(data):
            print(f"❌ SocketIO连接错误: {data}")
        
        @sio.event
        def disconnect():
            print("ℹ️ SocketIO连接断开")
        
        print("正在连接SocketIO...")
        sio.connect(api_url, wait_timeout=30)
        
        # 等待一下然后断开
        import time
        time.sleep(2)
        sio.disconnect()
        
        print("✅ SocketIO连接测试完成")
        
    except Exception as e:
        print(f"❌ SocketIO连接错误: {e}")

if __name__ == "__main__":
    print("开始HTTPS连接测试...")
    test_https_connection()
    test_socketio_connection()
    print("\n测试完成！")
