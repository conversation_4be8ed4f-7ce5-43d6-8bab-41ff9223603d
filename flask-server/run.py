#!/usr/bin/env python3
"""
Flask应用启动文件
"""
import eventlet
eventlet.monkey_patch()

import os
import logging
from app import create_app
import eventlet.wsgi

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 为 engineio 和 socketio 库设置更详细的日志
logging.getLogger('engineio').setLevel(logging.DEBUG)
logging.getLogger('socketio').setLevel(logging.DEBUG)

# 从环境变量获取配置，默认为development
config_name = os.environ.get('FLASK_CONFIG', 'development')

# 创建应用实例
# create_app 返回的是 Flask app 和 socketio 实例
app, socketio = create_app(config_name)

if __name__ == '__main__':
    # 从环境变量获取主机和端口配置
    host = os.environ.get('FLASK_HOST', '************')
    port = int(os.environ.get('FLASK_PORT', 5000))
    
    try:
        logging.info(f"启动服务器在 {host}:{port}")
        socketio.run(app, host=host, port=port, debug=True, keyfile='key.pem', certfile='cert.pem')
    except Exception as e:
        logging.error(f"启动服务器失败: {e}")