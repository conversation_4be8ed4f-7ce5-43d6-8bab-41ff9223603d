#
# This file is autogenerated by pip-compile with Python 3.13
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
bidict==0.23.1
    # via python-socketio
blinker==1.9.0
    # via flask
click==8.2.1
    # via flask
dnspython==2.7.0
    # via eventlet
eventlet==0.40.1
    # via -r requirements.in
flask==3.1.1
    # via
    #   flask-cors
    #   flask-socketio
flask-cors==6.0.1
    # via -r requirements.in
flask-socketio==5.5.1
    # via -r requirements.in
greenlet==3.2.3
    # via eventlet
h11==0.16.0
    # via wsproto
itsdangerous==2.2.0
    # via flask
jinja2==3.1.6
    # via flask
markupsafe==3.0.2
    # via
    #   flask
    #   jinja2
    #   werkzeug
python-engineio==4.12.2
    # via python-socketio
python-socketio==5.13.0
    # via flask-socketio
simple-websocket==1.1.0
    # via python-engineio
werkzeug==3.1.3
    # via
    #   flask
    #   flask-cors
wsproto==1.2.0
    # via simple-websocket
