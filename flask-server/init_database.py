import os
from app import create_app
from app.models.database import init_db

# 使用 'development' 配置创建应用上下文
config_name = os.environ.get('FLASK_CONFIG', 'development')
app, _ = create_app(config_name)

# 在应用上下文中执行数据库初始化
with app.app_context():
    db_path = app.config['DATABASE_PATH']
    
    # 强制重新初始化：先删除旧数据库
    if os.path.exists(db_path):
        print(f"找到旧数据库 '{db_path}'，正在删除...")
        os.remove(db_path)
        print("旧数据库已删除。")

    print(f"正在初始化新的数据库于: {db_path}")
    init_db(db_path)
    print("数据库初始化完成。")
