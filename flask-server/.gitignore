# IDE
/.idea/

# Python
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# Project specific
/test/
/data/
/uploads/
/backup/
README.md
deploy.sh
/scripts/

app/static/excalidraw/fonts/
app/static/excalidraw/libs/
app/static/excalidraw/assets/
app/static/excalidraw/screenshots/

app/static/excalidraw/_headers
app/static/excalidraw/sw.js.map
app/static/excalidraw/service-worker.js
app/static/excalidraw/robots.txt
app/static/excalidraw/og-image-3.png
app/static/excalidraw/maskable_icon_x512.png
app/static/excalidraw/maskable_icon_x192.png
app/static/excalidraw/manifest.webmanifest
app/static/excalidraw/favicon.ico
app/static/excalidraw/favicon-32x32.png
app/static/excalidraw/favicon.svg
app/static/excalidraw/favicon-16x16.png
app/static/excalidraw/Cascadia.woff2
app/static/excalidraw/Assistant-Regular.woff2
app/static/excalidraw/apple-touch-icon.png
app/static/excalidraw/android-chrome-512x512.png
app/static/excalidraw/android-chrome-192x192.png
app/static/excalidraw/sitemap.xml
app/static/excalidraw/sw.js
app/static/excalidraw/version.json
app/static/excalidraw/Virgil.woff2
app/static/excalidraw/workbox-3631b453.js
app/static/excalidraw/workbox-3631b453.js.map
