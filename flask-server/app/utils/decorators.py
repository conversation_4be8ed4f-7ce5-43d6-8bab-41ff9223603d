import functools
from flask import session, redirect, url_for, request, g, jsonify, current_app

# 使用简单的token生成替代jose
import hashlib
import json
import base64
import time

class SimpleJWT:
    @staticmethod
    def encode(payload, secret, algorithm='HS256'):
        # 简化的JWT实现
        header = {'typ': 'JWT', 'alg': algorithm}

        # 确保secret是字符串类型
        if isinstance(secret, bytes):
            secret = secret.decode('utf-8')
        elif not isinstance(secret, str):
            secret = str(secret)

        # 转换datetime为timestamp
        if 'exp' in payload and hasattr(payload['exp'], 'timestamp'):
            payload['exp'] = int(payload['exp'].timestamp())
        if 'iat' in payload and hasattr(payload['iat'], 'timestamp'):
            payload['iat'] = int(payload['iat'].timestamp())

        header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
        payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')

        signature_input = f"{header_b64}.{payload_b64}"
        signature = hashlib.sha256((signature_input + secret).encode()).hexdigest()

        return f"{header_b64}.{payload_b64}.{signature}"

    @staticmethod
    def decode(token, secret, algorithms=['HS256']):
        try:
            # 确保secret是字符串类型
            if isinstance(secret, bytes):
                secret = secret.decode('utf-8')
            elif not isinstance(secret, str):
                secret = str(secret)

            parts = token.split('.')
            if len(parts) != 3:
                raise ValueError("Invalid token format")

            header_b64, payload_b64, signature = parts

            # 验证签名
            signature_input = f"{header_b64}.{payload_b64}"
            expected_signature = hashlib.sha256((signature_input + secret).encode()).hexdigest()

            if signature != expected_signature:
                raise ValueError("Invalid signature")

            # 解码payload
            payload_json = base64.urlsafe_b64decode(payload_b64 + '==').decode()
            payload = json.loads(payload_json)

            # 检查过期时间
            if 'exp' in payload and payload['exp'] < time.time():
                raise ValueError("Token expired")

            return payload
        except Exception as e:
            raise ValueError(f"Token decode error: {e}")

jwt = SimpleJWT()

def login_required(user_type=None):
    """登录验证装饰器 (用于Web端，基于Session)"""
    def decorator(view):
        @functools.wraps(view)
        def wrapped_view(**kwargs):
            # 根据 user_type 确定要检查的 session 键
            if user_type == 'teacher':
                if 'teacher_id' not in session:
                    return redirect(url_for('auth.login'))
            elif user_type == 'student':
                if 'student_id' not in session:
                    return redirect(url_for('auth.login'))
            # 如果没有指定 user_type，则检查任一角色是否登录
            elif 'teacher_id' not in session and 'student_id' not in session:
                return redirect(url_for('auth.login'))
            
            # 检查用户类型是否匹配 (如果需要)
            if user_type and session.get('user_type') != user_type:
                # 如果类型不匹配，重定向到统一登录页
                return redirect(url_for('auth.login'))
                
            return view(**kwargs)
        return wrapped_view
    return decorator

def api_login_required(view):
    """
    API登录验证装饰器 (混合模式)。
    优先检查JWT Token，如果不存在则回退到检查Web Session。
    此装饰器只负责认证，不负责权限检查。
    """
    @functools.wraps(view)
    def wrapped_view(**kwargs):
        auth_header = request.headers.get('Authorization')
        
        # 模式一：JWT Token认证 (适用于原生App)
        if auth_header:
            try:
                token_type, token = auth_header.split()
                if token_type.lower() != 'bearer':
                    raise ValueError("Token类型错误")
            except ValueError:
                return jsonify({"status": "error", "message": "无效的Authorization头格式"}), 401

            try:
                payload = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
                g.user_id = payload['sub']
                g.user_type = payload['user_type']
                if g.user_type == 'teacher':
                    g.teacher_id = g.user_id
                elif g.user_type == 'student':
                    g.student_id = g.user_id
            except jwt.ExpiredSignatureError:
                return jsonify({"status": "error", "message": "Token已过期"}), 401
            except jwt.JWTError:
                return jsonify({"status": "error", "message": "无效的Token"}), 401
        
        # 模式二：Web Session认证 (适用于浏览器)
        else:
            g.user_type = session.get('user_type')
            g.user_id = session.get('teacher_id') or session.get('student_id')
            if g.user_type == 'teacher':
                g.teacher_id = g.user_id
            elif g.user_type == 'student':
                g.student_id = g.user_id
            else:
                # 如果两种认证方式都失败
                return jsonify({"status": "error", "message": "缺少认证凭据 (Token或Session)"}), 401

        return view(**kwargs)
    return wrapped_view

def student_required(view):
    """学生登录验证装饰器 (Web端)"""
    @functools.wraps(view)
    def wrapped_view(**kwargs):
        if session.get('user_type') != 'student' or 'student_id' not in session:
            return redirect(url_for('auth.login'))
        return view(**kwargs)
    return wrapped_view

def teacher_required(view):
    """教师登录验证装饰器 (Web端)"""
    @functools.wraps(view)
    def wrapped_view(**kwargs):
        if session.get('user_type') != 'teacher' or 'teacher_id' not in session:
            return redirect(url_for('auth.login'))
        return view(**kwargs)
    return wrapped_view

def admin_required(view):
    """管理员登录验证装饰器 (Web端)"""
    @functools.wraps(view)
    def wrapped_view(**kwargs):
        if session.get('user_type') != 'admin' or 'admin_id' not in session:
            return redirect(url_for('auth.login'))
        return view(**kwargs)
    return wrapped_view
