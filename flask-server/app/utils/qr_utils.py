"""
二维码生成工具
"""
import qrcode
import io
import base64
from PIL import Image


def generate_qr_code(data, size=10, border=4):
    """
    生成二维码
    
    Args:
        data: 要编码的数据
        size: 二维码大小
        border: 边框大小
    
    Returns:
        base64编码的二维码图片
    """
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=size,
        border=border,
    )
    qr.add_data(data)
    qr.make(fit=True)

    # 创建二维码图片
    img = qr.make_image(fill_color="black", back_color="white")
    
    # 转换为base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    
    img_base64 = base64.b64encode(buffer.getvalue()).decode()
    return f"data:image/png;base64,{img_base64}"


def generate_login_qr_code(teacher_id, token):
    """
    生成教师登录二维码
    
    Args:
        teacher_id: 教师ID
        token: 登录token
    
    Returns:
        base64编码的二维码图片
    """
    login_data = {
        "type": "teacher_login",
        "teacher_id": teacher_id,
        "token": token,
        "timestamp": int(__import__('time').time())
    }
    
    # 将数据转换为JSON字符串
    import json
    qr_data = json.dumps(login_data)
    
    return generate_qr_code(qr_data)


def generate_attendance_qr_code(course_schedule_id):
    """
    生成签到二维码
    
    Args:
        course_schedule_id: 课程安排ID
    
    Returns:
        base64编码的二维码图片
    """
    attendance_data = {
        "type": "attendance",
        "course_schedule_id": course_schedule_id,
        "timestamp": int(__import__('time').time())
    }
    
    # 将数据转换为JSON字符串
    import json
    qr_data = json.dumps(attendance_data)
    
    return generate_qr_code(qr_data)
