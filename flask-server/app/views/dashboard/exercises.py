"""
仪表盘习题相关路由
"""
from flask import render_template, jsonify, session, request
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import uuid
import json
from . import dashboard_bp

@dashboard_bp.route('/exercises')
@teacher_required
def exercises():
    """习题库页面"""
    return render_template("dashboard/exercises.html", current_page="exercises")

@dashboard_bp.route('/get_exercises', methods=['GET'])
@teacher_required
def get_exercises():
    """获取习题列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 获取筛选参数
        keyword = request.args.get('keyword', '').strip()
        types = request.args.get('types', '').strip()
        difficulties = request.args.get('difficulties', '').strip()

        # 构建基础查询
        base_query = """
            SELECT id, type, question, options, answer, difficulty, created_at, updated_at
            FROM exercises
            WHERE teacher_id = ?
        """
        params = [session.get('teacher_id')]

        # 添加搜索条件
        if keyword:
            base_query += " AND question LIKE ?"
            params.append(f'%{keyword}%')

        # 添加题型筛选
        if types:
            type_list = types.split(',')
            placeholders = ','.join(['?' for _ in type_list])
            base_query += f" AND type IN ({placeholders})"
            params.extend(type_list)

        # 添加难度筛选
        if difficulties:
            difficulty_list = difficulties.split(',')
            placeholders = ','.join(['?' for _ in difficulty_list])
            base_query += f" AND difficulty IN ({placeholders})"
            params.extend(difficulty_list)

        # 添加排序
        base_query += " ORDER BY created_at DESC"

        cursor.execute(base_query, params)
        exercises_data = cursor.fetchall()

        # 转换为字典列表
        exercises = []
        for row in exercises_data:
            exercise = dict(row)
            # 解析选项（如果是JSON字符串）
            if exercise['options']:
                try:
                    exercise['options'] = json.loads(exercise['options'])
                except:
                    pass
            exercises.append(exercise)

        conn.close()
        return jsonify({"status": "success", "exercises": exercises})

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取习题失败: {str(e)}"}), 500

@dashboard_bp.route('/get_exercise/<exercise_id>', methods=['GET'])
@teacher_required
def get_exercise(exercise_id):
    """获取单个习题详情"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 查询习题详情，验证权限
        cursor.execute("""
            SELECT id, type, question, options, answer, difficulty, folder_path, created_at, updated_at
            FROM exercises
            WHERE id = ? AND teacher_id = ?
        """, (exercise_id, session.get('teacher_id')))

        exercise_row = cursor.fetchone()
        if not exercise_row:
            conn.close()
            return jsonify({"status": "error", "message": "习题不存在或无权限访问"}), 404

        exercise = dict(exercise_row)

        # 解析选项（如果是JSON字符串）
        if exercise['options']:
            try:
                exercise['options'] = json.loads(exercise['options'])
            except:
                pass

        conn.close()
        return jsonify({"status": "success", "exercise": exercise})

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取习题详情失败: {str(e)}"}), 500

@dashboard_bp.route('/save_exercise', methods=['POST'])
@teacher_required
def save_exercise():
    """保存新习题"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        conn = get_db()
        cursor = conn.cursor()

        # 生成唯一ID
        exercise_id = str(uuid.uuid4())

        # 处理选项数据
        options_json = json.dumps(data.get('options', []), ensure_ascii=False) if data.get('options') else None

        # 插入习题
        cursor.execute("""
            INSERT INTO exercises (id, teacher_id, type, question, options, answer, difficulty, folder_path, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            exercise_id,
            session.get('teacher_id'),
            data.get('type'),
            data.get('question'),
            options_json,
            data.get('answer'),
            data.get('difficulty'),
            data.get('folder_path', '/'),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "习题保存成功", "exercise_id": exercise_id})

    except Exception as e:
        return jsonify({"status": "error", "message": f"保存习题失败: {str(e)}"}), 500

@dashboard_bp.route('/update_exercise/<exercise_id>', methods=['PUT'])
@teacher_required
def update_exercise(exercise_id):
    """更新习题"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        conn = get_db()
        cursor = conn.cursor()

        # 验证权限
        cursor.execute("SELECT id FROM exercises WHERE id = ? AND teacher_id = ?",
                      (exercise_id, session.get('teacher_id')))
        if not cursor.fetchone():
            conn.close()
            return jsonify({"status": "error", "message": "习题不存在或无权限访问"}), 404

        # 处理选项数据
        options_json = json.dumps(data.get('options', []), ensure_ascii=False) if data.get('options') else None

        # 更新习题
        cursor.execute("""
            UPDATE exercises
            SET type = ?, question = ?, options = ?, answer = ?, difficulty = ?, folder_path = ?, updated_at = ?
            WHERE id = ? AND teacher_id = ?
        """, (
            data.get('type'),
            data.get('question'),
            options_json,
            data.get('answer'),
            data.get('difficulty'),
            data.get('folder_path', '/'),
            datetime.now().isoformat(),
            exercise_id,
            session.get('teacher_id')
        ))

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "习题更新成功"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"更新习题失败: {str(e)}"}), 500

@dashboard_bp.route('/delete_exercise/<exercise_id>', methods=['DELETE'])
@teacher_required
def delete_exercise(exercise_id):
    """删除习题"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 验证权限并删除
        cursor.execute("DELETE FROM exercises WHERE id = ? AND teacher_id = ?",
                      (exercise_id, session.get('teacher_id')))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({"status": "error", "message": "习题不存在或无权限访问"}), 404

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "习题删除成功"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"删除习题失败: {str(e)}"}), 500
