"""
仪表盘试卷相关路由
"""
from flask import render_template, jsonify, session, request
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import uuid
import json
from . import dashboard_bp

@dashboard_bp.route('/papers')
@teacher_required
def papers():
    """试卷库页面"""
    return render_template("dashboard/papers.html", current_page="papers")

@dashboard_bp.route('/add_paper', methods=['GET', 'POST'])
@teacher_required
def add_paper():
    """新增试卷"""
    if request.method == 'POST':
        try:
            paper = request.get_json()
            if not paper:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            # 生成唯一ID（如果没有提供）
            if 'id' not in paper:
                paper['id'] = str(uuid.uuid4())

            conn = get_db()
            cursor = conn.cursor()

            # 试卷存储到资源库，不关联课程

            # 将整个试卷数据转换为JSON字符串存储到papers表
            cursor.execute(
                "INSERT INTO papers (id, title, description, teacher_id, created_at, data) VALUES (?, ?, ?, ?, ?, ?)",
                (
                    paper['id'],
                    paper.get('title', '未命名试卷'),
                    paper.get('description', ''),
                    session.get('teacher_id'),
                    datetime.now().isoformat(),
                    json.dumps(paper, ensure_ascii=False)
                )
            )

            conn.commit()
            conn.close()

            return jsonify({"status": "success"})
        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    # GET 请求：处理课程ID参数
    course_id = request.args.get('course_id')
    conn = get_db()
    cursor = conn.cursor()

    # 如果提供了course_id，获取该课程的信息并验证权限
    current_course = None
    if course_id:
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                    cl.name as classroom_name, cls.name as class_name,
                    cs.day_of_week, cs.start_time, cs.end_time
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (course_id, session.get('teacher_id')))
        current_course = cursor.fetchone()

    # 获取当前教师的所有课程安排（用于备选）
    cursor.execute("""
        SELECT cs.id, c.name as course_name, c.code as course_code,
                cl.name as classroom_name, cls.name as class_name,
                cs.day_of_week, cs.start_time, cs.end_time
        FROM course_schedules cs
        JOIN courses c ON cs.course_id = c.id
        JOIN classrooms cl ON cs.classroom_id = cl.id
        JOIN classes cls ON cs.class_id = cls.id
        WHERE cs.teacher_id = ?
        ORDER BY c.name, cs.day_of_week, cs.start_time
    """, (session.get('teacher_id'),))

    course_schedules = cursor.fetchall()
    conn.close()

    return render_template("dashboard/add_paper.html",
                          course_schedules=course_schedules,
                          current_course=current_course,
                          current_page="add_paper")

@dashboard_bp.route('/get_papers', methods=['GET'])
@teacher_required
def get_papers():
    """获取试卷列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 获取筛选参数
        keyword = request.args.get('keyword', '').strip()
        resource_only = request.args.get('resource_only', '').lower() == 'true'

        # 构建基础查询
        if resource_only:
            # 只查询资源库中的试卷
            base_query = """
                SELECT id, title, description, created_at, data
                FROM papers
                WHERE teacher_id = ?
            """
            params = [session.get('teacher_id')]
        else:
            # 查询所有试卷（包括作业中的试卷）
            base_query = """
                SELECT id, title, description, created_at, data, 'paper' as type
                FROM papers
                WHERE teacher_id = ?
                UNION ALL
                SELECT id, title, description, created_at, data, 'homework' as type
                FROM homework
                WHERE teacher_id = ?
            """
            params = [session.get('teacher_id'), session.get('teacher_id')]

        # 添加搜索条件
        if keyword:
            if resource_only:
                base_query += " AND title LIKE ?"
                params.append(f'%{keyword}%')
            else:
                # 对于UNION查询，需要重新构建
                base_query = """
                    SELECT id, title, description, created_at, data, 'paper' as type
                    FROM papers
                    WHERE teacher_id = ? AND title LIKE ?
                    UNION ALL
                    SELECT id, title, description, created_at, data, 'homework' as type
                    FROM homework
                    WHERE teacher_id = ? AND title LIKE ?
                """
                params = [session.get('teacher_id'), f'%{keyword}%', session.get('teacher_id'), f'%{keyword}%']

        # 添加排序
        base_query += " ORDER BY created_at DESC"

        cursor.execute(base_query, params)
        papers_data = cursor.fetchall()

        # 转换为字典列表
        papers = []
        for row in papers_data:
            paper = dict(row)
            # 解析试卷数据
            if paper.get('data'):
                try:
                    paper_data = json.loads(paper['data'])
                    paper.update(paper_data)
                except:
                    pass
            # 设置类型
            if 'type' not in paper:
                paper['type'] = '试卷'
            papers.append(paper)

        conn.close()
        return jsonify({"status": "success", "papers": papers})

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取试卷失败: {str(e)}"}), 500

@dashboard_bp.route('/paper/<paper_id>', methods=['GET'])
@teacher_required
def get_paper(paper_id):
    """获取单个试卷"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 先尝试从papers表查询
        cursor.execute("SELECT data FROM papers WHERE id = ? AND teacher_id = ?", (paper_id, session.get('teacher_id')))
        paper_row = cursor.fetchone()

        # 如果papers表中没有，再从homework表查询
        if not paper_row:
            cursor.execute("SELECT data FROM homework WHERE id = ? AND teacher_id = ?", (paper_id, session.get('teacher_id')))
            paper_row = cursor.fetchone()

        conn.close()

        if not paper_row:
            return jsonify({"status": "error", "message": "试卷不存在"}), 404

        # 返回试卷数据
        paper = json.loads(paper_row[0])

        return jsonify(paper)
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@dashboard_bp.route('/paper/<paper_id>', methods=['DELETE'])
@teacher_required
def delete_paper(paper_id):
    """删除试卷"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 先尝试从papers表删除
        cursor.execute("DELETE FROM papers WHERE id = ? AND teacher_id = ?",
                      (paper_id, session.get('teacher_id')))

        deleted_count = cursor.rowcount

        # 如果papers表中没有，再尝试从homework表删除
        if deleted_count == 0:
            cursor.execute("DELETE FROM homework WHERE id = ? AND teacher_id = ?",
                          (paper_id, session.get('teacher_id')))
            deleted_count = cursor.rowcount

        if deleted_count == 0:
            conn.close()
            return jsonify({"status": "error", "message": "试卷不存在或无权限访问"}), 404

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "试卷删除成功"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"删除试卷失败: {str(e)}"}), 500
