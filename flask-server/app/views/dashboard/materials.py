"""
仪表盘课件相关路由
"""
from flask import render_template, jsonify, session, request, send_file
from app.models.database import get_db
from app.utils.decorators import teacher_required
from app.services.file_service import FileService
from datetime import datetime
import uuid
import os
from flask import current_app
from . import dashboard_bp

@dashboard_bp.route('/materials')
@teacher_required
def materials():
    """课件库页面"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        # 获取文件夹树结构
        success, message, folders = file_service.get_folder_tree(teacher_id)
        if not success:
            folders = []

        # 获取课程安排用于分配功能
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT cs.id, c.name as course_name, cl.name as class_name, cs.day_of_week, cs.start_time, cs.end_time
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classes cl ON cs.class_id = cl.id
            WHERE cs.teacher_id = ?
            ORDER BY cs.day_of_week, cs.start_time
        """, (teacher_id,))

        course_schedules = cursor.fetchall()
        conn.close()

        return render_template("dashboard/materials.html",
                             current_page="materials",
                             folders=folders,
                             course_schedules=course_schedules)
    except Exception as e:
        current_app.logger.error(f"Materials page error: {str(e)}")
        return render_template("dashboard/materials.html",
                             current_page="materials",
                             folders=[],
                             course_schedules=[])

@dashboard_bp.route('/get_folders', methods=['GET'])
@teacher_required
def get_folders():
    """获取文件夹树结构"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message, folders = file_service.get_folder_tree(teacher_id)
        if success:
            return jsonify({"status": "success", "folders": folders})
        else:
            return jsonify({"status": "error", "message": message}), 500

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取文件夹失败: {str(e)}"}), 500

@dashboard_bp.route('/get_files', methods=['GET'])
@teacher_required
def get_files():
    """获取文件列表"""
    try:
        teacher_id = session.get('teacher_id')
        folder_id = request.args.get('folder_id', type=int)
        search_term = request.args.get('search', '').strip()

        file_service = FileService()
        success, message, files = file_service.get_files_in_folder(teacher_id, folder_id, search_term)

        if success:
            return jsonify({"status": "success", "files": files})
        else:
            return jsonify({"status": "error", "message": message}), 500

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取文件列表失败: {str(e)}"}), 500

@dashboard_bp.route('/upload_files', methods=['POST'])
@teacher_required
def upload_files():
    """上传文件"""
    try:
        if 'files' not in request.files:
            return jsonify({"status": "error", "message": "没有选择文件"}), 400

        files = request.files.getlist('files')
        if not files or all(file.filename == '' for file in files):
            return jsonify({"status": "error", "message": "没有选择文件"}), 400

        teacher_id = session.get('teacher_id')
        folder_id = request.form.get('folder_id', type=int)

        file_service = FileService()
        uploaded_files = []
        failed_files = []

        for file in files:
            if file.filename == '':
                continue

            success, message, file_id = file_service.upload_file(teacher_id, file, folder_id)
            if success:
                uploaded_files.append(file.filename)
            else:
                failed_files.append(f"{file.filename}: {message}")

        if uploaded_files:
            response_message = f"成功上传 {len(uploaded_files)} 个文件"
            if failed_files:
                response_message += f"，{len(failed_files)} 个文件上传失败"

            return jsonify({
                "status": "success",
                "message": response_message,
                "uploaded_files": uploaded_files,
                "failed_files": failed_files
            })
        else:
            return jsonify({
                "status": "error",
                "message": "所有文件上传失败",
                "failed_files": failed_files
            }), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"上传失败: {str(e)}"}), 500

@dashboard_bp.route('/delete_file/<int:file_id>', methods=['DELETE'])
@teacher_required
def delete_file(file_id):
    """删除文件"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.delete_file(teacher_id, file_id)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"删除文件失败: {str(e)}"}), 500

@dashboard_bp.route('/delete_files', methods=['DELETE'])
@teacher_required
def delete_files():
    """批量删除文件"""
    try:
        data = request.get_json()
        if not data or 'file_ids' not in data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_ids = data['file_ids']
        if not isinstance(file_ids, list) or not file_ids:
            return jsonify({"status": "error", "message": "文件ID列表不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        deleted_count = 0
        failed_count = 0

        for file_id in file_ids:
            success, message = file_service.delete_file(teacher_id, file_id)
            if success:
                deleted_count += 1
            else:
                failed_count += 1

        if deleted_count > 0:
            message = f"成功删除 {deleted_count} 个文件"
            if failed_count > 0:
                message += f"，{failed_count} 个文件删除失败"
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": "所有文件删除失败"}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"批量删除失败: {str(e)}"}), 500

@dashboard_bp.route('/create_folder', methods=['POST'])
@teacher_required
def create_folder():
    """创建文件夹"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        folder_name = data.get('folder_name', '').strip()
        parent_id = data.get('parent_id')
        if parent_id is not None:
            try:
                parent_id = int(parent_id)
            except (ValueError, TypeError):
                parent_id = None

        if not folder_name:
            return jsonify({"status": "error", "message": "文件夹名称不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message, folder_id = file_service.create_folder(teacher_id, folder_name, parent_id)

        if success:
            return jsonify({"status": "success", "message": message, "folder_id": folder_id})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"创建文件夹失败: {str(e)}"}), 500

@dashboard_bp.route('/delete_folder/<int:folder_id>', methods=['DELETE'])
@teacher_required
def delete_folder(folder_id):
    """删除文件夹"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.delete_folder(teacher_id, folder_id)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"删除文件夹失败: {str(e)}"}), 500

@dashboard_bp.route('/move_file', methods=['POST'])
@teacher_required
def move_file():
    """移动文件到指定文件夹"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_id = data.get('file_id')
        if file_id is not None:
            try:
                file_id = int(file_id)
            except (ValueError, TypeError):
                file_id = None
        target_folder_id = data.get('target_folder_id')
        if target_folder_id is not None:
            try:
                target_folder_id = int(target_folder_id)
            except (ValueError, TypeError):
                target_folder_id = None

        if not file_id:
            return jsonify({"status": "error", "message": "文件ID不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.move_file(teacher_id, file_id, target_folder_id)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"移动文件失败: {str(e)}"}), 500

@dashboard_bp.route('/move_files', methods=['POST'])
@teacher_required
def move_files():
    """批量移动文件"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_ids = data.get('file_ids', [])
        target_folder_id = data.get('target_folder_id', type=int)

        if not file_ids:
            return jsonify({"status": "error", "message": "文件ID列表不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        moved_count = 0
        failed_count = 0

        for file_id in file_ids:
            success, message = file_service.move_file(teacher_id, file_id, target_folder_id)
            if success:
                moved_count += 1
            else:
                failed_count += 1

        if moved_count > 0:
            message = f"成功移动 {moved_count} 个文件"
            if failed_count > 0:
                message += f"，{failed_count} 个文件移动失败"
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": "所有文件移动失败"}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"批量移动失败: {str(e)}"}), 500

@dashboard_bp.route('/rename_file', methods=['POST'])
@teacher_required
def rename_file():
    """重命名文件"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_id = data.get('file_id')
        if file_id is not None:
            try:
                file_id = int(file_id)
            except (ValueError, TypeError):
                file_id = None
        new_name = data.get('new_name', '').strip()

        if not file_id:
            return jsonify({"status": "error", "message": "文件ID不能为空"}), 400

        if not new_name:
            return jsonify({"status": "error", "message": "新文件名不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.rename_file(teacher_id, file_id, new_name)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"重命名文件失败: {str(e)}"}), 500

@dashboard_bp.route('/rename_folder', methods=['POST'])
@teacher_required
def rename_folder():
    """重命名文件夹"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        folder_id = data.get('folder_id')
        if folder_id is not None:
            try:
                folder_id = int(folder_id)
            except (ValueError, TypeError):
                folder_id = None
        new_name = data.get('new_name', '').strip()

        if not folder_id:
            return jsonify({"status": "error", "message": "文件夹ID不能为空"}), 400

        if not new_name:
            return jsonify({"status": "error", "message": "新文件夹名不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.rename_folder(teacher_id, folder_id, new_name)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"重命名文件夹失败: {str(e)}"}), 500

@dashboard_bp.route('/download_file/<int:file_id>')
@teacher_required
def download_file(file_id):
    """下载文件"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message, file_info = file_service.get_file_info(teacher_id, file_id)

        if not success:
            return jsonify({"status": "error", "message": message}), 404

        # 构建文件路径
        file_path = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), file_info['file_path'])

        if not os.path.exists(file_path):
            return jsonify({"status": "error", "message": "文件不存在"}), 404

        return send_file(file_path,
                        as_attachment=True,
                        download_name=file_info['original_filename'],
                        mimetype=file_info['mime_type'])

    except Exception as e:
        return jsonify({"status": "error", "message": f"下载文件失败: {str(e)}"}), 500

@dashboard_bp.route('/assign_file_to_class', methods=['POST'])
@teacher_required
def assign_file_to_class():
    """将文件分配给课堂"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_id = data.get('file_id')
        if file_id is not None:
            try:
                file_id = int(file_id)
            except (ValueError, TypeError):
                file_id = None
        schedule_id = data.get('schedule_id')

        if not file_id or not schedule_id:
            return jsonify({"status": "error", "message": "文件ID和课程安排ID不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        # 获取文件信息
        success, message, file_info = file_service.get_file_info(teacher_id, file_id)
        if not success:
            return jsonify({"status": "error", "message": message}), 404

        # 验证课程安排权限
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id FROM course_schedules
            WHERE id = ? AND teacher_id = ?
        """, (schedule_id, teacher_id))

        if not cursor.fetchone():
            conn.close()
            return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

        # 创建课件记录关联到课程
        material_id = str(uuid.uuid4())
        cursor.execute("""
            INSERT INTO teacher_materials (id, teacher_id, title, file_path, file_type, file_size, folder_path, upload_time, description, course_schedule_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            material_id,
            teacher_id,
            file_info['original_filename'],
            file_info['file_path'],
            file_info['mime_type'],
            file_info['file_size'],
            '/',
            datetime.now().isoformat(),
            '',
            schedule_id
        ))

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "文件分配成功"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"分配文件失败: {str(e)}"}), 500
