import os
from flask import Blueprint, jsonify, request
from app.services.file_service import FileService

materials_bp = Blueprint('materials', __name__, url_prefix='/api/materials')

@materials_bp.route('/<string:teacher_id>', methods=['GET'])
def get_teacher_files(teacher_id):
    """
    获取指定教师的文件和文件夹列表。
    可以按文件夹ID进行过滤。
    """
    folder_id = request.args.get('folder_id', None)
    search_term = request.args.get('q', None)
    
    file_service = FileService()
    
    # 获取文件
    success, message, files = file_service.get_files_in_folder(
        teacher_id=teacher_id, 
        folder_id=folder_id, 
        search_term=search_term
    )
    
    if not success:
        return jsonify({"error": message}), 500
        
    # 为了统一，将文件和文件夹都视为 "items"
    items = []
    for f in files:
        items.append({
            "id": f['id'],
            "name": f['original_filename'],
            "type": "file",
            "size": f['file_size'],
            "path": f['file_path'],
            "created_at": f['upload_at']
        })
        
    return jsonify(items)

@materials_bp.route('/<string:teacher_id>/folders', methods=['GET'])
def get_teacher_folders(teacher_id):
    """
    获取指定教师的文件夹树。
    """
    file_service = FileService()
    
    # 获取文件夹树
    success, message, folders = file_service.get_folder_tree(teacher_id)
    
    if not success:
        return jsonify({"error": message}), 500
        
    # 构建层级结构的逻辑可以放在前端，这里只返回列表
    return jsonify(folders)
