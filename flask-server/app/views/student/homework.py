from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from app.models.database import get_db
from app.utils.decorators import student_required
import json
from datetime import datetime

def register_routes(bp):
    """注册学生作业相关路由"""
    @bp.route('/homework')
    @student_required
    def student_homework():
        """学生作业页面 - 显示所有课程的作业"""
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        if not student:
            conn.close()
            return redirect(url_for('student.index'))

        cursor.execute("""
            SELECT h.id, h.title, h.description, h.created_at, c.name as course_name,
                h.type, h.deadline,
                CASE WHEN hr.id IS NOT NULL THEN 1 ELSE 0 END as is_submitted,
                hr.score, hr.submitted_at
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            JOIN courses c ON cs.course_id = c.id
            LEFT JOIN homework_results hr ON h.id = hr.homework_id AND hr.student_id = ?
            WHERE cs.class_id = ? AND h.status = 'published'
            ORDER BY h.created_at DESC
        """, (student['student_id'], student['class_id']))
        homeworks = cursor.fetchall()
        conn.close()
        return render_template("student/homework.html", student=student, homeworks=homeworks, current_page="homework")

    @bp.route('/exam/do/<exam_id>', methods=['GET'])
    @student_required
    def take_exam(exam_id):
        """
        进入做题页面。
        此函数现在只渲染一个骨架页面，具体题目将由前端通过API加载。
        """
        conn = get_db()
        cursor = conn.cursor()
        
        # 1. 获取学生信息
        cursor.execute("SELECT student_id, name FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        if not student:
            conn.close()
            return redirect(url_for('student.index'))

        # 2. 检查作业是否存在且已发布
        cursor.execute("SELECT title, description FROM homework WHERE id = ? AND status = 'published'", (exam_id,))
        exam_info = cursor.fetchone()
        if not exam_info:
            conn.close()
            return redirect(url_for('student.student_homework'))

        # 3. 检查是否已提交
        cursor.execute("SELECT id FROM homework_results WHERE homework_id = ? AND student_id = ?", (exam_id, student['student_id']))
        if cursor.fetchone():
            conn.close()
            return redirect(url_for('student.student_homework'))

        conn.close()
        
        # 渲染页面，只传递必要的信息
        return render_template(
            "student/student_exam_player.html",
            student=student,
            current_page="homework",
            exam_id=exam_id,
            exam_title=exam_info['title'],
            exam_description=exam_info['description']
        )

    @bp.route('/interactive_quiz/<homework_id>', methods=['GET'])
    @student_required
    def interactive_quiz(homework_id):
        """学生端互动答题页面"""
        conn = get_db()
        cursor = conn.cursor()
        
        # 1. 获取学生信息
        cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        if not student:
            conn.close()
            return redirect(url_for('student.index'))
        
        # 2. 验证作业存在且学生有权限
        cursor.execute("""
            SELECT h.id, h.title, h.data, h.status, h.type
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            JOIN students s ON s.class_id = cs.class_id
            WHERE h.id = ? AND s.student_id = ? AND h.status = 'published' AND h.type = 'interactive'
        """, (homework_id, student['student_id']))
        
        homework = cursor.fetchone()
        if not homework:
            conn.close()
            return redirect(url_for('student.student_homework'))
        
        # 3. 解析作业数据获取题目详情
        homework_data = json.loads(homework['data'])
        if isinstance(homework_data, dict):
            questions = homework_data.get('questions', [])
        elif isinstance(homework_data, list):
            questions = homework_data
        else:
            questions = []
        
        # 4. 获取题目详情
        question_details = []
        for question in questions:
            if isinstance(question, dict):
                # 如果已经是完整的题目数据，统一字段名
                if 'desc' in question and 'content' not in question:
                    question['content'] = question['desc']
                # 处理判断题的选项
                if question.get('type') == 'judge' and not question.get('options'):
                    question['options'] = ['正确', '错误']
                question_details.append(question)
            else:
                # 如果是题目ID，需要从exercises表中获取
                cursor.execute("""
                    SELECT id, type, question, options, difficulty
                    FROM exercises WHERE id = ?
                """, (question,))
                exercise = cursor.fetchone()
                if exercise:
                    exercise_dict = dict(exercise)
                    # 重命名字段以匹配模板
                    exercise_dict['content'] = exercise_dict.pop('question')
                    # 解析选项
                    if exercise_dict.get('options'):
                        try:
                            exercise_dict['options'] = json.loads(exercise_dict['options'])
                        except:
                            exercise_dict['options'] = []
                    else:
                        exercise_dict['options'] = []
                    
                    # 处理判断题的选项
                    if exercise_dict.get('type') == 'judge' and not exercise_dict['options']:
                        exercise_dict['options'] = ['正确', '错误']
                    question_details.append(exercise_dict)
        
        conn.close()
        
        return render_template(
            "student/interactive_quiz.html",
            student=student,
            current_page="homework",
            homework_id=homework_id,
            quiz_title=homework['title'],
            questions=question_details
        )

