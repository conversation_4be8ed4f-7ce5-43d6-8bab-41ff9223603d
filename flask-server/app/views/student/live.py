from flask import render_template, session, redirect, url_for
from app.models.database import get_db
from app.utils.decorators import student_required

def register_routes(bp):
    """注册直播相关路由"""

    @bp.route('/courses')
    @student_required
    def view_courses():
        """学生系统首页 - 显示课程列表"""
        conn = get_db()
        cursor = conn.cursor()
        
        # 获取学生信息
        cursor.execute("SELECT class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        if not student:
            conn.close()
            return redirect(url_for('auth.login'))

        # 获取该学生班级的所有课程安排
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   t.name as teacher_name, cl.name as classroom_name,
                   cls.name as class_name, cs.day_of_week, cs.start_time, 
                   cs.end_time, cs.status
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN teachers t ON cs.teacher_id = t.teacher_id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.class_id = ?
            ORDER BY cs.status, cs.day_of_week, cs.start_time
        """, (student['class_id'],))
        
        courses = cursor.fetchall()
        conn.close()
        
        # 注意：这里需要一个新的模板来展示课程列表
        return render_template("student/course_list.html", courses=courses)

    
    @bp.route('/course/<course_id>')
    @student_required
    def course_detail(course_id):
        """学生进入课堂详情/直播页面"""
        conn = get_db()
        cursor = conn.cursor()
        
        # 获取学生信息
        cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        
        # 获取课程信息，并验证学生是否属于该课程的班级
        cursor.execute("""
            SELECT cs.id, cs.status
            FROM course_schedules cs
            WHERE cs.id = ? AND cs.class_id = ?
        """, (course_id, student['class_id'] if student else None))
        course = cursor.fetchone()
        
        conn.close()
        
        if not student or not course:
            # 如果学生不存在，或课程不存在，或学生不属于该课程，则重定向
            return redirect(url_for('student.index'))

        # 将当前课程ID存入session，以便JS和Socket.IO使用
        session['course_schedule_id'] = course_id

        return render_template("student/player.html", current_page="live", student=student, course=course, course_id=course_id)
