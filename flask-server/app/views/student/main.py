# flask-server/app/views/student/main.py
from flask import render_template, jsonify, request, session, send_from_directory, current_app
from app.utils.decorators import student_required
import os

def register_routes(bp):
    @bp.route('/dashboard')
    @student_required
    def dashboard():
        """
        学生登录后的主仪表盘页面。
        """
        return render_template('student/dashboard.html')

    @bp.route('/screen_share')
    @student_required
    def screen_share():
        """
        学生投屏页面
        """
        return render_template('student/screen_share.html')

    @bp.route('/collaborative_whiteboard')
    @student_required
    def collaborative_whiteboard():
        """
        协同白板页面
        """
        return render_template('student/collaborative_whiteboard.html')

    @bp.route('/excalidraw')
    @student_required
    def excalidraw_proxy():
        """
        Excalidraw代理页面，解决iframe跨域问题
        """
        # 读取原始的index.html文件
        excalidraw_path = os.path.join(current_app.static_folder, 'excalidraw', 'index.html')

        try:
            with open(excalidraw_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 修改资源路径，使其指向我们的代理路由
            modified_content = content.replace('./assets/', '/student/excalidraw/assets/')

            # 替换CDN链接为本地文件（使用开发版本以获得更好的全局变量暴露）
            modified_content = modified_content.replace(
                'https://unpkg.com/react@18/umd/react.production.min.js',
                '/student/excalidraw/libs/react.development.js'
            )
            modified_content = modified_content.replace(
                'https://unpkg.com/react-dom@18/umd/react-dom.production.min.js',
                '/student/excalidraw/libs/react-dom.development.js'
            )
            modified_content = modified_content.replace(
                'https://cdn.socket.io/4.7.5/socket.io.min.js',
                '/student/excalidraw/libs/socket.io.min.js'
            )

            # 添加一个脚本来禁用iframe检查
            iframe_fix_script = '''
            <script>
            // 禁用Excalidraw的iframe检查
            (function() {
                // 方法1: 直接设置window.self等于window.top来欺骗检查
                try {
                    Object.defineProperty(window, 'self', {
                        get: function() { return window.top; },
                        configurable: true
                    });
                } catch(e) {}

                // 方法2: 设置一个全局标志
                window.EXCALIDRAW_ALLOW_IFRAME = true;

                // 方法3: 添加调试信息
                console.log('Iframe fix script loaded');
            })();
            </script>
            '''

            # 在head标签后插入我们的脚本
            modified_content = modified_content.replace('<head>', '<head>' + iframe_fix_script)

            # 在React脚本标签后添加一个脚本来确保全局变量设置
            react_fix_script = '''
            <script>
            // 确保React和ReactDOM正确暴露到全局作用域
            (function() {
                // 等待一小段时间让库文件加载
                setTimeout(function() {
                    console.log('Checking global React setup...', {
                        React: typeof React,
                        ReactDOM: typeof ReactDOM,
                        window_React: typeof window.React,
                        window_ReactDOM: typeof window.ReactDOM
                    });

                    // 如果React没有在全局作用域，尝试从window获取
                    if (typeof React === 'undefined' && typeof window.React !== 'undefined') {
                        window.React = window.React;
                        console.log('Fixed React global reference');
                    }
                    if (typeof ReactDOM === 'undefined' && typeof window.ReactDOM !== 'undefined') {
                        window.ReactDOM = window.ReactDOM;
                        console.log('Fixed ReactDOM global reference');
                    }
                }, 50);
            })();
            </script>
            '''

            # 在Socket.IO脚本标签后插入React修复脚本
            modified_content = modified_content.replace(
                '</script>\n    <script>',
                '</script>' + react_fix_script + '\n    <script>'
            )

            # 替换模块脚本标签为普通脚本标签，并修改检查逻辑
            modified_content = modified_content.replace(
                '<script type="module">',
                '<script>'
            )

            # 替换React检查逻辑，使用延迟检查
            old_check = '''        // 检查必要的依赖
        if (typeof React === 'undefined') {
            showError('React 库加载失败');
        } else if (typeof ReactDOM === 'undefined') {
            showError('ReactDOM 库加载失败');
        } else if (typeof io === 'undefined') {
            showError('Socket.IO 库加载失败');
        } else {
            // 尝试直接使用全局的Excalidraw
            initializeExcalidraw();
        }'''

            new_check = '''        // 延迟检查必要的依赖，给库文件更多时间加载
        function checkDependencies() {
            console.log('Checking dependencies...', {
                React: typeof React,
                ReactDOM: typeof ReactDOM,
                io: typeof io,
                window_self: window.self,
                window_top: window.top,
                self_equals_top: window.self === window.top
            });

            if (typeof React === 'undefined') {
                console.log('React not loaded, retrying...');
                setTimeout(checkDependencies, 200);
                return;
            }
            if (typeof ReactDOM === 'undefined') {
                console.log('ReactDOM not loaded, retrying...');
                setTimeout(checkDependencies, 200);
                return;
            }
            if (typeof io === 'undefined') {
                console.log('Socket.IO not loaded, retrying...');
                setTimeout(checkDependencies, 200);
                return;
            }

            console.log('All dependencies loaded, initializing Excalidraw...');
            // 所有依赖都已加载，初始化Excalidraw
            try {
                initializeExcalidraw();
            } catch (error) {
                console.error('Error initializing Excalidraw:', error);
                showError('Excalidraw初始化失败: ' + error.message);
            }
        }

        // 立即开始检查，不等待DOM
        console.log('Starting dependency check...');
        setTimeout(checkDependencies, 100);'''

            modified_content = modified_content.replace(old_check, new_check)

            return modified_content, 200, {'Content-Type': 'text/html; charset=utf-8'}

        except FileNotFoundError:
            return "Excalidraw文件未找到", 404
        except Exception as e:
            return f"加载Excalidraw失败: {str(e)}", 500

    @bp.route('/excalidraw/assets/<path:filename>')
    @student_required
    def excalidraw_assets(filename):
        """
        代理Excalidraw的静态资源文件
        """
        try:
            assets_path = os.path.join(current_app.static_folder, 'excalidraw', 'assets')

            # 如果是JavaScript文件，我们需要修改其中的iframe检查代码
            if filename.endswith('.js'):
                file_path = os.path.join(assets_path, filename)
                with open(file_path, 'r', encoding='utf-8') as f:
                    js_content = f.read()

                # 查找并替换iframe检查逻辑
                # 这个模式匹配压缩后的JavaScript代码
                import re

                # 模式1: 匹配 let vK=!1;if(window.self!==window.top)...
                pattern1 = r'let vK=!1;if\(window\.self!==window\.top\)[^}]*\}catch\(\)\{\}'
                js_content = re.sub(pattern1, 'let vK=!1;', js_content)

                # 模式2: 匹配可能的其他iframe检查模式
                pattern2 = r'window\.self!==window\.top'
                js_content = re.sub(pattern2, 'false', js_content)

                # 模式3: 查找"I'm not a pretzel!"并替换相关逻辑
                if "I'm not a pretzel!" in js_content:
                    # 找到包含这个字符串的部分并修改
                    pattern3 = r'vK\?"[^"]*":"I\'m not a pretzel!"'
                    js_content = re.sub(pattern3, '"Excalidraw"', js_content)

                return js_content, 200, {'Content-Type': 'application/javascript; charset=utf-8'}

            return send_from_directory(assets_path, filename)
        except Exception as e:
            return f"资源文件加载失败: {str(e)}", 404

    @bp.route('/excalidraw/libs/<path:filename>')
    @student_required
    def excalidraw_libs(filename):
        """
        代理Excalidraw的库文件（React、ReactDOM、Socket.IO）
        """
        try:
            libs_path = os.path.join(current_app.static_folder, 'excalidraw', 'libs')
            return send_from_directory(libs_path, filename)
        except Exception as e:
            return f"库文件加载失败: {str(e)}", 404

    @bp.route('/share_content')
    @student_required
    def share_content():
        """
        内容分享页面
        """
        return render_template('student/share_content.html')

    @bp.route('/api/groups')
    @student_required
    def get_groups():
        """
        获取可用的小组列表
        """
        from app.models.database import get_db

        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取当前进行中的课程的所有分组
            cursor.execute("""
                SELECT DISTINCT cg.id as group_id, cg.group_name, cg.group_description,
                       cs.id as course_schedule_id, c.name as course_name
                FROM class_groups cg
                JOIN course_schedules cs ON cg.course_schedule_id = cs.id
                JOIN courses c ON cs.course_id = c.id
                WHERE cs.status = 'in_progress'
                ORDER BY cg.group_name
            """)

            groups = []
            for row in cursor.fetchall():
                # 从group_description中提取IP地址
                group_ip = None
                if row['group_description'] and 'IP:' in row['group_description']:
                    group_ip = row['group_description'].split('IP:')[1].strip()

                if group_ip:  # 只返回有IP地址的小组
                    group_data = {
                        'id': f"group_{row['group_name']}_{group_ip.replace('.', '_')}",
                        'name': row['group_name'],
                        'status': 'online',  # 假设所有分组都在线，实际可以通过socket检查
                        'ip': group_ip,
                        'hostname': row['group_name'],
                        'course_name': row['course_name'],
                        'course_schedule_id': row['course_schedule_id']
                    }
                    groups.append(group_data)

            conn.close()
            return jsonify({
                'success': True,
                'groups': groups
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取小组列表失败: {str(e)}',
                'groups': []
            })

    @bp.route('/api/my_group')
    @student_required
    def get_my_group():
        """
        获取当前学生的分组信息
        """
        from app.models.database import get_db

        student_id = session.get('student_id')
        if not student_id:
            return jsonify({'success': False, 'message': '未找到学生信息'}), 401

        try:
            conn = get_db()
            cursor = conn.cursor()

            # 查找学生当前的分组（在进行中的课程）
            cursor.execute("""
                SELECT cg.id as group_id, cg.group_name, cg.group_description,
                       cs.id as course_schedule_id, c.name as course_name,
                       cs.status as course_status
                FROM group_members gm
                JOIN class_groups cg ON gm.group_id = cg.id
                JOIN course_schedules cs ON cg.course_schedule_id = cs.id
                JOIN courses c ON cs.course_id = c.id
                WHERE gm.student_id = ? AND cs.status = 'in_progress'
                ORDER BY gm.joined_at DESC
                LIMIT 1
            """, (student_id,))

            group_info = cursor.fetchone()

            if group_info:
                # 获取同组的其他学生
                cursor.execute("""
                    SELECT s.student_id, s.name
                    FROM group_members gm
                    JOIN students s ON gm.student_id = s.student_id
                    WHERE gm.group_id = ? AND gm.student_id != ?
                    ORDER BY s.name
                """, (group_info['group_id'], student_id))

                group_members = [{'student_id': row['student_id'], 'name': row['name']}
                               for row in cursor.fetchall()]

                # 从group_description中提取IP地址
                group_ip = None
                if group_info['group_description'] and 'IP:' in group_info['group_description']:
                    group_ip = group_info['group_description'].split('IP:')[1].strip()

                result = {
                    'success': True,
                    'group': {
                        'group_id': group_info['group_id'],
                        'group_name': group_info['group_name'],
                        'group_ip': group_ip,
                        'course_name': group_info['course_name'],
                        'course_schedule_id': group_info['course_schedule_id'],
                        'members': group_members
                    }
                }
            else:
                result = {
                    'success': True,
                    'group': None,
                    'message': '当前没有分组信息'
                }

            conn.close()
            return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'message': f'获取分组信息失败: {str(e)}'}), 500

    @bp.route('/api/group_by_ip/<group_ip>')
    @student_required
    def get_group_by_ip(group_ip):
        """
        根据小组IP获取小组信息（用于学生端直接连接小组）
        """
        from app.models.database import get_db

        try:
            conn = get_db()
            cursor = conn.cursor()

            # 查找对应IP的小组信息
            cursor.execute("""
                SELECT cg.id as group_id, cg.group_name, cg.group_description,
                       cs.id as course_schedule_id, c.name as course_name,
                       cs.status as course_status
                FROM class_groups cg
                JOIN course_schedules cs ON cg.course_schedule_id = cs.id
                JOIN courses c ON cs.course_id = c.id
                WHERE cg.group_description LIKE ? AND cs.status = 'in_progress'
                ORDER BY cg.created_at DESC
                LIMIT 1
            """, (f'%IP: {group_ip}%',))

            group_info = cursor.fetchone()

            if group_info:
                # 获取小组成员
                cursor.execute("""
                    SELECT s.student_id, s.name
                    FROM group_members gm
                    JOIN students s ON gm.student_id = s.student_id
                    WHERE gm.group_id = ?
                    ORDER BY s.name
                """, (group_info['group_id'],))

                group_members = [{'student_id': row['student_id'], 'name': row['name']}
                               for row in cursor.fetchall()]

                result = {
                    'success': True,
                    'group': {
                        'group_id': group_info['group_id'],
                        'group_name': group_info['group_name'],
                        'group_ip': group_ip,
                        'course_name': group_info['course_name'],
                        'course_schedule_id': group_info['course_schedule_id'],
                        'members': group_members,
                        'socket_group_id': f"group_{group_info['group_name']}_{group_ip.replace('.', '_')}"
                    }
                }
            else:
                result = {
                    'success': False,
                    'message': f'未找到IP为 {group_ip} 的活跃小组'
                }

            conn.close()
            return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'message': f'获取小组信息失败: {str(e)}'}), 500
