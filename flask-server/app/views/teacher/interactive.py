from flask import Blueprint, render_template
from flask_login import login_required, current_user
from app.utils.decorators import teacher_required
from app.services.homework_service import HomeworkService

interactive_bp = Blueprint('interactive', __name__, url_prefix='/teacher/interactive')

@interactive_bp.route('/new')
@login_required
@teacher_required
def new_interactive_quiz():
    """渲染发起互动答题的页面"""
    papers = HomeworkService.get_papers_by_teacher(current_user.teacher_id)
    return render_template('teacher/new_interactive_quiz.html', papers=papers)
