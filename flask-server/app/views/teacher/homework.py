"""
作业管理相关路由
"""
from flask import render_template, request, jsonify, session
from app.models.database import get_db
from app.utils.decorators import teacher_required
from app.services.homework_service import HomeworkService  # 导入服务
from datetime import datetime
import uuid
import json


def register_routes(bp):
    """注册作业管理相关路由"""
    
    @bp.route('/homework')
    @teacher_required
    def homework():
        """课后作业页面"""
        homework_service = HomeworkService()
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return render_template("teacher/homework.html", current_page="homework", current_class=None)

        conn = get_db()
        cursor = conn.cursor()

        # 获取当前课堂信息 (这部分保持不变，因为它属于页面通用信息)
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))
        current_class = cursor.fetchone()
        conn.close()

        if not current_class:
            return render_template("teacher/homework.html", current_page="homework", current_class=None)

        # 使用服务获取作业列表
        success, message, homework_list = homework_service.get_homework_list_for_teacher(
            session.get('teacher_id'), current_class_id
        )
        if not success:
            # 在实际应用中，这里可能需要记录日志或向用户显示错误
            homework_list = []

        return render_template("teacher/homework.html",
                              current_page="homework",
                              current_class=current_class,
                              homework_list=homework_list)

    @bp.route('/import_paper_as_homework', methods=['POST'])
    @teacher_required
    def import_paper_as_homework():
        """从资源库导入试卷作为作业"""
        try:
            paper_id = request.form.get('paper_id')
            title = request.form.get('title')
            deadline = request.form.get('deadline')
            description = request.form.get('description', '')
            course_id = request.form.get('course_id')

            if not all([paper_id, title, course_id, deadline]):
                return jsonify({"status": "error", "message": "缺少必要参数"}), 400

            homework_service = HomeworkService()
            success, message, homework_id = homework_service.create_homework_from_paper(
                session.get('teacher_id'), course_id, paper_id, title, deadline, description
            )

            if success:
                return jsonify({"status": "success", "message": message, "homework_id": homework_id})
            else:
                return jsonify({"status": "error", "message": message}), 500

        except Exception as e:
            return jsonify({"status": "error", "message": f"创建作业失败: {str(e)}"}), 500

    @bp.route('/publish_homework/<homework_id>', methods=['POST'])
    @teacher_required
    def publish_homework(homework_id):
        """发布作业"""
        try:
            homework_service = HomeworkService()
            success, message = homework_service.publish_homework(session.get('teacher_id'), homework_id)
            
            if success:
                return jsonify({"status": "success", "message": message})
            else:
                return jsonify({"status": "error", "message": message}), 500

        except Exception as e:
            return jsonify({"status": "error", "message": f"发布失败: {str(e)}"}), 500

    @bp.route('/delete_homework/<homework_id>', methods=['DELETE'])
    @teacher_required
    def delete_homework(homework_id):
        """删除作业"""
        try:
            homework_service = HomeworkService()
            success, message = homework_service.delete_homework(session.get('teacher_id'), homework_id)

            if success:
                return jsonify({"status": "success", "message": message})
            else:
                return jsonify({"status": "error", "message": message}), 500

        except Exception as e:
            return jsonify({"status": "error", "message": f"删除失败: {str(e)}"}), 500

    @bp.route('/get_course_homework/<course_schedule_id>')
    @teacher_required
    def get_course_homework(course_schedule_id):
        """获取指定课程的作业列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取该课程的所有作业
            cursor.execute("""
                SELECT id, title, description, created_at
                FROM homework
                WHERE course_schedule_id = ?
                ORDER BY created_at DESC
            """, (course_schedule_id,))

            homework_list = cursor.fetchall()
            conn.close()

            # 转换为字典格式
            homework_data = []
            for homework in homework_list:
                homework_data.append({
                    'id': homework['id'],
                    'title': homework['title'],
                    'description': homework['description'],
                    'created_at': homework['created_at']
                })

            return jsonify({"status": "success", "homework": homework_data})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取作业列表失败: {str(e)}"}), 500

    @bp.route('/get_resource_papers')
    @teacher_required
    def get_resource_papers():
        """获取资源库中的原始试卷列表（用于导入功能）"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取资源库中的试卷，用于导入到课程中
            cursor.execute("""
                SELECT id, title, description, created_at
                FROM papers
                WHERE teacher_id = ?
                ORDER BY created_at DESC
            """, (session.get('teacher_id'),))

            papers = cursor.fetchall()
            conn.close()

            # 转换为字典格式
            papers_list = []
            for paper in papers:
                papers_list.append({
                    'id': paper['id'],
                    'title': paper['title'],
                    'description': paper['description'],
                    'created_at': paper['created_at']
                })

            return jsonify({"status": "success", "papers": papers_list})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取资源库试卷失败: {str(e)}"}), 500

    @bp.route('/view_homework/<homework_id>')
    @teacher_required
    def view_homework(homework_id):
        """查看作业详情页面"""
        try:
            homework_service = HomeworkService()
            success, message, homework = homework_service.get_homework_details(
                session.get('teacher_id'), homework_id
            )

            if not success:
                 return jsonify({"status": "error", "message": message}), 404

            # 获取提交统计
            _, _, submissions = homework_service.get_homework_submissions(
                session.get('teacher_id'), homework_id
            )
            
            # 获取班级总人数
            conn = get_db()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(DISTINCT s.student_id) as total_students
                FROM students s
                JOIN course_schedules cs ON s.class_id = cs.class_id
                JOIN homework h ON cs.id = h.course_schedule_id
                WHERE h.id = ?
            """, (homework_id,))
            total_students_result = cursor.fetchone()
            total_students = total_students_result['total_students'] if total_students_result else 0
            conn.close()

            homework['submitted_count'] = len(submissions) if submissions else 0
            homework['total_students'] = total_students

            return render_template("teacher/view_homework.html", homework=homework, current_page="homework")

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取作业详情失败: {str(e)}"}), 500

    @bp.route('/homework_analysis/<homework_id>')
    @teacher_required
    def homework_analysis(homework_id):
        """作业分析页面"""
        try:
            homework_service = HomeworkService()
            success, message, analysis_data = homework_service.get_homework_analysis(
                session.get('teacher_id'), homework_id
            )

            if not success:
                # 渲染一个带有错误消息的空页面
                return render_template("teacher/homework_analysis.html", 
                                       error_message=message,
                                       current_page="homework")

            # 从分析数据中提取模板需要的部分
            homework = analysis_data.get("homework")
            current_course = {
                'id': homework.get('course_schedule_id'),
                'course_name': homework.get('course_name'),
                'class_name': homework.get('class_name')
            } if homework else None

            return render_template("teacher/homework_analysis.html",
                                  current_page="homework",
                                  homework=homework,
                                  current_course=current_course,
                                  results=analysis_data.get("results", []),
                                  question_stats=analysis_data.get("question_stats", []),
                                  score_distribution=analysis_data.get("score_distribution", []),
                                  stats=analysis_data.get("stats", {}))

        except Exception as e:
            return render_template("teacher/homework_analysis.html", 
                                   error_message=f"加载分析页面时出错: {str(e)}",
                                   current_page="homework")

    @bp.route('/create_homework')
    @teacher_required
    def create_homework():
        """新建作业页面"""
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return jsonify({'status': 'error', 'message': '未指定课程'})

        conn = get_db()
        cursor = conn.cursor()

        # 获取课程信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))

        current_class = cursor.fetchone()
        if not current_class:
            conn.close()
            return jsonify({'status': 'error', 'message': '课程不存在或无权限'})

        return render_template("teacher/add_homework.html", 
                            current_page="homework", 
                            current_class=current_class)

    @bp.route('/save_homework', methods=['POST'])
    @teacher_required
    def save_homework():
        """保存作业"""
        try:
            data = request.get_json()
            homework_service = HomeworkService()
            
            success, message, homework_id = homework_service.create_homework_manually(
                session.get('teacher_id'), data
            )

            if success:
                return jsonify({
                    'status': 'success',
                    'message': message,
                    'homework_id': homework_id
                })
            else:
                return jsonify({'status': 'error', 'message': message})

        except Exception as e:
            return jsonify({'status': 'error', 'message': str(e)})
