"""
签到管理相关路由
"""
from flask import request, jsonify, g
from app.models.database import get_db
from app.utils.decorators import api_login_required
from datetime import datetime
import uuid


def register_routes(bp):
    """注册签到管理相关路由"""
    
    @bp.route('/manual_checkin/<course_schedule_id>', methods=['POST'])
    @api_login_required
    def manual_checkin(course_schedule_id):
        """手动签到"""
        try:
            # 验证课程权限
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, g.teacher_id))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取表单数据
            student_id = request.form.get('student_id')
            remark = request.form.get('remark', '')

            if not student_id:
                conn.close()
                return jsonify({"status": "error", "message": "请选择学生"}), 400

            # 检查学生是否已签到
            cursor.execute("""
                SELECT id FROM class_attendance
                WHERE course_schedule_id = ? AND student_id = ? AND status = 'present'
            """, (course_schedule_id, student_id))

            if cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "该学生已经签到"}), 400

            # 更新或插入签到记录
            current_time = datetime.now().isoformat()

            # 先尝试更新现有记录
            cursor.execute("""
                UPDATE class_attendance
                SET status = 'present', signin_time = ?, remark = ?
                WHERE course_schedule_id = ? AND student_id = ?
            """, (current_time, remark, course_schedule_id, student_id))

            # 如果没有更新任何记录，则插入新记录
            if cursor.rowcount == 0:
                attendance_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO class_attendance (
                        id, course_schedule_id, student_id, signin_time, status, remark, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    attendance_id,
                    course_schedule_id,
                    student_id,
                    current_time,
                    'present',
                    remark,
                    current_time
                ))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "签到成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"签到失败: {str(e)}"}), 500

    @bp.route('/get_attendance_status/<course_schedule_id>', methods=['GET'])
    @api_login_required
    def get_attendance_status(course_schedule_id):
        """获取签到状态"""
        try:
            # 验证课程权限
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, g.teacher_id))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取学生签到情况
            cursor.execute("""
                SELECT s.student_id, s.name, s.gender,
                       CASE WHEN ca.status = 'present' THEN 1 ELSE 0 END as signed_in,
                       ca.signin_time, ca.status as attendance_status
                FROM students s
                LEFT JOIN class_attendance ca ON s.student_id = ca.student_id AND ca.course_schedule_id = ?
                WHERE s.class_id IN (SELECT class_id FROM course_schedules WHERE id = ?)
                ORDER BY s.name
            """, (course_schedule_id, course_schedule_id))

            students = cursor.fetchall()
            conn.close()

            # 转换为字典格式
            students_list = []
            for student in students:
                students_list.append({
                    "student_id": student[0],
                    "name": student[1],
                    "gender": student[2],
                    "signed_in": student[3],
                    "signin_time": student[4] or '',
                    "attendance_status": student[5] or 'absent'
                })

            return jsonify({"status": "success", "students": students_list})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取签到状态失败: {str(e)}"}), 500
