from flask import Blueprint, render_template, request, redirect, url_for, session, flash, jsonify, current_app
from app.models.database import get_db
from app.utils.helpers import verify_password
from datetime import datetime, timedelta
# 使用简单的token生成替代jose
import hashlib
import json
import base64
import time

class SimpleJWT:
    @staticmethod
    def encode(payload, secret, algorithm='HS256'):
        # 简化的JWT实现
        header = {'typ': 'JWT', 'alg': algorithm}

        # 确保secret是字符串类型
        if isinstance(secret, bytes):
            secret = secret.decode('utf-8')
        elif not isinstance(secret, str):
            secret = str(secret)

        # 转换datetime为timestamp
        if 'exp' in payload and hasattr(payload['exp'], 'timestamp'):
            payload['exp'] = int(payload['exp'].timestamp())
        if 'iat' in payload and hasattr(payload['iat'], 'timestamp'):
            payload['iat'] = int(payload['iat'].timestamp())

        header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
        payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')

        signature_input = f"{header_b64}.{payload_b64}"
        signature = hashlib.sha256((signature_input + secret).encode()).hexdigest()

        return f"{header_b64}.{payload_b64}.{signature}"

    @staticmethod
    def decode(token, secret, algorithms=['HS256']):
        try:
            # 确保secret是字符串类型
            if isinstance(secret, bytes):
                secret = secret.decode('utf-8')
            elif not isinstance(secret, str):
                secret = str(secret)

            parts = token.split('.')
            if len(parts) != 3:
                raise ValueError("Invalid token format")

            header_b64, payload_b64, signature = parts

            # 验证签名
            signature_input = f"{header_b64}.{payload_b64}"
            expected_signature = hashlib.sha256((signature_input + secret).encode()).hexdigest()

            if signature != expected_signature:
                raise ValueError("Invalid signature")

            # 解码payload
            payload_json = base64.urlsafe_b64decode(payload_b64 + '==').decode()
            payload = json.loads(payload_json)

            # 检查过期时间
            if 'exp' in payload and payload['exp'] < time.time():
                raise ValueError("Token expired")

            return payload
        except Exception as e:
            raise ValueError(f"Token decode error: {e}")

jwt = SimpleJWT()

auth_bp = Blueprint('auth', __name__)

def _generate_token(identity_id, user_type):
    """生成JWT Token"""
    now = time.time()  # 使用time.time()获取当前时间戳
    exp_time = now + (24 * 60 * 60)  # 24小时有效期

    payload = {
        'exp': int(exp_time),
        'iat': int(now),
        'sub': identity_id,
        'user_type': user_type
    }
    return jwt.encode(
        payload,
        current_app.config['SECRET_KEY'],
        algorithm='HS256'
    )

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """统一登录页面 (Web端)"""
    if request.method == 'POST':
        user_id = request.form.get('user_id')
        password = request.form.get('password')

        if not user_id:
            return render_template('login.html', error='用户ID不能为空')

        if not password:
            return render_template('login.html', error='密码不能为空')

        conn = get_db()
        cursor = conn.cursor()

        # 首先检查是否为学生
        cursor.execute("SELECT student_id, name, password FROM students WHERE student_id = ?", (user_id,))
        student = cursor.fetchone()

        if student:
            # 验证学生密码
            if verify_password(password, student['password']):
                conn.close()
                session.clear()
                # 设置学生会话
                session.permanent = True
                session['user_id'] = student['student_id'] # 统一设置user_id
                session['student_id'] = student['student_id']
                session['student_name'] = student['name']
                session['user_type'] = 'student'
                # 生成token用于Socket.IO认证
                session['token'] = _generate_token(student['student_id'], 'student')

                return redirect(url_for('student.dashboard'))

        # 检查是否为教师
        cursor.execute("SELECT teacher_id, name, password FROM teachers WHERE teacher_id = ?", (user_id,))
        teacher = cursor.fetchone()

        if teacher:
            # 验证教师密码
            if verify_password(password, teacher['password']):
                conn.close()
                session.clear()
                # 设置教师会话
                session.permanent = True
                session['user_id'] = teacher['teacher_id'] # 统一设置user_id
                session['teacher_id'] = teacher['teacher_id']
                session['teacher_name'] = teacher['name']
                session['user_type'] = 'teacher'
                # 生成token用于Socket.IO认证
                session['token'] = _generate_token(teacher['teacher_id'], 'teacher')

                return redirect(url_for('dashboard.index'))

        conn.close()
        return render_template('login.html', error='用户ID或密码错误')

    return render_template('login.html')

@auth_bp.route('/api/login', methods=['POST'])
def api_login():
    """API登录接口 (原生App使用)"""
    data = request.get_json()
    if not data:
        return jsonify({"status": "error", "message": "请求体不能为空"}), 400

    login_id = data.get('user_id')
    password = data.get('password')

    if not login_id or not password:
        return jsonify({"status": "error", "message": "用户ID和密码不能为空"}), 400

    conn = get_db()
    cursor = conn.cursor()

    # 检查学生
    cursor.execute("SELECT student_id, name, password FROM students WHERE student_id = ?", (login_id,))
    student = cursor.fetchone()
    if student and verify_password(password, student['password']):
        conn.close()
        token = _generate_token(student['student_id'], 'student')
        return jsonify({
            "status": "success",
            "message": "登录成功",
            "user_type": "student",
            "user_info": {"id": student['student_id'], "name": student['name']},
            "token": token
        })

    # 检查教师
    cursor.execute("SELECT teacher_id, name, password FROM teachers WHERE teacher_id = ?", (login_id,))
    teacher = cursor.fetchone()
    if teacher and verify_password(password, teacher['password']):
        conn.close()
        token = _generate_token(teacher['teacher_id'], 'teacher')
        return jsonify({
            "status": "success",
            "message": "登录成功",
            "user_type": "teacher",
            "user_info": {"id": teacher['teacher_id'], "name": teacher['name']},
            "token": token
        })

    conn.close()
    return jsonify({"status": "error", "message": "用户ID或密码错误"}), 401


@auth_bp.route('/logout')
def logout():
    """统一登出"""
    session.clear()
    return redirect(url_for('auth.login'))

