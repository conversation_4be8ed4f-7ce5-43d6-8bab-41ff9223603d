{% extends "student/base.html" %}

{% block title %}正在答题 - {{ exam_title }}{% endblock %}

{% block styles %}
<style>
    .exam-container {
        display: flex;
        gap: 20px;
    }
    .question-panel {
        flex: 1;
    }
    .side-panel {
        width: 280px;
        position: sticky;
        top: 20px;
    }
    .question-item {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    .question-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
    }
    .question-options .layui-form-radio,
    .question-options .layui-form-checkbox {
        margin-bottom: 10px;
    }
    #question-nav-container .layui-btn {
        margin: 3px;
    }
    .loading-spinner {
        text-align: center;
        padding: 50px;
    }
</style>
{% endblock %}

{% block content %}
<div class="layui-card">
    <div class="layui-card-header">
        <h3>{{ exam_title }}</h3>
        <p style="color: #999; font-size: 12px;">{{ exam_description }}</p>
    </div>
    <div class="layui-card-body">
        <div class="exam-container">
            <!-- 题目区域 -->
            <div class="question-panel">
                <form class="layui-form" id="exam-form">
                    <!-- 题目将由JS动态加载到这里 -->
                    <div id="questions-container">
                        <div class="loading-spinner">
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 40px;"></i>
                            <p>正在加载试卷...</p>
                        </div>
                    </div>
                </form>
            </div>
            <!-- 侧边栏 -->
            <div class="side-panel">
                <div class="layui-card">
                    <div class="layui-card-header">答题卡</div>
                    <div class="layui-card-body" id="question-nav-container">
                        <!-- 答题卡导航将由JS动态生成 -->
                    </div>
                </div>
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-body">
                        <button id="submit-exam-btn" class="layui-btn layui-btn-fluid layui-btn-normal" disabled>
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="display: none;"></i>
                            <span>提交答卷</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 将 exam_id 传递给 JavaScript -->
<script>
    window.EXAM_ID = "{{ exam_id }}";
</script>
{% endblock %}

{% block scripts %}
<!-- 引入新的JS文件 -->
<script src="{{ url_for('static', filename='js/exam_player.js') }}"></script>
{% endblock %}
