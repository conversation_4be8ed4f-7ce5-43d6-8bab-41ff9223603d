<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}学员端{% endblock %} - 智慧课堂系统</title>
    <link rel="stylesheet" href="/static/css/layui.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f5f7fa;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .top-navbar {
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05); 
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 60px;
        }

        .navbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            color: #333;
            text-decoration: none;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .navbar-brand i {
            margin-right: 10px;
            font-size: 1.5rem;
        }

        .navbar-nav {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            color: #555;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-link:hover {
            background: #f0f0f0;
            color: #111;
        }

        .nav-link.active {
            background: #e7f5ff;
            color: #1890ff;
            font-weight: 600;
        }

        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            background: transparent;
        }

        .user-avatar:hover {
            background: #f5f5f5;
            border-color: #ccc;
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 8px 0;
            min-width: 120px;
            z-index: 1001;
        }

        .user-dropdown a {
            display: block;
            padding: 8px 16px;
            color: #333;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .user-dropdown a:hover {
            background-color: #f5f5f5;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #52c41a;
        }

        .status-indicator.offline { background: #ff4d4f; }
        .status-indicator.connecting { background: #faad14; animation: pulse 1.5s infinite; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .content-wrapper {
            max-width: 1400px;
            margin: 130px auto 20px;
            padding: 0 20px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .navbar-content {
                padding: 0 15px;
            }

            .navbar-brand {
                font-size: 1rem;
            }

            .navbar-nav {
                gap: 5px;
            }

            .nav-link {
                padding: 6px 10px;
                font-size: 12px;
            }

            .nav-link span {
                display: none;
            }

            .content-wrapper {
                padding: 0 15px;
                margin-top: 120px;
            }
        }

        @media (max-width: 480px) {
            .top-navbar {
                height: 50px;
            }

            .status-bar {
                height: 40px;
                top: 50px;
            }

            .navbar-brand {
                font-size: 0.9rem;
            }

            .navbar-brand i {
                font-size: 1.2rem;
            }

            .nav-link {
                padding: 4px 8px;
            }

            .user-avatar {
                width: 30px;
                height: 30px;
                font-size: 12px;
            }

            .content-wrapper {
                margin-top: 100px;
                padding: 0 10px;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
        <div class="navbar-content">
            <a href="{{ url_for('student.dashboard') }}" class="navbar-brand">
                <i class="layui-icon layui-icon-home"></i>
                智慧课堂学员端
            </a>

            <div class="navbar-nav">
                <a href="{{ url_for('student.dashboard') }}" class="nav-link" id="nav-dashboard">
                    <i class="layui-icon layui-icon-app"></i>
                    <span>首页</span>
                </a>
                <a href="{{ url_for('student.view_courses') }}" class="nav-link" id="nav-courses">
                    <i class="layui-icon layui-icon-read"></i>
                    <span>课程</span>
                </a>
                <a href="{{ url_for('student.student_homework') }}" class="nav-link" id="nav-homework">
                    <i class="layui-icon layui-icon-edit"></i>
                    <span>作业</span>
                </a>

                <div class="user-menu">
                    <div class="user-avatar" onclick="toggleUserMenu()">
                        {{ session.get('student_name', '同学')[0] }}
                    </div>
                    <div class="user-dropdown" id="userDropdown" style="display: none;">
                        <a href="{{ url_for('auth.logout') }}" style="color: #ff4d4f;">
                            <i class="layui-icon layui-icon-logout"></i>
                            退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="content-wrapper">
        {% block content %}{% endblock %}
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/layui.js"></script>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qr-scanner@1.4.2/qr-scanner.umd.min.js"></script>
    <script>
        let socket = null;
        let currentGroupInfo = null;

        // 初始化
        $(document).ready(function() {
            initializeGlobalSocket();
        });

        // 全局 Socket.IO 连接
        function initializeGlobalSocket() {
            const socketUrl = `http://${window.location.hostname}:{{ config.get("SOCKET_PORT", 5000) }}`;
            socket = io(socketUrl, {
                auth: {
                    token: "{{ session.get('token') }}"
                },
                transports: ['websocket'] // 优先使用WebSocket
            });

            window.socket = socket; // 将socket暴露到全局作用域，以兼容旧代码

            socket.on('connect', function() {
                console.log('Socket.IO: Successfully connected to server.');
                updateConnectionStatus('connecting');
            });

            socket.on('authenticated', function(data) {
                if(data.success) {
                    console.log('Socket.IO: Authenticated successfully!');
                    updateConnectionStatus('online');
                } else {
                    console.error('Socket.IO: Authentication failed:', data.error);
                    updateConnectionStatus('offline');
                    if (layui && layui.layer) {
                        layui.layer.msg('实时通信服务认证失败，请刷新页面或重新登录。', {icon: 2});
                    }
                }
            });

            socket.on('disconnect', function() {
                console.warn('Socket.IO: Disconnected from server.');
                updateConnectionStatus('offline');
            });

            // 监听小组信息更新
            socket.on('group_info_updated', function(data) {
                updateGroupStatus(data);
            });
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            const indicator = $('#connection-status');
            const text = $('#connection-text');

            indicator.removeClass('online offline connecting').addClass(status);

            switch(status) {
                case 'online':
                    text.text('已连接');
                    break;
                case 'offline':
                    text.text('未连接');
                    break;
                case 'connecting':
                    text.text('连接中...');
                    break;
            }
        }

        // 更新小组状态
        function updateGroupStatus(groupInfo) {
            const groupStatus = $('#group-status');
            const groupName = $('#group-name');

            if (groupInfo && groupInfo.group_name) {
                currentGroupInfo = groupInfo;
                groupName.text(groupInfo.group_name);
                groupStatus.show();
            } else {
                currentGroupInfo = null;
                groupStatus.hide();
            }
        }

        // 高亮当前导航
        function highlightCurrentNav() {
            const currentPath = window.location.pathname;
            $('.nav-link').removeClass('active');

            if (currentPath.includes('/dashboard')) {
                $('#nav-dashboard').addClass('active');
            } else if (currentPath.includes('/courses')) {
                $('#nav-courses').addClass('active');
            } else if (currentPath.includes('/homework')) {
                $('#nav-homework').addClass('active');
                $('#nav-interaction').addClass('active'); // 互动也指向作业页面
            }
        }

        // 切换用户菜单
        function toggleUserMenu() {
            const dropdown = $('#userDropdown');
            dropdown.toggle();
        }

        // 点击外部关闭用户菜单
        $(document).click(function(e) {
            if (!$(e.target).closest('.user-menu').length) {
                $('#userDropdown').hide();
            }
        });

        // 全局Socket访问
        window.getGlobalSocket = function() {
            return globalSocket;
        };

        // 兼容性：为了向后兼容，保留socket变量
        window.socket = globalSocket;

        // 初始化Layui基础组件
        layui.use(['element'], function(){
            var element = layui.element;
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
