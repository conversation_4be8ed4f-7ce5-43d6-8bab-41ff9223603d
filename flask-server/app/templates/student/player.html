{% extends "student/base.html" %}

{% block title %}课程直播{% endblock %}

{% block styles %}
<style>
    .video-wrapper {
        position: relative;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        width: 100%;
        padding-top: 56.25%; /* 16:9 aspect ratio */
    }
    #videoContainer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    #liveVideo {
        width: 100%;
        height: 100%;
        background-color: #000;
    }
    /* 添加弹幕容器样式 */
    #danmakuArea {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 90%;  /* 避免遮挡视频控制条 */
        pointer-events: none;
        z-index: 10;
        overflow: hidden;
    }
    .placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f0f2f5;
        color: #999;
        text-align: center;
    }
    .placeholder i {
        font-size: 60px;
        margin-bottom: 20px;
    }
    .status-bar {
        padding: 15px;
        background-color: #fff;
        border-radius: 8px;
        margin-top: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #e6e6e6;
    }
    .status-indicator {
        font-size: 16px;
        font-weight: 500;
    }
    .status-dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
        vertical-align: middle;
    }
    .status-dot.offline {
        background-color: #f5222d;
    }
    .status-dot.online {
        background-color: #52c41a;
    }
    .status-dot.loading {
        background-color: #faad14;
    }

    /* 聊天区域样式 */
    .chat-card {
        margin-top: 20px;
    }
    .chat-panel {
        height: 400px;
        display: flex;
        flex-direction: column;
    }
    .chat-messages {
        flex-grow: 1;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        background-color: #f9f9f9;
        margin-bottom: 10px;
    }
    .chat-message {
        margin-bottom: 10px;
        line-height: 1.5;
    }
    .chat-message .user {
        font-weight: bold;
        color: #5FB878; /* 学生默认为绿色 */
        margin-right: 8px;
    }
    .chat-message .user.teacher {
        color: #1E9FFF; /* 老师为蓝色 */
    }
    .chat-message .user.system {
        color: #FF5722;
    }
    .chat-message .message {
        color: #333;
    }
    .chat-input {
        display: flex;
    }
    .chat-input input {
        flex-grow: 1;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="layui-fluid" style="padding: 0;">
    <div class="layui-row layui-col-space20">
        <div class="layui-col-md12">
            <div class="layui-card chat-card">
                 <div class="layui-card-header">
                    <i class="layui-icon layui-icon-dialogue"></i> 课堂互动
                </div>
                <div class="layui-card-body chat-panel">
                    <div class="chat-messages" id="chatMessages">
                        <!-- 消息将显示在这里 -->
                    </div>
                    <div class="chat-input">
                        <input type="text" id="chatInput" class="layui-input" placeholder="输入消息...">
                        <button id="sendChatBtn" class="layui-btn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }} {# 继承父模板的scripts块 #}
<script>
layui.use(['layer', 'element'], function() {
    var layer = layui.layer;
    var $ = layui.jquery;

    // --- Chat Logic ---
    function sendChatMessage() {
        const input = $('#chatInput');
        const message = input.val().trim();
        if (message) {
            if (typeof socket !== 'undefined' && socket && socket.connected) {
                socket.emit('send_message', { 'message': message });
                input.val('');
            } else {
                layer.msg('无法连接到互动服务器，请刷新页面。', {icon: 2});
            }
        }
    }

    function appendChatMessage(user, user_type, message) {
        const messagesContainer = $('#chatMessages');
        let userClass = 'user';
        if (user_type === 'teacher') {
            userClass += ' teacher';
        } else if (user_type === 'system') {
            userClass += ' system';
        }

        const messageEl = $('<div>').addClass('chat-message');
        messageEl.html(`<span class="${userClass}">${user}:</span> <span class="message">${message}</span>`);
        messagesContainer.append(messageEl);
        messagesContainer.scrollTop(messagesContainer.prop("scrollHeight"));
    }

    function initializeSocketHandlers() {
        // 统一监听 'chat_message' 事件
        socket.on('chat_message', function(data) {
            appendChatMessage(data.user, data.user_type, data.message);
        });

        function joinCourseRoom() {
            const courseId = "{{ course_id }}";
            if (courseId) {
                socket.emit('join_course', { 'course_id': courseId });
                console.log(`Socket.IO: Sent request to join course room ${courseId}`);
            } else {
                console.error('Socket.IO: course_id is not available for joining room.');
            }
        }

        // 当socket连接成功后，自动加入课程房间
        socket.on('connect', function() {
            // 短暂延时以确保认证流程优先完成
            setTimeout(joinCourseRoom, 100);
        });

        // 如果socket已经连接，则手动触发一次加入房间的逻辑
        if (socket.connected) {
            joinCourseRoom();
        }
    }

    // 加载历史聊天记录
    function loadChatHistory() {
        const courseId = "{{ course_id }}";
        if (!courseId) {
            console.error("无法获取课程ID，无法加载历史记录。");
            return;
        }

        $.ajax({
            url: `/api/course/${courseId}/chat_history`,
            type: 'GET',
            dataType: 'json',
            success: function(res) {
                if (res.status === 'success' && res.messages) {
                    res.messages.forEach(function(msg) {
                        appendChatMessage(msg.user, msg.user_type, msg.message);
                    });
                }
            },
            error: function(xhr) {
                console.error("加载聊天记录失败:", xhr.responseText);
            }
        });
    }

    // Bind events
    $('#sendChatBtn').on('click', sendChatMessage);
    $('#chatInput').on('keypress', function(e) {
        if (e.key === 'Enter') {
            sendChatMessage();
        }
    });

    // 页面加载时，首先加载历史记录
    loadChatHistory();

    // 使用轮询来安全地初始化Socket处理器
    var socketInitInterval = setInterval(function() {
        if (typeof socket !== 'undefined' && socket && socket.on) {
            clearInterval(socketInitInterval);
            initializeSocketHandlers();
        }
    }, 100);
});
</script>
{% endblock %}