{% extends "student/base.html" %}

{% block title %}正在答题 - {{ exam_title }}{% endblock %}

{% block styles %}
<style>
    .exam-container {
        display: flex;
        gap: 20px;
    }
    .question-panel {
        flex: 1;
    }
    .side-panel {
        width: 280px;
    }
    .question-item {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    .question-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
    }
    .question-options .layui-form-radio,
    .question-options .layui-form-checkbox {
        margin-bottom: 10px;
    }
    #question-nav-container .layui-btn {
        margin: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="layui-card">
    <div class="layui-card-header">
        <h3>{{ exam_title }}</h3>
        <p style="color: #999; font-size: 12px;">{{ exam_description }}</p>
    </div>
    <div class="layui-card-body">
        <div class="exam-container">
            <!-- 题目区域 -->
            <div class="question-panel">
                <form class="layui-form" id="exam-form">
                    {% for question in exam_data.questions %}
                    <div class="question-item" id="q-{{ loop.index }}">
                        <div class="question-title">
                            <span>{{ loop.index }}. {{ question.desc | safe }}</span>
                            <span class="layui-badge layui-bg-gray">{{ question.score }}分</span>
                        </div>
                        <div class="question-options">
                            {% if question.type == 'single' or question.type == 'judge' %}
                                {% for option in question.options %}
                                <input type="radio" name="q_{{ question.id }}" value="{{ loop.index0 | to_option }}" title="{{ loop.index | to_option }}. {{ option }}" lay-skin="primary">
                                {% endfor %}
                            {% elif question.type == 'multiple' %}
                                {% for option in question.options %}
                                <input type="checkbox" name="q_{{ question.id }}" value="{{ loop.index0 | to_option }}" title="{{ loop.index | to_option }}. {{ option }}" lay-skin="primary">
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </form>
            </div>
            <!-- 侧边栏 -->
            <div class="side-panel">
                <div class="layui-card">
                    <div class="layui-card-header">答题卡</div>
                    <div class="layui-card-body" id="question-nav-container">
                        {% for question in exam_data.questions %}
                        <a href="#q-{{ loop.index }}" class="layui-btn layui-btn-primary layui-btn-sm">{{ loop.index }}</a>
                        {% endfor %}
                    </div>
                </div>
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-body">
                        <button id="submit-exam-btn" class="layui-btn layui-btn-fluid layui-btn-normal">提交答卷</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;

    var questionIdMap = {};
    {% for q in exam_data.questions %}
    questionIdMap["{{ q.id }}"] = {{ loop.index }};
    {% endfor %}

    form.on('radio', function(data){
        updateNavStatus(data.elem.name, true);
    });
    form.on('checkbox', function(data){
        var name = data.elem.name;
        var hasChecked = $(`input[name="${name}"]:checked`).length > 0;
        updateNavStatus(name, hasChecked);
    });

    function updateNavStatus(questionName, isAnswered) {
        var questionId = questionName.split('_')[1];
        var index = questionIdMap[questionId];

        if(index) {
            var navBtn = $(`#question-nav-container a[href="#q-${index}"]`);
            if (isAnswered) {
                navBtn.removeClass('layui-btn-primary').addClass('layui-btn-normal');
            } else {
                navBtn.removeClass('layui-btn-normal').addClass('layui-btn-primary');
            }
        }
    }

    $('#submit-exam-btn').on('click', function(){
        layer.confirm('确定要提交答卷吗？', { icon: 3, title:'确认' }, function(index){
            layer.close(index);
            submitAnswers();
        });
    });

    function submitAnswers() {
        var formData = $('#exam-form').serializeArray();
        var answers = {};

        formData.forEach(function(item){
            var key = item.name;
            if (answers[key]) {
                if (!Array.isArray(answers[key])) {
                    answers[key] = [answers[key]];
                }
                answers[key].push(item.value);
            } else {
                answers[key] = item.value;
            }
        });

        var finalAnswers = {};
        for (const [key, value] of Object.entries(answers)) {
            const questionId = key.split('_')[1];
            finalAnswers[questionId] = Array.isArray(value) ? value.join(',') : value;
        }

        var postData = {
            homework_id: "{{ exam_id }}",
            answers: finalAnswers
        };

        var loading = layer.load(1);
        $.ajax({
            url: "{{ url_for('student.student_exam') }}",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(postData),
            success: function(res){
                layer.close(loading);
                if(res.status === 'success') {
                    layer.alert(`提交成功！得分：${res.score} / ${res.total}`, { icon: 1, title: '完成' }, function(i){
                        layer.close(i);
                        window.location.href = "{{ url_for('student.view_courses') }}";
                    });
                } else {
                    layer.alert(res.message || '提交失败', { icon: 2 });
                }
            },
            error: function(){
                layer.close(loading);
                layer.alert('请求失败，请检查网络', { icon: 2 });
            }
        });
    }
});
</script>
{% endblock %}
