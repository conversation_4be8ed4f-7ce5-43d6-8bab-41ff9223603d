<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协同白板</title>
    <style>
        html, body, iframe { margin: 0; padding: 0; height: 100%; width: 100%; border: none; overflow: hidden; }
        .group-controls { position: absolute; bottom: 5rem; right: 1rem; z-index: 10; background: #fff; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); display: flex; align-items: center; gap: 1rem; }
        .group-controls button { padding: 0.5rem 1rem; border-radius: 5px; border: 1px solid #ccc; cursor: pointer; }
        .group-controls button:hover { background: #f0f0f0; }
    </style>
</head>
<body>
    <iframe id="excalidraw-iframe" src="{{ url_for('student.excalidraw_proxy') }}"></iframe>
    <div class="group-controls">
        <div id="group-info">正在查找小组信息...</div>
    </div>

    <script>
        const iframe = document.getElementById('excalidraw-iframe');
        const groupInfoDiv = document.getElementById('group-info');
        
        let groupInfo = null;
        let isExcalidrawReady = false;
        let hasJoined = false;

        // 1. 尝试加入小组的中心函数
        function attemptToJoinGroup() {
            // 必须获取到小组信息、白板已准备就绪，且尚未加入
            if (groupInfo && isExcalidrawReady && !hasJoined) {
                hasJoined = true; // 防止重复加入

                // 通知iframe加入小组，传递小组ID作为房间号
                iframe.contentWindow.postMessage({
                    type: 'JOIN_GROUP',
                    payload: { group_id: groupInfo.group_id, group_name: groupInfo.group_name }
                }, '*');

                // 更新UI状态
                groupInfoDiv.innerText = `已连接: ${groupInfo.group_name}`;
                console.log(`已自动加入协作小组: ${groupInfo.group_name} (ID: ${groupInfo.group_id})`);
            }
        }

        // 2. 获取用户小组信息
        fetch('/student/api/my_group')
            .then(res => res.json())
            .then(data => {
                if (data.success && data.group) {
                    groupInfo = data.group;
                    groupInfoDiv.innerText = `小组: ${data.group.group_name}`;
                    attemptToJoinGroup(); // 尝试加入
                } else {
                    groupInfoDiv.innerText = '未分配小组，无法协作';
                }
            })
            .catch(error => {
                console.error('获取小组信息失败:', error);
                groupInfoDiv.innerText = '获取小组信息失败';
            });

        // 3. 监听来自iframe的消息
        window.addEventListener('message', (event) => {
            // 安全检查，确保消息来自我们的iframe
            if (event.source !== iframe.contentWindow) return;

            const { type, payload } = event.data;

            switch (type) {
                case 'EXCALIDRAW_READY':
                    console.log('Excalidraw 已准备就绪');
                    isExcalidrawReady = true;
                    attemptToJoinGroup(); // 尝试加入
                    break;

                case 'EXCALIDRAW_CHANGE':
                    // 白板内容已更改，可以根据需要进行处理（例如自动保存快照）
                    // console.log('Excalidraw 内容已更改');
                    break;

                default:
                    // console.log('收到未知消息类型:', type);
            }
        });

        // 4. 错误处理
        iframe.onload = () => {
            console.log('Excalidraw iframe 已加载');
            groupInfoDiv.innerText = '正在等待白板初始化...';
        };

        iframe.onerror = () => {
            console.error('Excalidraw iframe 加载失败');
            groupInfoDiv.innerText = '白板加载失败，请刷新页面';
        };

        window.addEventListener('error', (event) => {
            console.error('页面错误:', event.error);
        });
    </script>
</body>
</html>