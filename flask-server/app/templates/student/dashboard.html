{% extends "student/base.html" %}

{% block title %}学员控制台{% endblock %}

{% block extra_css %}
<style>
/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}
.status-indicator.online { background-color: #52c41a; }
.status-indicator.offline { background-color: #ff4d4f; }
.status-indicator.connecting { background-color: #faad14; animation: pulse 1.5s infinite; }

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 卡片通用样式 */
.layui-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 1px solid #e8e8e8;
}

.layui-card-header {
    font-weight: 600;
}

/* 快捷功能卡片 */
.quick-action-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 1px solid #e8e8e8;
}

.quick-action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    border-color: #1890ff;
}

.action-icon {
    width: 5px;
    height: 5px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    background: #e6f7ff;
}

.action-icon i {
    font-size: 28px;
    color: #1890ff;
}

.action-content h4 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
}

.action-content p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.action-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #ff4d4f;
    color: white;
    border-radius: 10px;
    padding: 2px 8px;
    font-size: 12px;
    font-weight: 500;
}

.action-status {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #f0f0f0;
    color: #666;
    border-radius: 10px;
    padding: 2px 8px;
    font-size: 12px;
}

/* 协作工具网格 */
.collaboration-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.collab-tool-card {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.collab-tool-card:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24,144,255,0.1);
    transform: translateY(-2px);
}

.tool-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.tool-icon {
    width: 5px;
    height: 5px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.tool-icon i {
    font-size: 24px;
    color: inherit;
}

.tool-info h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.tool-info p {
    margin: 0;
    color: #666;
    font-size: 14px;
}


/* 移动端适配 */
@media (max-width: 768px) {
    .quick-action-card {
        padding: 15px;
    }

    .action-icon {
        width: 50px;
        height: 50px;
    }

    .action-icon i {
        font-size: 24px;
    }

    .action-content h4 {
        font-size: 16px;
    }

    .collaboration-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .collab-tool-card {
        padding: 15px;
    }

    .tool-icon {
        width: 40px;
        height: 40px;
    }

    .tool-icon i {
        font-size: 20px;
    }

    .tool-info h4 {
        font-size: 14px;
    }

    .tool-info p {
        font-size: 12px;
    }
}

@media (max-width: 480px) {

    .quick-action-card {
        padding: 12px;
    }

    .action-content h4 {
        font-size: 14px;
    }

    .action-content p {
        font-size: 12px;
    }

    .collaboration-grid {
        gap: 10px;
    }

    .collab-tool-card {
        padding: 12px;
    }

    .tool-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .tool-icon {
        margin-right: 0;
    }
}
</style>
{% endblock %}

{% block content %}

<!-- 小组信息显示区域 -->
<div id="group-info-container" style="margin-top: 20px;">
    <!-- 小组信息将通过JavaScript动态插入到这里 -->
</div>

<!-- 快捷功能区域 -->
<div class="quick-actions-section" style="margin-top: 20px;">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3><i class="layui-icon layui-icon-app"></i> 快捷功能</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space20">
                <div class="layui-col-md4">
                </div>
                <div class="layui-col-md4">
                    <div class="quick-action-card" id="check-in-btn">
                        <div class="action-icon">
                            <i class="layui-icon layui-icon-vercode"></i>
                        </div>
                        <div class="action-content">
                            <h4>课堂签到</h4>
                            <span class="action-status" id="checkin-status">待签到</span>
                            <div style="margin-top: 10px;">
                                <button class="layui-btn layui-btn-xs layui-btn-normal" id="auto-checkin-btn">自动签到</button>
                                <button class="layui-btn layui-btn-xs layui-btn-primary" id="qr-checkin-btn">扫码签到</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 小组研讨功能模块 -->
<div class="collaboration-section" style="margin-top: 20px;">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3><i class="layui-icon layui-icon-group"></i> 小组协作工具</h3>
        </div>
        <div class="layui-card-body">
            <div class="collaboration-grid">
                <div class="collab-tool-card" onclick="location.href='{{ url_for('student.screen_share') }}'">
                    <div class="tool-header">
                        <div class="tool-icon screen-share">
                            <i class="layui-icon layui-icon-screen-full"></i>
                        </div>
                        <div class="tool-info">
                            <h4>投屏分享</h4>
                        </div>
                    </div>
                </div>

                <div class="collab-tool-card" onclick="location.href='{{ url_for('student.collaborative_whiteboard') }}'">
                    <div class="tool-header">
                        <div class="tool-icon whiteboard">
                            <i class="layui-icon layui-icon-edit"></i>
                        </div>
                        <div class="tool-info">
                            <h4>协同白板</h4>
                        </div>
                    </div>
                </div>

                <div class="collab-tool-card" onclick="location.href='{{ url_for('student.share_content') }}'">
                    <div class="tool-header">
                        <div class="tool-icon content-share">
                            <i class="layui-icon layui-icon-upload"></i>
                        </div>
                        <div class="tool-info">
                            <h4>内容分享</h4>
                        </div>
                    </div>
                </div>

                <div class="collab-tool-card" onclick="location.href='{{ url_for('student.student_homework') }}'">
                    <div class="tool-header">
                        <div class="tool-icon interaction">
                            <i class="layui-icon layui-icon-dialogue"></i>
                        </div>
                        <div class="tool-info">
                            <h4>课堂互动</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
{{ super() }} {# 继承父模板的scripts块 #}
<script>
layui.use(['layer', 'jquery'], function(){
    var layer = layui.layer;
    var $ = layui.jquery;

    // 初始化页面
    $(document).ready(function() {
        initializePage();
        updateCurrentTime();
        setInterval(updateCurrentTime, 1000);
    });

    // 页面初始化
    function initializePage() {
        updateConnectionStatus('connecting');
        checkMyGroup();
        updateBadges();
    }

    // 更新当前时间
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        $('#current-time').text(timeString);
    }

    // 更新连接状态
    function updateConnectionStatus(status) {
        const statusDot = $('#status-dot');
        const statusText = $('#status-text');
        const groupStatusIndicator = $('#group-status-indicator');
        const groupStatusText = $('#group-status-text');

        statusDot.removeClass('online offline connecting').addClass(status);
        groupStatusIndicator.removeClass('online offline connecting').addClass(status);

        switch(status) {
            case 'online':
                statusText.text('已连接');
                groupStatusText.text('已连接');
                break;
            case 'offline':
                statusText.text('未连接');
                groupStatusText.text('未连接');
                break;
            case 'connecting':
                statusText.text('连接中...');
                groupStatusText.text('连接中...');
                break;
        }
    }

    let peerConnection;
    let localStream;
    const config = {
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
    };

    // 检查socket连接
    function checkSocket() {
        if (typeof socket === 'undefined' || !socket.connected) {
            layer.msg('无法连接到互动服务器，请刷新页面。', {icon: 2});
            return false;
        }
        return true;
    }

    // 检查当前学生的分组信息
    function checkMyGroup() {
        $.ajax({
            url: '/student/api/my_group',
            type: 'GET',
            dataType: 'json',
            success: function(res) {
                if (res.success && res.group) {
                    // 学生已被分组，显示分组信息
                    showGroupInfo(res.group);

                    // 自动设置小组ID到输入框
                    if (res.group.group_ip) {
                        const socketGroupId = `group_${res.group.group_name}_${res.group.group_ip.replace(/\./g, '_')}`;
                        $('#group-id-input').val(socketGroupId);
                    }
                } else {
                    // 学生未被分组，显示提示
                    showNoGroupInfo();
                }
            },
            error: function() {
                console.log('获取分组信息失败');
            }
        });
    }

    // 显示分组信息
    function showGroupInfo(group) {
        const groupInfoHtml = `
            <div class="layui-card" style="border: 2px solid #52c41a; border-radius: 12px; overflow: hidden;">
                <div class="layui-card-header" style="background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%); color: white; padding: 20px;">
                    <div class="layui-row">
                        <div class="layui-col-md8">
                            <h3 style="margin: 0; font-size: 20px;">
                                <i class="layui-icon layui-icon-group" style="margin-right: 10px;"></i>
                                ${group.group_name}
                            </h3>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">${group.course_name}</p>
                        </div>
                        <div class="layui-col-md4" style="text-align: right;">
                            <div style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 12px; display: inline-block;">
                                <span class="status-indicator online"></span>
                                <span>已连接</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-card-body" style="padding: 25px;">
                    <div class="layui-row layui-col-space20">
                        <div class="layui-col-md6">
                            <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 8px; padding: 15px;">
                                <h4 style="margin: 0 0 10px 0; color: #52c41a;">
                                    <i class="layui-icon layui-icon-location"></i> 连接信息
                                </h4>
                                ${group.group_ip ? `<p style="margin: 5px 0;"><strong>小组IP：</strong><code style="background: #fff; padding: 2px 6px; border-radius: 4px; color: #1890ff;">${group.group_ip}</code></p>` : ''}
                                <p style="margin: 5px 0;"><strong>状态：</strong><span style="color: #52c41a;">● 在线</span></p>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div style="background: #f0f5ff; border: 1px solid #adc6ff; border-radius: 8px; padding: 15px;">
                                <h4 style="margin: 0 0 10px 0; color: #1890ff;">
                                    <i class="layui-icon layui-icon-username"></i> 小组成员
                                </h4>
                                ${group.members && group.members.length > 0 ?
                                    `<div>${group.members.map(m => `<span style="display: inline-block; background: #fff; border: 1px solid #d9d9d9; border-radius: 16px; padding: 4px 12px; margin: 2px; font-size: 12px;">${m.name}</span>`).join('')}</div>` :
                                    '<p style="color: #8c8c8c; margin: 0;">暂无其他成员</p>'
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 显示在专门的容器中
        $('#group-info-container').html(groupInfoHtml);
        updateConnectionStatus('online');
    }

    // 显示未分组信息
    function showNoGroupInfo() {
        const noGroupHtml = `
            <div class="layui-card" style="border: 2px solid #faad14; border-radius: 12px; overflow: hidden;">
                <div class="layui-card-header" style="background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%); color: white; padding: 20px;">
                    <h3 style="margin: 0; font-size: 18px;">
                        <i class="layui-icon layui-icon-tips" style="margin-right: 10px;"></i>
                        等待分组中
                    </h3>
                </div>
                <div class="layui-card-body" style="padding: 30px; text-align: center;">
                    <div style="margin-bottom: 20px;">
                        <i class="layui-icon layui-icon-group" style="font-size: 64px; color: #faad14; opacity: 0.6;"></i>
                    </div>
                    <h4 style="color: #595959; margin-bottom: 10px;">您还未被分配到小组</h4>
                    <p style="color: #8c8c8c; margin-bottom: 20px;">
                        请联系老师获取小组信息，或等待老师进行分组分配
                    </p>
                    <div style="background: #fffbe6; border: 1px solid #ffe58f; border-radius: 8px; padding: 15px; margin-top: 20px;">
                        <p style="margin: 0; color: #d48806; font-size: 14px;">
                            <i class="layui-icon layui-icon-notice" style="margin-right: 5px;"></i>
                            分组完成后，您将能够使用协同白板、投屏分享等小组功能
                        </p>
                    </div>
                </div>
            </div>
        `;

        $('#group-info-container').html(noGroupHtml);
        updateConnectionStatus('offline');
    }

    // 连接到我的小组
    window.connectToMyGroup = function() {
        const groupId = $('#group-id-input').val();
        if (!groupId) {
            layer.msg('未找到小组ID', {icon: 2});
            return;
        }

        if (!checkSocket()) return;

        // 发送加入小组请求
        socket.emit('student_join_group', {
            'student_id': '{{ session.get("student_id") }}',
            'student_name': '{{ session.get("student_name") }}',
            'group_id': groupId
        });

        layer.msg('正在连接到小组...', {icon: 16});
    };

    // 页面加载完成后检查分组信息
    $(document).ready(function() {
        checkMyGroup();
    });

    // 自动签到逻辑
    $('#auto-checkin-btn').on('click', function(e){
        e.preventDefault();
        var loading = layer.load(1, {shade: [0.3, '#000']});
        $.ajax({
            url: "{{ url_for('student.sign_in_ajax') }}",
            type: 'POST',
            dataType: 'json',
            success: function(res){
                layer.close(loading);
                if(res.code === 200){
                    layer.msg(res.message, {icon: 1});
                    updateBadges(); // 重新加载统计数据以更新签到状态
                } else {
                    layer.alert(res.message, {icon: 2});
                }
            },
            error: function(){
                layer.close(loading);
                layer.alert('请求失败，请检查网络或稍后重试。', {icon: 2});
            }
        });
    });

    // 扫码签到逻辑
    $('#qr-checkin-btn').on('click', function(e){
        e.preventDefault();

        // 检查是否支持摄像头
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            layer.alert('您的浏览器不支持摄像头功能，请使用现代浏览器。', {icon: 2});
            return;
        }

        // 打开扫码窗口
        layer.open({
            type: 1,
            title: '扫码签到',
            area: ['500px', '600px'],
            content: '<div id="qr-scanner-container" style="text-align: center; padding: 20px;">' +
                    '<div style="margin-bottom: 20px;">请将教师端的签到二维码对准摄像头</div>' +
                    '<video id="qr-video" width="400" height="300" style="border: 1px solid #ccc;"></video>' +
                    '<div id="qr-result" style="margin-top: 20px; color: #666;">正在启动摄像头...</div>' +
                    '<div style="margin-top: 20px;">' +
                    '<button class="layui-btn layui-btn-sm" id="stop-scan-btn">停止扫描</button>' +
                    '</div>' +
                    '</div>',
            success: function(layero, index){
                startQRScanner(index);
            }
        });
    });

    // 启动二维码扫描
    function startQRScanner(layerIndex) {
        const video = document.getElementById('qr-video');
        const resultDiv = document.getElementById('qr-result');
        let stream = null;
        let scanning = true;

        // 停止扫描按钮事件
        $('#stop-scan-btn').on('click', function(){
            scanning = false;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            layer.close(layerIndex);
        });

        // 启动摄像头
        navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } })
            .then(function(mediaStream) {
                stream = mediaStream;
                video.srcObject = stream;
                video.play();
                resultDiv.textContent = '请将二维码对准摄像头';

                // 开始扫描
                scanQRCode();
            })
            .catch(function(err) {
                resultDiv.textContent = '无法访问摄像头: ' + err.message;
                console.error('摄像头访问失败:', err);
            });

        function scanQRCode() {
            if (!scanning) return;

            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            if (video.readyState === video.HAVE_ENOUGH_DATA) {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

                // 使用jsQR库解析二维码
                if (typeof QrScanner !== 'undefined') {
                    QrScanner.scanImage(canvas)
                        .then(result => {
                            if (result) {
                                handleQRResult(result, layerIndex, stream);
                                return;
                            }
                        })
                        .catch(err => {
                            // 继续扫描
                        });
                }
            }

            if (scanning) {
                requestAnimationFrame(scanQRCode);
            }
        }
    }

    // 处理二维码扫描结果
    function handleQRResult(qrData, layerIndex, stream) {
        try {
            const data = JSON.parse(qrData);

            if (data.type === 'attendance' && data.course_schedule_id) {
                // 停止摄像头
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }
                layer.close(layerIndex);

                // 发送签到请求
                const loading = layer.load(1, {shade: [0.3, '#000']});
                $.ajax({
                    url: "{{ url_for('student.qr_sign_in') }}",
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        course_schedule_id: data.course_schedule_id
                    },
                    success: function(res){
                        layer.close(loading);
                        if(res.code === 200){
                            layer.msg('扫码签到成功！', {icon: 1});
                            updateBadges();
                        } else {
                            layer.alert(res.message, {icon: 2});
                        }
                    },
                    error: function(){
                        layer.close(loading);
                        layer.alert('签到请求失败，请稍后重试。', {icon: 2});
                    }
                });
            } else {
                layer.alert('这不是有效的签到二维码', {icon: 2});
            }
        } catch (e) {
            layer.alert('二维码格式错误', {icon: 2});
        }
    }
});
</script>
{% endblock %}

