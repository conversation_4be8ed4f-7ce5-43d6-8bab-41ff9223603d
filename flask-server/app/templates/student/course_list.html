{% extends "student/base.html" %}

{% block title %}我的课程{% endblock %}

{% block content %}
<div class="layui-card">
    <div class="layui-card-header"><h3>我的课程</h3></div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space15">
            {% if courses %}
                {% for course in courses %}
                <div class="layui-col-md6 layui-col-lg4">
                    <div class="layui-card">
                        <div class="layui-card-header">{{ course.course_name }}</div>
                        <div class="layui-card-body">
                            <p><strong>教师:</strong> {{ course.teacher_name }}</p>
                            <p><strong>教室:</strong> {{ course.classroom_name }}</p>
                            <p><strong>时间:</strong> {{ course.day_of_week }} {{ course.start_time }}-{{ course.end_time }}</p>
                            <p><strong>状态:</strong> {{ course.status }}</p>
                            <a href="{{ url_for('student.course_detail', course_id=course.id) }}" class="layui-btn layui-btn-fluid">进入课堂</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <p>您当前没有课程安排。</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
