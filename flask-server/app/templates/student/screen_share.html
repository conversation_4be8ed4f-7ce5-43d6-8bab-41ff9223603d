<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投屏到小组</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/student.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            min-height: 100vh;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .screen-share-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .content-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.8);
        }

        .group-selection {
            background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
            border: 1px solid #b7eb8f;
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .share-options {
            background: #fff;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        }
        
        .share-type-btn {
            width: 100%;
            height: 120px;
            margin: 15px 0;
            border: 2px solid #e8f4fd;
            background: linear-gradient(135deg, #fff 0%, #f8fbff 100%);
            border-radius: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .share-type-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .share-type-btn:hover::before {
            left: 100%;
        }

        .share-type-btn:hover {
            border-color: #1890ff;
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(24,144,255,0.2);
        }

        .share-type-btn.active {
            border-color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
            box-shadow: 0 8px 25px rgba(82,196,26,0.3);
            transform: translateY(-3px);
        }

        .share-type-btn i {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #1890ff;
            transition: all 0.3s ease;
        }

        .share-type-btn.active i {
            color: #52c41a;
            transform: scale(1.1);
        }

        .share-type-btn span {
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        
        .preview-area {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 20px;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
            border: 2px solid #333;
        }

        .preview-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(24,144,255,0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        #previewPlaceholder {
            text-align: center;
            z-index: 1;
        }

        #previewPlaceholder i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.6;
        }

        #previewPlaceholder p {
            font-size: 1.2rem;
            opacity: 0.8;
            margin: 0;
        }

        #localVideo {
            width: 100%;
            height: auto;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .control-buttons {
            text-align: center;
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .btn-share {
            background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(82,196,26,0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-share:disabled {
            background: #d9d9d9;
            color: #999;
            box-shadow: none;
            cursor: not-allowed;
        }

        .btn-share:not(:disabled):hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(82,196,26,0.4);
        }

        .btn-stop {
            background: #ff4d4f;
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255,77,79,0.3);
        }

        .btn-stop:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255,77,79,0.4);
        }

        .btn-secondary {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .screen-share-container {
                padding: 20px 15px;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .page-header p {
                font-size: 1rem;
            }

            .content-card {
                padding: 20px;
                margin-bottom: 20px;
            }

            .share-type-btn {
                height: 100px;
                margin: 10px 0;
            }

            .share-type-btn i {
                font-size: 2rem;
                margin-bottom: 8px;
            }

            .share-type-btn span {
                font-size: 0.9rem;
            }

            .share-type-btn small {
                font-size: 0.7rem;
            }

            .preview-area {
                min-height: 300px;
                margin: 20px 0;
            }

            #previewPlaceholder i {
                font-size: 3rem;
                margin-bottom: 15px;
            }

            #previewPlaceholder p {
                font-size: 1rem;
            }

            .control-buttons {
                flex-direction: column;
                gap: 15px;
                margin-top: 20px;
            }

            .btn-share, .btn-stop {
                padding: 12px 30px;
                font-size: 14px;
            }

            .btn-secondary {
                padding: 10px 25px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .screen-share-container {
                padding: 15px 10px;
            }

            .page-header h1 {
                font-size: 1.8rem;
            }

            .page-header p {
                font-size: 0.9rem;
            }

            .content-card {
                padding: 15px;
                border-radius: 15px;
            }

            .share-type-btn {
                height: 80px;
                margin: 8px 0;
                border-radius: 12px;
            }

            .share-type-btn i {
                font-size: 1.8rem;
                margin-bottom: 6px;
            }

            .share-type-btn span {
                font-size: 0.8rem;
            }

            .share-type-btn small {
                font-size: 0.6rem;
                margin-top: 3px;
            }

            .preview-area {
                min-height: 250px;
                margin: 15px 0;
                border-radius: 15px;
            }

            #previewPlaceholder i {
                font-size: 2.5rem;
                margin-bottom: 12px;
            }

            #previewPlaceholder p {
                font-size: 0.9rem;
            }

            .control-buttons {
                gap: 12px;
                margin-top: 15px;
            }

            .btn-share, .btn-stop {
                padding: 10px 25px;
                font-size: 13px;
                border-radius: 25px;
            }

            .btn-secondary {
                padding: 8px 20px;
                font-size: 12px;
                border-radius: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="screen-share-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>
                <i class="fas fa-desktop"></i> 投屏分享
            </h1>
        </div>
            
        <!-- 小组信息卡片 -->
        <div class="content-card">
            <div class="group-selection">
                <h4 style="color: #52c41a; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-users"></i> 我的小组
                </h4>
                <div id="groupInfo">
                    <div id="groupInfoCard" style="display: none;">
                        <!-- 小组信息将通过JavaScript动态显示 -->
                    </div>
                    <div id="loadingInfo" style="text-align: center; padding: 20px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #1890ff; margin-bottom: 15px;"></i>
                        <p style="color: #666; margin: 0;">正在获取小组信息...</p>
                    </div>
                    <div id="noGroupInfo" style="display: none;">
                        <div style="background: #fff2e8; border: 1px solid #ffbb96; border-radius: 12px; padding: 20px; text-align: center;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #fa8c16; margin-bottom: 15px;"></i>
                            <h5 style="color: #d46b08; margin-bottom: 10px;">未分配小组</h5>
                            <p style="color: #8c8c8c; margin: 0;">您还未被分配到小组，请联系老师获取小组信息。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 投屏选项卡片 -->
        <div class="content-card">
            <div class="share-options">
                <h4 style="color: #1890ff; margin-bottom: 25px; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-share-alt"></i> 选择投屏类型
                </h4>
                <div class="row">
                    <div class="col-md-4">
                        <button class="share-type-btn" data-type="screen">
                            <i class="fas fa-desktop"></i>
                            <span>屏幕分享</span>
                            <small style="color: #8c8c8c; font-size: 0.8rem; margin-top: 5px;">分享整个屏幕内容</small>
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="share-type-btn" data-type="camera">
                            <i class="fas fa-video"></i>
                            <span>摄像头</span>
                            <small style="color: #8c8c8c; font-size: 0.8rem; margin-top: 5px;">分享摄像头画面</small>
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="share-type-btn" data-type="file">
                            <i class="fas fa-file"></i>
                            <span>文件展示</span>
                            <small style="color: #8c8c8c; font-size: 0.8rem; margin-top: 5px;">分享文档或图片</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预览区域卡片 -->
        <div class="content-card">
            <h4 style="color: #722ed1; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-eye"></i> 预览窗口
            </h4>
            <div class="preview-area" id="previewArea">
                <div id="previewPlaceholder">
                    <i class="fas fa-eye"></i>
                    <p>选择投屏类型开始预览</p>
                </div>
                <video id="localVideo" style="display: none;" autoplay muted></video>
            </div>

            <!-- 控制按钮 -->
            <div class="control-buttons">
                <button id="startShareBtn" class="btn btn-share" disabled>
                    <i class="fas fa-play" style="margin-right: 8px;"></i>
                    <span>开始投屏</span>
                </button>
                <button id="stopShareBtn" class="btn btn-stop" style="display: none;">
                    <i class="fas fa-stop" style="margin-right: 8px;"></i>
                    <span>停止投屏</span>
                </button>
                <button class="btn btn-secondary" onclick="history.back()">
                    <i class="fas fa-arrow-left" style="margin-right: 8px;"></i>
                    <span>返回首页</span>
                </button>
            </div>
        </div>
            
            <!-- 状态显示 -->
            <div id="statusArea" class="mt-4" style="display: none;">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <span id="statusText">准备中...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    
    <script>
        // 全局变量
        let socket = null;
        let selectedGroup = null;
        let selectedShareType = null;
        let localStream = null;
        let isSharing = false;
        let rtspPublisher = null;
        
        // 学生信息
        const studentInfo = {
            id: '{{ session.student_id }}',
            name: '{{ session.student_name }}',
            token: '{{ session.token }}'
        };
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            loadMyGroup();
            setupEventListeners();
            updateStartButton(); // 初始化按钮状态
        });
        
        // 初始化Socket连接
        function initializeSocket() {
            socket = io();
            
            // 认证
            socket.emit('authenticate', { token: studentInfo.token });
            
            socket.on('authenticated', function(data) {
                if (data.success) {
                    console.log('Socket认证成功');
                } else {
                    console.error('Socket认证失败:', data.error);
                }
            });
            
            // 监听投屏响应
            socket.on('screen_share_response', function(data) {
                if (data.success) {
                    console.log('投屏请求成功:', data);
                    startRTSPPublish(data.rtsp_url, data.stream_name);
                } else {
                    console.error('投屏请求失败:', data.error);
                    showStatus('投屏失败: ' + data.error, 'danger');
                }
            });
            
            // 监听小组加入响应
            socket.on('join_group_response', function(data) {
                if (data.success) {
                    console.log('成功加入小组:', data.group_id);
                } else {
                    console.error('加入小组失败:', data.error);
                    showStatus('加入小组失败: ' + data.error, 'danger');
                }
            });
        }
        
        // 加载我的小组信息
        function loadMyGroup() {
            fetch('/student/api/my_group')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loadingInfo').style.display = 'none';

                    if (data.success && data.group) {
                        // 学生已被分组
                        selectedGroup = {
                            id: `group_${data.group.group_name}_${data.group.group_ip.replace(/\./g, '_')}`,
                            name: data.group.group_name,
                            ip: data.group.group_ip,
                            course_name: data.group.course_name
                        };

                        // 显示小组信息
                        renderGroupInfo(data.group);

                        // 自动加入小组
                        autoJoinGroup();

                        updateStartButton();
                    } else {
                        // 学生未被分组
                        document.getElementById('noGroupInfo').style.display = 'block';
                        showStatus('您还未被分配到小组', 'warning');
                    }
                })
                .catch(error => {
                    console.error('获取小组信息失败:', error);
                    document.getElementById('loadingInfo').style.display = 'none';
                    document.getElementById('noGroupInfo').style.display = 'block';
                    showStatus('获取小组信息失败', 'danger');
                });
        }

        // 渲染小组信息
        function renderGroupInfo(group) {
            const groupInfoCard = document.getElementById('groupInfoCard');
            groupInfoCard.innerHTML = `
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i> ${group.group_name}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">
                            <strong>课程：</strong>${group.course_name}<br>
                            <strong>小组IP：</strong><span class="text-primary font-monospace">${group.group_ip}</span><br>
                            <strong>成员：</strong>${group.members && group.members.length > 0 ?
                                group.members.map(m => m.name).join('、') : '暂无其他成员'}
                        </p>
                        <div class="text-success">
                            <i class="fas fa-check-circle"></i> 已连接
                        </div>
                    </div>
                </div>
            `;
            groupInfoCard.style.display = 'block';
        }

        // 自动加入小组
        function autoJoinGroup() {
            if (selectedGroup && socket) {
                socket.emit('student_join_group', {
                    student_id: studentInfo.id,
                    student_name: studentInfo.name,
                    group_id: selectedGroup.id
                });
                showStatus('已连接到小组，可以开始投屏', 'success');
            }
        }

        
        // 设置事件监听器
        function setupEventListeners() {
            // 投屏类型选择
            document.querySelectorAll('.share-type-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.share-type-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    selectedShareType = this.dataset.type;
                    
                    // 开始预览
                    startPreview(selectedShareType);
                    updateStartButton();
                });
            });
            
            // 开始投屏按钮
            document.getElementById('startShareBtn').addEventListener('click', startScreenShare);
            
            // 停止投屏按钮
            document.getElementById('stopShareBtn').addEventListener('click', stopScreenShare);
        }
        
        // 开始预览
        async function startPreview(type) {
            const video = document.getElementById('localVideo');
            const placeholder = document.getElementById('previewPlaceholder');

            try {
                if (localStream) {
                    localStream.getTracks().forEach(track => track.stop());
                }

                // 检查浏览器支持
                if (!navigator.mediaDevices) {
                    throw new Error('您的浏览器不支持媒体设备访问。请使用现代浏览器或HTTPS连接。');
                }

                if (type === 'screen') {
                    if (!navigator.mediaDevices.getDisplayMedia) {
                        throw new Error('您的浏览器不支持屏幕分享功能。');
                    }
                    localStream = await navigator.mediaDevices.getDisplayMedia({
                        video: {
                            mediaSource: 'screen',
                            width: { ideal: 1920 },
                            height: { ideal: 1080 }
                        },
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true
                        }
                    });
                } else if (type === 'camera') {
                    if (!navigator.mediaDevices.getUserMedia) {
                        throw new Error('您的浏览器不支持摄像头访问。');
                    }
                    localStream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 1280 },
                            height: { ideal: 720 },
                            facingMode: 'user'
                        },
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true
                        }
                    });
                } else if (type === 'file') {
                    // 文件展示类型，显示文件选择界面
                    placeholder.innerHTML = `
                        <div>
                            <i class="fas fa-file fa-3x d-block mb-3"></i>
                            <p>文件展示模式</p>
                            <input type="file" id="fileInput" class="form-control" accept="image/*,video/*,.pdf,.ppt,.pptx,.doc,.docx">
                            <small class="text-muted">支持图片、视频、PDF、PPT、Word文档</small>
                        </div>
                    `;
                    video.style.display = 'none';
                    placeholder.style.display = 'block';

                    // 模拟一个流用于按钮状态
                    localStream = new MediaStream();
                    return;
                }

                if (localStream) {
                    video.srcObject = localStream;
                    video.style.display = 'block';
                    placeholder.style.display = 'none';
                }

            } catch (error) {
                console.error('获取媒体流失败:', error);
                let errorMessage = '获取媒体流失败: ';

                if (error.name === 'NotAllowedError') {
                    errorMessage += '用户拒绝了权限请求。请允许访问摄像头/屏幕。';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += '未找到可用的媒体设备。';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += '浏览器不支持此功能。请使用HTTPS连接或更新浏览器。';
                } else if (error.name === 'NotReadableError') {
                    errorMessage += '媒体设备被其他应用占用。';
                } else {
                    errorMessage += error.message;
                }

                showStatus(errorMessage, 'danger');
            }
        }
        
        // 更新开始按钮状态
        function updateStartButton() {
            const startBtn = document.getElementById('startShareBtn');
            const hasGroup = selectedGroup !== null;
            const hasType = selectedShareType !== null;
            const hasStream = localStream !== null;

            startBtn.disabled = !(hasGroup && hasType && hasStream);

            // 更新按钮文本提示
            if (!hasGroup) {
                startBtn.textContent = '等待小组信息...';
            } else if (!hasType) {
                startBtn.textContent = '请选择投屏类型';
            } else if (!hasStream) {
                startBtn.textContent = '请开始预览';
            } else {
                startBtn.textContent = '开始投屏';
            }
        }
        
        // 开始投屏
        function startScreenShare() {
            if (!selectedGroup || !selectedShareType) {
                showStatus('请选择小组和投屏类型', 'warning');
                return;
            }

            // 对于文件类型，检查是否选择了文件
            if (selectedShareType === 'file') {
                const fileInput = document.getElementById('fileInput');
                if (!fileInput || !fileInput.files.length) {
                    showStatus('请选择要分享的文件', 'warning');
                    return;
                }
            } else if (!localStream) {
                showStatus('请先开始预览', 'warning');
                return;
            }

            showStatus('正在请求投屏...', 'info');

            // 准备投屏数据
            const shareData = {
                student_id: studentInfo.id,
                student_name: studentInfo.name,
                group_id: selectedGroup.id,
                stream_type: selectedShareType
            };

            // 如果是文件类型，添加文件信息
            if (selectedShareType === 'file') {
                const fileInput = document.getElementById('fileInput');
                const file = fileInput.files[0];
                shareData.file_name = file.name;
                shareData.file_type = file.type;
                shareData.file_size = file.size;
            }

            // 发送投屏请求
            socket.emit('request_screen_share', shareData);

            isSharing = true;
            document.getElementById('startShareBtn').style.display = 'none';
            document.getElementById('stopShareBtn').style.display = 'inline-block';
        }
        
        // 停止投屏
        function stopScreenShare() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            
            if (rtspPublisher) {
                rtspPublisher.stop();
                rtspPublisher = null;
            }
            
            // 通知服务器停止投屏
            socket.emit('stop_screen_share', {
                student_id: studentInfo.id,
                group_id: selectedGroup.id
            });
            
            isSharing = false;
            document.getElementById('startShareBtn').style.display = 'inline-block';
            document.getElementById('stopShareBtn').style.display = 'none';
            
            // 重置预览
            const video = document.getElementById('localVideo');
            const placeholder = document.getElementById('previewPlaceholder');
            video.style.display = 'none';
            placeholder.style.display = 'block';
            
            showStatus('投屏已停止', 'success');
        }
        
        // 开始RTSP推流 (简化实现)
        function startRTSPPublish(rtspUrl, streamName) {
            // 这里需要实现RTSP推流
            // 由于浏览器不直接支持RTSP，可能需要使用WebRTC转RTSP的方案
            console.log('开始RTSP推流:', rtspUrl, streamName);
            showStatus('投屏已开始', 'success');
        }
        
        // 显示状态
        function showStatus(message, type = 'info') {
            const statusArea = document.getElementById('statusArea');
            const statusText = document.getElementById('statusText');
            
            statusText.textContent = message;
            statusArea.className = `mt-4 alert alert-${type}`;
            statusArea.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                statusArea.style.display = 'none';
            }, 3000);
        }
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (isSharing) {
                stopScreenShare();
            }
            if (socket) {
                socket.disconnect();
            }
        });
    </script>
</body>
</html>
