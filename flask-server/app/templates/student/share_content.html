<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享内容到小组</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/student.css') }}" rel="stylesheet">
    <style>
        .share-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .content-tabs {
            margin-bottom: 20px;
        }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        .file-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        
        .file-item {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            background: #fff;
            width: 200px;
            text-align: center;
            position: relative;
        }
        
        .file-item img {
            max-width: 100%;
            max-height: 120px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .file-item .file-icon {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .file-item .file-name {
            font-size: 12px;
            word-break: break-all;
            margin-bottom: 5px;
        }
        
        .file-item .file-size {
            font-size: 10px;
            color: #6c757d;
        }
        
        .file-item .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .artwork-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .artwork-item {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .artwork-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .artwork-item.selected {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.3);
        }
        
        .artwork-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        
        .artwork-item .artwork-info {
            padding: 10px;
        }
        
        .artwork-item .artwork-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .artwork-item .artwork-date {
            font-size: 12px;
            color: #6c757d;
        }
        
        .group-selection {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .group-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .group-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
        }
        
        .group-card.selected {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        
        .share-controls {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            bottom: 20px;
        }
        
        .layout-options {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }
        
        .layout-btn {
            border: 2px solid #dee2e6;
            background: #fff;
            border-radius: 8px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            text-align: center;
        }
        
        .layout-btn:hover {
            border-color: #007bff;
        }
        
        .layout-btn.active {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        
        .progress-area {
            margin-top: 15px;
            display: none;
        }
        
        .progress-bar {
            height: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="share-container">
            <h2 class="text-center mb-4">
                <i class="fas fa-share-alt"></i> 分享内容到小组
            </h2>
            
            <!-- 小组选择 -->
            <div class="group-selection">
                <h4><i class="fas fa-users"></i> 选择目标小组</h4>
                <div id="groupList" class="row">
                    <!-- 小组列表将动态加载 -->
                </div>
            </div>
            
            <!-- 内容标签页 -->
            <ul class="nav nav-tabs content-tabs" id="contentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab">
                        <i class="fas fa-file"></i> 文件资料
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="artworks-tab" data-bs-toggle="tab" data-bs-target="#artworks" type="button" role="tab">
                        <i class="fas fa-image"></i> 作品展示
                    </button>
                </li>
            </ul>
            
            <!-- 标签页内容 -->
            <div class="tab-content" id="contentTabsContent">
                <!-- 文件资料标签页 -->
                <div class="tab-pane fade show active" id="files" role="tabpanel">
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>拖拽文件到此处或点击上传</h5>
                        <p class="text-muted">支持 PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, 图片等格式</p>
                        <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif" style="display: none;">
                        <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-plus"></i> 选择文件
                        </button>
                    </div>
                    
                    <div class="file-preview" id="filePreview">
                        <!-- 文件预览将在这里显示 -->
                    </div>
                </div>
                
                <!-- 作品展示标签页 -->
                <div class="tab-pane fade" id="artworks" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>我的作品</h5>
                        <button class="btn btn-outline-primary" id="uploadArtworkBtn">
                            <i class="fas fa-plus"></i> 上传新作品
                        </button>
                    </div>
                    
                    <div class="artwork-gallery" id="artworkGallery">
                        <!-- 作品画廊将在这里显示 -->
                    </div>
                    
                    <input type="file" id="artworkInput" accept="image/*" style="display: none;">
                </div>
            </div>
            
            <!-- 分享控制 -->
            <div class="share-controls">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h6>展示布局</h6>
                        <div class="layout-options">
                            <div class="layout-btn active" data-layout="single">
                                <i class="fas fa-square"></i><br>
                                <small>单画面</small>
                            </div>
                            <div class="layout-btn" data-layout="double">
                                <i class="fas fa-th-large"></i><br>
                                <small>双画面</small>
                            </div>
                            <div class="layout-btn" data-layout="quad">
                                <i class="fas fa-th"></i><br>
                                <small>四画面</small>
                            </div>
                            <div class="layout-btn" data-layout="grid">
                                <i class="fas fa-border-all"></i><br>
                                <small>网格</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="d-flex gap-2 justify-content-end">
                            <button class="btn btn-outline-secondary" onclick="history.back()">
                                <i class="fas fa-arrow-left"></i> 返回
                            </button>
                            <button class="btn btn-success" id="shareBtn" disabled>
                                <i class="fas fa-share"></i> 分享到小组
                            </button>
                        </div>
                        
                        <div class="progress-area" id="progressArea">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small>上传进度</small>
                                <small id="progressText">0%</small>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Socket.IO -->
    <script src="{{ url_for('static', filename='js/socket.io.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    
    <script>
        // 全局变量
        let socket = null;
        let selectedGroup = null;
        let selectedFiles = [];
        let selectedArtworks = [];
        let selectedLayout = 'single';
        
        // 学生信息
        const studentInfo = {
            id: '{{ session.student_id }}',
            name: '{{ session.student_name }}',
            token: '{{ session.token }}'
        };
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            loadAvailableGroups();
            loadMyArtworks();
            setupEventListeners();
            setupDragAndDrop();
        });
        
        // 初始化Socket连接
        function initializeSocket() {
            socket = io();
            
            // 认证
            socket.emit('authenticate', { token: studentInfo.token });
            
            socket.on('authenticated', function(data) {
                if (data.success) {
                    console.log('Socket认证成功');
                } else {
                    console.error('Socket认证失败:', data.error);
                }
            });
            
            // 监听分享响应
            socket.on('file_share_response', function(data) {
                if (data.success) {
                    showAlert('文件分享成功！', 'success');
                    resetForm();
                } else {
                    showAlert('文件分享失败: ' + data.error, 'danger');
                }
                hideProgress();
            });
            
            socket.on('artwork_share_response', function(data) {
                if (data.success) {
                    showAlert('作品分享成功！', 'success');
                    resetForm();
                } else {
                    showAlert('作品分享失败: ' + data.error, 'danger');
                }
                hideProgress();
            });
        }
        
        // 加载可用小组
        function loadAvailableGroups() {
            fetch('/student/api/groups')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderGroupList(data.groups);
                    } else {
                        console.error('获取小组列表失败');
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                });
        }
        
        // 渲染小组列表
        function renderGroupList(groups) {
            const groupList = document.getElementById('groupList');
            groupList.innerHTML = '';
            
            groups.forEach(group => {
                if (group.status === 'online') {
                    const groupCard = document.createElement('div');
                    groupCard.className = 'col-md-4';
                    groupCard.innerHTML = `
                        <div class="group-card" data-group='${JSON.stringify(group)}'>
                            <h6>
                                <span class="status-indicator status-${group.status}"></span>
                                ${group.name}
                            </h6>
                            <p class="text-muted mb-0">IP: ${group.ip}</p>
                        </div>
                    `;
                    
                    groupCard.addEventListener('click', () => selectGroup(group));
                    groupList.appendChild(groupCard);
                }
            });
        }
        
        // 选择小组
        function selectGroup(group) {
            document.querySelectorAll('.group-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            event.currentTarget.querySelector('.group-card').classList.add('selected');
            selectedGroup = group;
            updateShareButton();
        }
        
        // 加载我的作品
        function loadMyArtworks() {
            // 这里应该从服务器加载学生的作品
            // 暂时使用模拟数据
            const mockArtworks = [
                { id: 1, title: '数学作业', url: '/static/images/sample1.jpg', date: '2024-01-15' },
                { id: 2, title: '科学实验报告', url: '/static/images/sample2.jpg', date: '2024-01-14' },
                { id: 3, title: '美术作品', url: '/static/images/sample3.jpg', date: '2024-01-13' }
            ];
            
            renderArtworkGallery(mockArtworks);
        }
        
        // 渲染作品画廊
        function renderArtworkGallery(artworks) {
            const gallery = document.getElementById('artworkGallery');
            gallery.innerHTML = '';
            
            artworks.forEach(artwork => {
                const artworkItem = document.createElement('div');
                artworkItem.className = 'artwork-item';
                artworkItem.innerHTML = `
                    <img src="${artwork.url}" alt="${artwork.title}" onerror="this.style.display='none'; this.parentNode.style.background='#f0f0f0'; this.parentNode.innerHTML='<div style=\\'display:flex;align-items:center;justify-content:center;height:100%;color:#999;\\'>暂无图片</div>'">
                    <div class="artwork-info">
                        <div class="artwork-title">${artwork.title}</div>
                        <div class="artwork-date">${artwork.date}</div>
                    </div>
                `;
                
                artworkItem.addEventListener('click', () => toggleArtworkSelection(artwork, artworkItem));
                gallery.appendChild(artworkItem);
            });
        }
        
        // 切换作品选择
        function toggleArtworkSelection(artwork, element) {
            const index = selectedArtworks.findIndex(a => a.id === artwork.id);
            
            if (index > -1) {
                selectedArtworks.splice(index, 1);
                element.classList.remove('selected');
            } else {
                selectedArtworks.push(artwork);
                element.classList.add('selected');
            }
            
            updateShareButton();
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 文件输入
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);
            
            // 作品上传
            document.getElementById('uploadArtworkBtn').addEventListener('click', function() {
                document.getElementById('artworkInput').click();
            });
            
            document.getElementById('artworkInput').addEventListener('change', handleArtworkUpload);
            
            // 布局选择
            document.querySelectorAll('.layout-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.layout-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    selectedLayout = this.dataset.layout;
                });
            });
            
            // 分享按钮
            document.getElementById('shareBtn').addEventListener('click', shareContent);
        }
        
        // 设置拖拽上传
        function setupDragAndDrop() {
            const uploadArea = document.getElementById('uploadArea');
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = Array.from(e.dataTransfer.files);
                handleFiles(files);
            });
            
            uploadArea.addEventListener('click', function() {
                document.getElementById('fileInput').click();
            });
        }
        
        // 处理文件选择
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            handleFiles(files);
        }
        
        // 处理文件
        function handleFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    selectedFiles.push(file);
                    addFilePreview(file);
                }
            });
            
            updateShareButton();
        }
        
        // 验证文件
        function validateFile(file) {
            const allowedTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/plain',
                'image/jpeg',
                'image/png',
                'image/gif'
            ];
            
            if (!allowedTypes.includes(file.type)) {
                showAlert('不支持的文件类型: ' + file.name, 'warning');
                return false;
            }
            
            if (file.size > 50 * 1024 * 1024) { // 50MB
                showAlert('文件太大: ' + file.name, 'warning');
                return false;
            }
            
            return true;
        }
        
        // 添加文件预览
        function addFilePreview(file) {
            const preview = document.getElementById('filePreview');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            const fileIcon = getFileIcon(file.type);
            const fileSize = formatFileSize(file.size);
            
            fileItem.innerHTML = `
                <button class="remove-btn" onclick="removeFile('${file.name}')">×</button>
                <div class="file-icon">${fileIcon}</div>
                <div class="file-name">${file.name}</div>
                <div class="file-size">${fileSize}</div>
            `;
            
            // 如果是图片，显示预览
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    fileItem.innerHTML = `
                        <button class="remove-btn" onclick="removeFile('${file.name}')">×</button>
                        <img src="${e.target.result}" alt="${file.name}">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${fileSize}</div>
                    `;
                };
                reader.readAsDataURL(file);
            }
            
            preview.appendChild(fileItem);
        }
        
        // 移除文件
        function removeFile(fileName) {
            selectedFiles = selectedFiles.filter(file => file.name !== fileName);
            
            // 移除预览
            const preview = document.getElementById('filePreview');
            const fileItems = preview.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                if (item.querySelector('.file-name').textContent === fileName) {
                    item.remove();
                }
            });
            
            updateShareButton();
        }
        
        // 获取文件图标
        function getFileIcon(fileType) {
            if (fileType.includes('pdf')) return '<i class="fas fa-file-pdf"></i>';
            if (fileType.includes('word')) return '<i class="fas fa-file-word"></i>';
            if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '<i class="fas fa-file-powerpoint"></i>';
            if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '<i class="fas fa-file-excel"></i>';
            if (fileType.includes('text')) return '<i class="fas fa-file-alt"></i>';
            if (fileType.includes('image')) return '<i class="fas fa-file-image"></i>';
            return '<i class="fas fa-file"></i>';
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 处理作品上传
        function handleArtworkUpload(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                // 这里应该上传到服务器
                console.log('上传作品:', file.name);
                // 重新加载作品列表
                loadMyArtworks();
            }
        }
        
        // 分享内容
        function shareContent() {
            if (!selectedGroup) {
                showAlert('请选择目标小组', 'warning');
                return;
            }
            
            const activeTab = document.querySelector('.nav-link.active').id;
            
            if (activeTab === 'files-tab' && selectedFiles.length === 0) {
                showAlert('请选择要分享的文件', 'warning');
                return;
            }
            
            if (activeTab === 'artworks-tab' && selectedArtworks.length === 0) {
                showAlert('请选择要分享的作品', 'warning');
                return;
            }
            
            showProgress();
            
            if (activeTab === 'files-tab') {
                shareFiles();
            } else {
                shareArtworks();
            }
        }
        
        // 分享文件
        function shareFiles() {
            // 这里应该先上传文件到服务器，然后分享文件信息
            const fileInfos = selectedFiles.map(file => ({
                filename: file.name,
                size: file.size,
                type: file.type,
                url: '/uploads/' + file.name // 模拟URL
            }));
            
            socket.emit('share_file_to_group', {
                student_id: studentInfo.id,
                student_name: studentInfo.name,
                group_id: selectedGroup.id,
                file_info: {
                    files: fileInfos,
                    layout: selectedLayout,
                    shared_at: new Date().toISOString()
                }
            });
        }
        
        // 分享作品
        function shareArtworks() {
            socket.emit('share_artwork_to_group', {
                student_id: studentInfo.id,
                student_name: studentInfo.name,
                group_id: selectedGroup.id,
                artwork_info: {
                    artworks: selectedArtworks,
                    layout: selectedLayout,
                    shared_at: new Date().toISOString()
                }
            });
        }
        
        // 更新分享按钮状态
        function updateShareButton() {
            const shareBtn = document.getElementById('shareBtn');
            const activeTab = document.querySelector('.nav-link.active').id;
            
            let hasContent = false;
            if (activeTab === 'files-tab') {
                hasContent = selectedFiles.length > 0;
            } else {
                hasContent = selectedArtworks.length > 0;
            }
            
            shareBtn.disabled = !selectedGroup || !hasContent;
        }
        
        // 显示进度
        function showProgress() {
            document.getElementById('progressArea').style.display = 'block';
            // 模拟进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                document.getElementById('progressBar').style.width = progress + '%';
                document.getElementById('progressText').textContent = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 200);
        }
        
        // 隐藏进度
        function hideProgress() {
            setTimeout(() => {
                document.getElementById('progressArea').style.display = 'none';
                document.getElementById('progressBar').style.width = '0%';
                document.getElementById('progressText').textContent = '0%';
            }, 1000);
        }
        
        // 重置表单
        function resetForm() {
            selectedFiles = [];
            selectedArtworks = [];
            document.getElementById('filePreview').innerHTML = '';
            document.querySelectorAll('.artwork-item').forEach(item => {
                item.classList.remove('selected');
            });
            updateShareButton();
        }
        
        // 显示提示
        function showAlert(message, type) {
            // 创建提示框
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alert);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }
        
        // 标签页切换时更新分享按钮
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', updateShareButton);
        });
    </script>
</body>
</html>
