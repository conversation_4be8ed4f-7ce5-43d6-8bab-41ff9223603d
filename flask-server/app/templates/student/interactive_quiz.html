{% extends "student/base.html" %}

{% block title %}互动答题 - {{ quiz_title }}{% endblock %}

{% block styles %}
<style>
    .quiz-container {
        display: flex;
        gap: 20px;
    }
    .question-panel {
        flex: 1;
    }
    .side-panel {
        width: 280px;
    }
    .question-item {
        margin-bottom: 25px;
        padding: 20px;
        border: 1px solid #e6e6e6;
        border-radius: 6px;
        background: #fff;
    }
    .question-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
    }
    .question-options .layui-form-radio,
    .question-options .layui-form-checkbox {
        margin-bottom: 12px;
    }
    .submit-question-btn {
        margin-top: 15px;
    }
    .question-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 12px;
    }
    .question-item {
        position: relative;
    }
    .status-submitted {
        background: #5FB878;
        color: #fff;
    }
    .status-pending {
        background: #FF5722;
        color: #fff;
    }
    #question-nav-container .layui-btn {
        margin: 3px;
    }
    .nav-submitted {
        background-color: #5FB878 !important;
        border-color: #5FB878 !important;
    }
    .quiz-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 6px;
        margin-bottom: 20px;
    }
    .quiz-instructions {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
        border-left: 4px solid #01AAED;
    }
</style>
{% endblock %}

{% block content %}
<div class="quiz-header">
    <h3>{{ quiz_title }}</h3>
    <p>🚀 实时互动答题 - 每道题提交后立即显示给老师</p>
</div>

<div class="quiz-instructions">
    <h4>📝 答题说明：</h4>
    <ul>
        <li>• 这是实时互动答题，每道题答完后请点击"提交这道题"</li>
        <li>• 老师可以实时看到全班的答题情况</li>
        <li>• 已提交的题目可以重新修改和提交</li>
        <li>• 绿色按钮表示已提交，红色表示未提交</li>
    </ul>
</div>

<div class="layui-card">
    <div class="layui-card-body">
        <div class="quiz-container">
            <!-- 题目区域 -->
            <div class="question-panel">
                <form class="layui-form" id="quiz-form">
                    {% for question in questions %}
                    <div class="question-item" id="q-{{ loop.index }}">
                        <div class="question-status status-pending" id="status-{{ question.id }}">未提交</div>
                        <div class="question-title">
                            <span>{{ loop.index }}. {{ question.content | safe }}</span>
                        </div>
                        <div class="question-options">
                            {% if question.type == 'judge' %}
                                <input type="radio" name="q_{{ question.id }}" value="A" title="A. 正确" lay-skin="primary">
                                <input type="radio" name="q_{{ question.id }}" value="B" title="B. 错误" lay-skin="primary">
                            {% elif question.type == 'single' %}
                                {% for option in question.options %}
                                <input type="radio" name="q_{{ question.id }}" value="{{ 'ABCDEFGHIJKLMNOP'[loop.index0] }}" title="{{ 'ABCDEFGHIJKLMNOP'[loop.index0] }}. {{ option }}" lay-skin="primary">
                                {% endfor %}
                            {% elif question.type == 'multiple' %}
                                {% for option in question.options %}
                                <input type="checkbox" name="q_{{ question.id }}" value="{{ 'ABCDEFGHIJKLMNOP'[loop.index0] }}" title="{{ 'ABCDEFGHIJKLMNOP'[loop.index0] }}. {{ option }}" lay-skin="primary">
                                {% endfor %}
                            {% endif %}
                        </div>
                        <button type="button" class="layui-btn layui-btn-sm submit-question-btn" 
                                data-question-id="{{ question.id }}" data-question-index="{{ loop.index }}">
                            提交这道题
                        </button>
                    </div>
                    {% endfor %}
                </form>
            </div>
            
            <!-- 侧边栏 -->
            <div class="side-panel">
                <div class="layui-card">
                    <div class="layui-card-header">答题进度</div>
                    <div class="layui-card-body" id="question-nav-container">
                        {% for question in questions %}
                        <a href="#q-{{ loop.index }}" class="layui-btn layui-btn-primary layui-btn-sm nav-btn" 
                           data-question-id="{{ question.id }}">{{ loop.index }}</a>
                        {% endfor %}
                    </div>
                </div>
                
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">统计信息</div>
                    <div class="layui-card-body">
                        <p>总题数：<span class="layui-badge">{{ questions|length }}</span></p>
                        <p>已提交：<span id="submitted-count" class="layui-badge layui-bg-green">0</span></p>
                        <p>未提交：<span id="pending-count" class="layui-badge layui-bg-orange">{{ questions|length }}</span></p>
                    </div>
                </div>
                
                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-body">
                        <button id="refresh-status-btn" class="layui-btn layui-btn-fluid layui-btn-warm">刷新状态</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    
    var homeworkId = "{{ homework_id }}";
    var submittedQuestions = new Set();
    var quizEnded = false;
    
    // 提交单道题目
    $('.submit-question-btn').on('click', function(){
        if (quizEnded) {
            layer.msg('答题已结束，无法提交', {icon: 0});
            return;
        }
        
        var questionId = $(this).data('question-id');
        var questionIndex = $(this).data('question-index');
        
        // 获取该题目的答案
        var answer = getQuestionAnswer(questionId);
        if (!answer) {
            layer.msg('请先选择答案', {icon: 0});
            return;
        }
        
        submitSingleQuestion(questionId, answer, questionIndex);
    });
    
    // 获取单道题目的答案
    function getQuestionAnswer(questionId) {
        var name = 'q_' + questionId;
        var checkedInputs = $(`input[name="${name}"]:checked`);
        
        if (checkedInputs.length === 0) {
            return null;
        }
        
        var answers = [];
        checkedInputs.each(function() {
            answers.push($(this).val());
        });
        
        return answers.join(',');
    }
    
    // 提交单道题目
    function submitSingleQuestion(questionId, answer, questionIndex) {
        var loading = layer.load(1, {shade: [0.3, '#000']});
        
        $.ajax({
            url: "/api/student/homework/" + homeworkId + "/submit",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                question_id: questionId,
                answer: answer
            }),
            success: function(res) {
                layer.close(loading);
                if (res.status === 'success') {
                    markQuestionAsSubmitted(questionId, questionIndex);
                    layer.msg('提交成功！', {icon: 1, time: 1000});
                } else {
                    layer.msg(res.message || '提交失败', {icon: 2});
                }
            },
            error: function(xhr) {
                layer.close(loading);
                var message = '提交失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                layer.msg(message, {icon: 2});
            }
        });
    }
    
    // 标记题目为已提交
    function markQuestionAsSubmitted(questionId, questionIndex) {
        submittedQuestions.add(questionId);
        
        // 更新题目状态显示
        $('#status-' + questionId).removeClass('status-pending').addClass('status-submitted').text('已提交');
        
        // 更新导航按钮
        $(`.nav-btn[data-question-id="${questionId}"]`).removeClass('layui-btn-primary').addClass('nav-submitted');
        
        // 更新统计信息
        updateStatistics();
    }
    
    // 更新统计信息
    function updateStatistics() {
        var submittedCount = submittedQuestions.size;
        var totalCount = {{ questions|length }};
        var pendingCount = totalCount - submittedCount;
        
        $('#submitted-count').text(submittedCount);
        $('#pending-count').text(pendingCount);
    }
    
    // 刷新状态按钮
    $('#refresh-status-btn').on('click', function() {
        location.reload();
    });
    
    // 监听选项变化
    form.on('radio', function(data){
        enableSubmitButton(data.elem.name);
    });
    
    form.on('checkbox', function(data){
        enableSubmitButton(data.elem.name);
    });
    
    function enableSubmitButton(questionName) {
        var questionId = questionName.split('_')[1];
        var submitBtn = $(`.submit-question-btn[data-question-id="${questionId}"]`);
        submitBtn.removeClass('layui-btn-disabled');
    }
    
    // 页面加载时的初始化
    loadSubmittedAnswers();
    setupWebSocket();
    
    // 加载已提交的答案
    function loadSubmittedAnswers() {
        $.ajax({
            url: "/api/student/homework/" + homeworkId + "/submissions",
            type: "GET",
            success: function(res) {
                if (res.status === 'success') {
                    var submissions = res.submissions;
                    for (var questionId in submissions) {
                        // 恢复答案选择
                        var answer = submissions[questionId].answer;
                        restoreQuestionAnswer(questionId, answer);
                        
                        // 标记为已提交
                        var questionIndex = getQuestionIndex(questionId);
                        if (questionIndex) {
                            markQuestionAsSubmitted(questionId, questionIndex);
                        }
                    }
                }
            },
            error: function() {
                console.log('加载提交记录失败');
            }
        });
    }
    
    // 恢复题目答案
    function restoreQuestionAnswer(questionId, answer) {
        var name = 'q_' + questionId;
        var answers = answer.split(',');
        
        answers.forEach(function(ans) {
            var input = $(`input[name="${name}"][value="${ans.trim()}"]`);
            if (input.length > 0) {
                input.prop('checked', true);
            }
        });
        
        // 重新渲染layui表单
        form.render();
    }
    
    // 获取题目序号
    function getQuestionIndex(questionId) {
        var btn = $(`.submit-question-btn[data-question-id="${questionId}"]`);
        return btn.data('question-index');
    }
    
    // 设置WebSocket监听
    function setupWebSocket() {
        // 简单的WebSocket连接（这里假设有全局的socket连接）
        // 在实际项目中，你需要根据你的WebSocket实现来调整
        if (typeof io !== 'undefined') {
            var socket = io();
            socket.on('quiz_ended', function(data) {
                if (data.homework_id === homeworkId) {
                    handleQuizEnded(data.message);
                }
            });
        }
    }
    
    // 处理答题结束
    function handleQuizEnded(message) {
        quizEnded = true;
        $('.submit-question-btn').addClass('layui-btn-disabled').text('已结束');
        layer.alert(message, {icon: 1, title: '答题结束'});
    }
});
</script>
{% endblock %}