{% extends "dashboard/base.html" %}

{% block title %}课件管理{% endblock %}

{% block styles %}
<style>
    .file-manager-container {
        background: #fff;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .toolbar {
        padding: 15px 20px;
        border-bottom: 1px solid #e6e6e6;
        background: #fafafa;
        border-radius: 6px 6px 0 0;
    }

    .folder-tree {
        height: 600px;
        overflow-y: auto;
        border-right: 1px solid #e6e6e6;
        background: #fafafa;
    }

    .folder-item {
        padding: 8px 15px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s;
        user-select: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .folder-actions {
        display: flex;
        gap: 5px;
        visibility: hidden;
    }

    .folder-item:hover .folder-actions {
        visibility: visible;
    }

    .folder-item:hover {
        background: #e6f7ff;
    }

    .folder-item.active {
        background: #1890ff;
        color: white;
    }

    .folder-item .layui-icon {
        margin-right: 8px;
        font-size: 16px;
    }

    .file-list-area {
        height: 600px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .search-bar {
        padding: 15px 20px;
        border-bottom: 1px solid #e6e6e6;
        background: #fafafa;
    }

    .file-list {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
    }

    .file-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 10px;
        transition: all 0.2s;
        cursor: pointer;
    }

    .file-item:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24,144,255,0.2);
    }

    .file-item.selected {
        border-color: #1890ff;
        background: #e6f7ff;
    }

    .file-icon {
        width: 40px;
        height: 40px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 20px;
        color: white;
    }

    .file-icon.pdf { background: #ff4d4f; }
    .file-icon.doc { background: #1890ff; }
    .file-icon.xls { background: #52c41a; }
    .file-icon.ppt { background: #fa8c16; }
    .file-icon.img { background: #722ed1; }
    .file-icon.default { background: #8c8c8c; }

    .file-info {
        flex: 1;
    }

    .file-name {
        font-weight: 500;
        margin-bottom: 4px;
        color: #262626;
    }

    .file-meta {
        font-size: 12px;
        color: #8c8c8c;
    }

    .file-actions {
        display: flex;
        gap: 5px;
    }

    .batch-actions {
        padding: 10px 20px;
        background: #f0f8ff;
        border-bottom: 1px solid #e6e6e6;
        display: none;
    }

    .batch-actions.show {
        display: block;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #8c8c8c;
    }

    .empty-state .layui-icon {
        font-size: 48px;
        margin-bottom: 15px;
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-card">
    <div class="layui-card-header">
        <h2>课件管理</h2>
    </div>

    <div class="layui-card-body" style="padding: 0;">
        <div class="file-manager-container">
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <button class="layui-btn layui-btn-normal" id="createFolderBtn">
                            <i class="layui-icon layui-icon-add-1"></i>新建文件夹
                        </button>
                        <button class="layui-btn layui-btn-warm" id="uploadBtn">
                            <i class="layui-icon layui-icon-upload"></i>上传文件
                        </button>
                        <button class="layui-btn layui-btn-danger" id="deleteSelectedBtn" style="display: none;">
                            <i class="layui-icon layui-icon-delete"></i>删除选中
                        </button>
                        <button class="layui-btn" id="moveSelectedBtn" style="display: none;">
                            <i class="layui-icon layui-icon-next"></i>移动选中
                        </button>
                    </div>
                    <div class="layui-col-md6" style="text-align: right;">
                        <span id="selectedCount" style="color: #666; margin-right: 15px; display: none;">
                            已选择 <span id="selectedNum">0</span> 个文件
                        </span>
                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="selectAllBtn">
                            <i class="layui-icon layui-icon-ok"></i>全选
                        </button>
                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="clearSelectBtn" style="display: none;">
                            <i class="layui-icon layui-icon-close"></i>取消选择
                        </button>
                    </div>
                </div>
            </div>

            <!-- 批量操作提示栏 -->
            <div class="batch-actions" id="batchActions">
                <div class="layui-row">
                    <div class="layui-col-md8">
                        <i class="layui-icon layui-icon-tips" style="color: #1890ff;"></i>
                        已选择 <span id="batchSelectedNum">0</span> 个文件，可以进行批量操作
                    </div>
                    <div class="layui-col-md4" style="text-align: right;">
                        <button class="layui-btn layui-btn-xs layui-btn-danger" id="batchDeleteBtn">
                            <i class="layui-icon layui-icon-delete"></i>批量删除
                        </button>
                        <button class="layui-btn layui-btn-xs" id="batchMoveBtn">
                            <i class="layui-icon layui-icon-next"></i>批量移动
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-row" style="height: 600px;">
                <!-- 文件夹树 -->
                <div class="layui-col-md3">
                    <div class="folder-tree">
                        <div class="folder-item active" data-folder-id="" data-folder-name="根目录">
                            <i class="layui-icon layui-icon-home"></i>根目录
                        </div>
                        <div id="folderTreeContainer">
                            <!-- 动态加载文件夹树 -->
                        </div>
                    </div>
                </div>

                <!-- 文件列表区域 -->
                <div class="layui-col-md9">
                    <div class="file-list-area">
                        <!-- 搜索栏 -->
                        <div class="search-bar">
                            <div class="layui-row">
                                <div class="layui-col-md8">
                                    <div class="layui-input-group">
                                        <input type="text" id="searchInput" placeholder="搜索文件..." class="layui-input">
                                        <div class="layui-input-split layui-input-suffix">
                                            <button class="layui-btn layui-btn-primary" id="searchBtn">
                                                <i class="layui-icon layui-icon-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md4" style="text-align: right;">
                                    <span id="currentFolderName" style="color: #666; line-height: 38px;">根目录</span>
                                </div>
                            </div>
                        </div>

                        <!-- 文件列表 -->
                        <div class="file-list" id="fileListContainer">
                            <div class="empty-state" id="emptyState">
                                <i class="layui-icon layui-icon-file"></i>
                                <div>当前文件夹为空</div>
                                <div style="font-size: 12px; margin-top: 5px;">点击上传按钮添加文件</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 隐藏的文件输入 -->
<input type="file" id="fileInput" multiple style="display: none;">

<!-- 新建文件夹弹窗 -->
<div id="createFolderModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="createFolderForm">
        <div class="layui-form-item">
            <label class="layui-form-label">文件夹名称</label>
            <div class="layui-input-block">
                <input type="text" name="folder_name" placeholder="请输入文件夹名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn layui-btn-normal" lay-submit lay-filter="createFolderForm">创建</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>

<!-- 移动文件弹窗 -->
<div id="moveFileModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="moveFileForm">
        <div class="layui-form-item">
            <label class="layui-form-label">目标文件夹</label>
            <div class="layui-input-block">
                <select name="target_folder_id" lay-verify="required">
                    <option value="">请选择目标文件夹</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn layui-btn-normal" lay-submit lay-filter="moveFileForm">移动</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>

<!-- 分配文件到课堂弹窗 -->
<div id="assignFileModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="assignFileForm">
        <div class="layui-form-item">
            <label class="layui-form-label">选择课堂</label>
            <div class="layui-input-block">
                <select name="schedule_id" lay-verify="required" lay-search>
                    <option value="">请选择课堂</option>
                    {% for schedule in course_schedules %}
                    <option value="{{ schedule.id }}">
                        {{ schedule.course_name }} - {{ schedule.class_name }}
                        ({{ schedule.day_of_week }} {{ schedule.start_time }}-{{ schedule.end_time }})
                    </option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn layui-btn-normal" lay-submit lay-filter="assignFileForm">确认分配</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
layui.use(['element', 'form', 'layer', 'upload'], function(){
    var element = layui.element;
    var form = layui.form;
    var layer = layui.layer;
    var upload = layui.upload;

    // 全局变量
    var currentFolderId = null;
    var selectedFiles = [];
    var allFolders = [];
    var allFiles = [];

    // 初始化
    $(document).ready(function() {
        loadFolderTree();
        loadFiles(null);
        bindEvents();
        initUpload();
    });

    // 绑定事件
    function bindEvents() {
        // 文件夹点击事件
        $(document).on('click', '.folder-item', function() {
            var folderId = $(this).data('folder-id');
            var folderName = $(this).data('folder-name');

            $('.folder-item').removeClass('active');
            $(this).addClass('active');

            currentFolderId = folderId;
            $('#currentFolderName').text(folderName);

            loadFiles(folderId);
            clearSelection();
        });

        // 文件选择事件
        $(document).on('click', '.file-item', function(e) {
            if (e.ctrlKey || e.metaKey) {
                // Ctrl/Cmd + 点击：多选
                toggleFileSelection($(this));
            } else {
                // 普通点击：单选
                clearSelection();
                toggleFileSelection($(this));
            }
            updateSelectionUI();
        });

        // 全选按钮
        $('#selectAllBtn').click(function() {
            $('.file-item').addClass('selected');
            selectedFiles = $('.file-item').map(function() {
                return parseInt($(this).data('file-id'));
            }).get();
            updateSelectionUI();
        });

        // 取消选择按钮
        $('#clearSelectBtn').click(function() {
            clearSelection();
        });

        // 新建文件夹按钮
        $('#createFolderBtn').click(function() {
            showCreateFolderModal();
        });

        // 上传文件按钮
        $('#uploadBtn').click(function() {
            $('#fileInput').click();
        });

        // 批量删除按钮
        $('#batchDeleteBtn, #deleteSelectedBtn').click(function() {
            if (selectedFiles.length === 0) {
                layer.msg('请先选择要删除的文件');
                return;
            }
            batchDeleteFiles();
        });

        // 批量移动按钮
        $('#batchMoveBtn, #moveSelectedBtn').click(function() {
            if (selectedFiles.length === 0) {
                layer.msg('请先选择要移动的文件');
                return;
            }
            showMoveFileModal(selectedFiles);
        });

        // 搜索功能
        $('#searchBtn').click(function() {
            performSearch();
        });

        $('#searchInput').keypress(function(e) {
            if (e.which === 13) {
                performSearch();
            }
        });
    }
    // 加载文件夹树
    function loadFolderTree() {
        $.get('/dashboard/get_folders', function(response) {
            if (response.status === 'success') {
                allFolders = response.folders;
                renderFolderTree();
            } else {
                layer.msg('加载文件夹失败: ' + response.message);
            }
        }).fail(function() {
            layer.msg('网络错误，请重试');
        });
    }

    // 渲染文件夹树
    function renderFolderTree() {
        var html = '';
        // 创建文件夹映射
        var folderMap = {};
        allFolders.forEach(function(folder) {
            folderMap[folder.id] = folder;
            folder.children = [];
        });
        
        // 创建文件夹树
        var rootFolders = [];
        allFolders.forEach(function(folder) {
            if (folder.parent_id === null || folder.parent_id === undefined) {
                rootFolders.push(folder);
            } else if (folderMap[folder.parent_id]) {
                folderMap[folder.parent_id].children.push(folder);
            }
        });
        
        // 递归渲染文件夹
        function renderFolderWithLevel(folder, level) {
            var indent = level * 20;
            html += '<div class="folder-item" data-folder-id="' + folder.id + '" data-folder-name="' + folder.folder_name + '" style="padding-left: ' + (15 + indent) + 'px;">';
            html += '<span><i class="layui-icon layui-icon-file"></i>' + folder.folder_name + '</span>';
            if (folder.id !== '') {
                html += '<div class="folder-actions">';
                html += '<button class="layui-btn layui-btn-xs" onclick="renameFolder(' + folder.id + ', \'' + folder.folder_name + '\'); event.stopPropagation();"><i class="layui-icon layui-icon-edit"></i></button>';
                html += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteFolder(' + folder.id + '); event.stopPropagation();"><i class="layui-icon layui-icon-delete"></i></button>';
                html += '</div>';
            }
            html += '</div>';
            folder.children.forEach(function(child) {
                renderFolderWithLevel(child, level + 1);
            });
        }
        
        // 渲染根文件夹
        rootFolders.forEach(function(folder) {
            renderFolderWithLevel(folder, 0);
        });
        
        $('#folderTreeContainer').html(html);
    }

    // 加载文件列表
    function loadFiles(folderId, searchTerm) {
        var params = {};
        if (folderId !== null) {
            params.folder_id = folderId;
        }
        if (searchTerm) {
            params.search = searchTerm;
        }

        $.get('/dashboard/get_files', params, function(response) {
            if (response.status === 'success') {
                allFiles = response.files;
                renderFileList();
            } else {
                layer.msg('加载文件失败: ' + response.message);
            }
        }).fail(function() {
            layer.msg('网络错误，请重试');
        });
    }

    // 渲染文件列表
    function renderFileList() {
        var container = $('#fileListContainer');

        if (allFiles.length === 0) {
            container.html('<div class="empty-state"><i class="layui-icon layui-icon-file"></i><div>当前文件夹为空</div><div style="font-size: 12px; margin-top: 5px;">点击上传按钮添加文件</div></div>');
            return;
        }

        var html = '';
        allFiles.forEach(function(file) {
            var fileIcon = getFileIcon(file);
            var fileSize = formatFileSize(file.file_size);

            html += '<div class="file-item" data-file-id="' + file.id + '">';
            html += '<div class="file-icon ' + fileIcon.class + '">';
            html += '<i class="layui-icon ' + fileIcon.icon + '"></i>';
            html += '</div>';
            html += '<div class="file-info">';
            html += '<div class="file-name">' + file.original_filename + '</div>';
            html += '<div class="file-meta">' + fileSize + ' • ' + file.upload_time + '</div>';
            html += '</div>';
            html += '<div class="file-actions">';
            html += '<button class="layui-btn layui-btn-xs" onclick="downloadFile(' + file.id + ')"><i class="layui-icon layui-icon-download-circle"></i></button>';
            html += '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="assignFile(' + file.id + ')"><i class="layui-icon layui-icon-share"></i></button>';
            html += '<button class="layui-btn layui-btn-xs" onclick="renameFile(' + file.id + ', \'' + file.original_filename + '\')"><i class="layui-icon layui-icon-edit"></i></button>';
            html += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteFile(' + file.id + ')"><i class="layui-icon layui-icon-delete"></i></button>';
            html += '</div>';
            html += '</div>';
        });

        container.html(html);
    }
    // 工具函数
    function getFileIcon(file) {
        var mimeType = file.mime_type || '';
        var filename = file.original_filename || '';
        var extension = filename.split('.').pop().toLowerCase();

        if (mimeType.includes('pdf') || extension === 'pdf') {
            return { class: 'pdf', icon: 'layui-icon-file' };
        } else if (mimeType.includes('word') || mimeType.includes('document') || ['doc', 'docx'].includes(extension)) {
            return { class: 'doc', icon: 'layui-icon-file' };
        } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet') || ['xls', 'xlsx'].includes(extension)) {
            return { class: 'xls', icon: 'layui-icon-file' };
        } else if (mimeType.includes('powerpoint') || mimeType.includes('presentation') || ['ppt', 'pptx'].includes(extension)) {
            return { class: 'ppt', icon: 'layui-icon-file' };
        } else if (mimeType.includes('image') || ['png', 'jpg', 'jpeg', 'gif', 'bmp'].includes(extension)) {
            return { class: 'img', icon: 'layui-icon-picture' };
        } else {
            return { class: 'default', icon: 'layui-icon-file' };
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        var k = 1024;
        var sizes = ['B', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function toggleFileSelection(fileItem) {
        var fileId = parseInt(fileItem.data('file-id'));
        var index = selectedFiles.indexOf(fileId);

        if (index > -1) {
            selectedFiles.splice(index, 1);
            fileItem.removeClass('selected');
        } else {
            selectedFiles.push(fileId);
            fileItem.addClass('selected');
        }
    }

    function clearSelection() {
        selectedFiles = [];
        $('.file-item').removeClass('selected');
        updateSelectionUI();
    }

    function updateSelectionUI() {
        var count = selectedFiles.length;

        if (count > 0) {
            $('#selectedCount, #batchActions').show();
            $('#selectedNum, #batchSelectedNum').text(count);
            $('#deleteSelectedBtn, #moveSelectedBtn, #clearSelectBtn').show();
            $('#selectAllBtn').hide();
        } else {
            $('#selectedCount, #batchActions').hide();
            $('#deleteSelectedBtn, #moveSelectedBtn, #clearSelectBtn').hide();
            $('#selectAllBtn').show();
        }
    }

    function performSearch() {
        var searchTerm = $('#searchInput').val().trim();
        loadFiles(currentFolderId, searchTerm);
    }
    // 初始化上传功能
    function initUpload() {
        $('#fileInput').change(function() {
            var files = this.files;
            if (files.length > 0) {
                uploadFiles(files);
            }
        });
    }

    // 上传文件
    function uploadFiles(files) {
        var formData = new FormData();

        for (var i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }

        if (currentFolderId) {
            formData.append('folder_id', currentFolderId);
        }

        var loadingIndex = layer.load(2, {content: '正在上传文件...'});

        $.ajax({
            url: '/dashboard/upload_files',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                layer.close(loadingIndex);
                if (response.status === 'success') {
                    layer.msg(response.message);
                    loadFiles(currentFolderId);
                } else {
                    layer.msg('上传失败: ' + response.message);
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('网络错误，请重试');
            }
        });
    }

    // 显示新建文件夹弹窗
    function showCreateFolderModal() {
        layer.open({
            type: 1,
            title: '新建文件夹',
            content: $('#createFolderModal'),
            area: ['400px', '250px'],
            success: function() {
                form.render();
            }
        });
    }

    // 显示移动文件弹窗
    function showMoveFileModal(fileIds) {
        loadFolderOptions();
        layer.open({
            type: 1,
            title: '移动文件',
            content: $('#moveFileModal'),
            area: ['400px', '250px'],
            success: function() {
                form.render();
            }
        });
    }

    // 加载文件夹选项
    function loadFolderOptions() {
        var select = $('select[name="target_folder_id"]');
        select.empty();
        select.append('<option value="">请选择目标文件夹</option>');

        allFolders.forEach(function(folder) {
            if (folder.id !== currentFolderId) {
                select.append('<option value="' + folder.id + '">' + folder.folder_name + '</option>');
            }
        });

        form.render('select');
    }
    // 全局函数
    window.downloadFile = function(fileId) {
        window.open('/dashboard/download_file/' + fileId);
    };

    window.renameFolder = function(folderId, currentName) {
        layer.prompt({
            title: '重命名文件夹',
            value: currentName
        }, function(newName, index) {
            if (newName.trim() === '') {
                layer.msg('文件夹名不能为空');
                return;
            }

            $.ajax({
                url: '/dashboard/rename_folder',
                type: 'POST',
                data: JSON.stringify({
                    folder_id: folderId,
                    new_name: newName.trim()
                }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.status === 'success') {
                        layer.msg(response.message);
                        loadFolderTree();
                    } else {
                        layer.msg('重命名失败: ' + response.message);
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试');
                }
            });
            layer.close(index);
        });
    };

    window.deleteFolder = function(folderId) {
        layer.confirm('确定要删除这个文件夹吗？此操作会将文件夹下的文件移动到根目录。', {icon: 3, title: '提示'}, function(index) {
            $.ajax({
                url: '/dashboard/delete_folder/' + folderId,
                type: 'DELETE',
                success: function(response) {
                    if (response.status === 'success') {
                        layer.msg(response.message);
                        loadFolderTree();
                        if (currentFolderId == folderId) {
                            currentFolderId = null;
                            $('#currentFolderName').text('根目录');
                            $('.folder-item').removeClass('active');
                            $('.folder-item[data-folder-id=""]').addClass('active');
                            loadFiles(null);
                        }
                    } else {
                        layer.msg('删除失败: ' + response.message);
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试');
                }
            });
            layer.close(index);
        });
    };

    window.assignFile = function(fileId) {
        layer.open({
            type: 1,
            title: '分配文件到课堂',
            content: $('#assignFileModal'),
            area: ['500px', '300px'],
            success: function() {
                $('input[name="file_id"]').val(fileId);
                form.render();
            }
        });
    };

    window.renameFile = function(fileId, currentName) {
        layer.prompt({
            title: '重命名文件',
            value: currentName
        }, function(newName, index) {
            if (newName.trim() === '') {
                layer.msg('文件名不能为空');
                return;
            }

            $.ajax({
                url: '/dashboard/rename_file',
                type: 'POST',
                data: JSON.stringify({
                    file_id: fileId,
                    new_name: newName.trim()
                }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.status === 'success') {
                        layer.msg(response.message);
                        loadFiles(currentFolderId);
                    } else {
                        layer.msg('重命名失败: ' + response.message);
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试');
                }
            });
            layer.close(index);
        });
    };

    window.deleteFile = function(fileId) {
        layer.confirm('确定要删除这个文件吗？', {icon: 3, title: '提示'}, function(index) {
            $.ajax({
                url: '/dashboard/delete_file/' + fileId,
                type: 'DELETE',
                success: function(response) {
                    if (response.status === 'success') {
                        layer.msg(response.message);
                        loadFiles(currentFolderId);
                    } else {
                        layer.msg('删除失败: ' + response.message);
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试');
                }
            });
            layer.close(index);
        });
    };

    // 批量删除文件
    function batchDeleteFiles() {
        layer.confirm('确定要删除选中的 ' + selectedFiles.length + ' 个文件吗？', {icon: 3, title: '提示'}, function(index) {
            $.ajax({
                url: '/dashboard/delete_files',
                type: 'DELETE',
                data: JSON.stringify({file_ids: selectedFiles}),
                contentType: 'application/json',
                success: function(response) {
                    if (response.status === 'success') {
                        layer.msg(response.message);
                        loadFiles(currentFolderId);
                        clearSelection();
                    } else {
                        layer.msg('删除失败: ' + response.message);
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试');
                }
            });
            layer.close(index);
        });
    }
    // 表单提交处理
    form.on('submit(createFolderForm)', function(data) {
        $.ajax({
            url: '/dashboard/create_folder',
            type: 'POST',
            data: JSON.stringify({
                folder_name: data.field.folder_name,
                parent_id: currentFolderId
            }),
            contentType: 'application/json',
            success: function(response) {
                if (response.status === 'success') {
                    layer.msg(response.message);
                    layer.closeAll();
                    loadFolderTree();
                } else {
                    layer.msg('创建失败: ' + response.message);
                }
            },
            error: function() {
                layer.msg('网络错误，请重试');
            }
        });
        return false;
    });

    form.on('submit(moveFileForm)', function(data) {
        var targetFolderId = data.field.target_folder_id;

        if (selectedFiles.length === 1) {
            // 单个文件移动
            $.ajax({
                url: '/dashboard/move_file',
                type: 'POST',
                data: JSON.stringify({
                    file_id: selectedFiles[0],
                    target_folder_id: targetFolderId
                }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.status === 'success') {
                        layer.msg(response.message);
                        layer.closeAll();
                        loadFiles(currentFolderId);
                        clearSelection();
                    } else {
                        layer.msg('移动失败: ' + response.message);
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试');
                }
            });
        } else {
            // 批量移动
            $.ajax({
                url: '/dashboard/move_files',
                type: 'POST',
                data: JSON.stringify({
                    file_ids: selectedFiles,
                    target_folder_id: targetFolderId
                }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.status === 'success') {
                        layer.msg(response.message);
                        layer.closeAll();
                        loadFiles(currentFolderId);
                        clearSelection();
                    } else {
                        layer.msg('移动失败: ' + response.message);
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试');
                }
            });
        }
        return false;
    });

    form.on('submit(assignFileForm)', function(data) {
        var fileId = selectedFiles.length > 0 ? selectedFiles[0] : null;
        if (!fileId) {
            layer.msg('请先选择文件');
            return false;
        }

        $.ajax({
            url: '/dashboard/assign_file_to_class',
            type: 'POST',
            data: JSON.stringify({
                file_id: fileId,
                schedule_id: data.field.schedule_id
            }),
            contentType: 'application/json',
            success: function(response) {
                if (response.status === 'success') {
                    layer.msg(response.message);
                    layer.closeAll();
                } else {
                    layer.msg('分配失败: ' + response.message);
                }
            },
            error: function() {
                layer.msg('网络错误，请重试');
            }
        });
        return false;
    });
});
</script>
{% endblock %}
