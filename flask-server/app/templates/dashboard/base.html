<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智慧课堂系统{% endblock %}</title>
    <link rel="stylesheet" href="/static/css/layui.css">
    {% block styles %}{% endblock %}
    <style>
        body {
            background: #f5f7fa;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }

        /* 侧边栏样式 */
        .layui-side {
            width: 220px;
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,0,0,0.1);
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            padding: 0 20px 10px;
            font-size: 14px;
            color: #999;
            font-weight: 500;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 10px;
        }

        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-menu li {
            margin: 0;
        }

        .nav-menu a {
            display: block;
            padding: 12px 20px;
            color: #666;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }

        .nav-menu a:hover {
            background: #f8f9fa;
            color: #1890ff;
            border-left-color: #1890ff;
        }

        .nav-menu a.active {
            background: #e6f7ff;
            color: #1890ff;
            border-left-color: #1890ff;
            font-weight: 500;
        }

        .nav-menu a i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* 主要内容区域 */
        .layui-body {
            left: 220px;
            background: #f5f7fa;
            padding: 24px;
        }

        /* 欢迎区域样式 */
        .welcome-section {
            margin-bottom: 24px;
        }

        .welcome-section h1 {
            font-size: 24px;
            color: #1f2329;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .welcome-section .date {
            color: #86909c;
            font-size: 14px;
        }

        /* 卡片容器样式 */
        .card-container {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            padding: 24px;
        }

        /* 操作按钮样式 */
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 16px;
            margin-top: 24px;
        }

        .action-button {
            background: #fff;
            border: 1px solid #e5e6eb;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .action-button:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .action-button .icon {
            width: 48px;
            height: 48px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f5ff;
            border-radius: 8px;
            color: #1890ff;
        }

        .action-button .text {
            font-size: 14px;
            color: #1f2329;
        }

        /* 课程卡片样式 */
        .course-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .course-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e6eb;
            transition: all 0.3s;
        }

        .course-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }

        .course-card .title {
            font-size: 16px;
            font-weight: 500;
            color: #1f2329;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .course-card .status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            background: #f0f5ff;
            color: #1890ff;
        }

        .course-card .status.ended {
            background: #f5f5f5;
            color: #86909c;
        }

        .course-info {
            color: #4e5969;
            font-size: 14px;
            line-height: 1.8;
        }

        .course-info p {
            margin: 4px 0;
            display: flex;
            align-items: center;
        }

        .course-info .label {
            color: #86909c;
            width: 70px;
        }

        .enter-btn {
            width: 100%;
            margin-top: 16px;
            height: 36px;
            line-height: 36px;
            background: #1890ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .enter-btn:hover {
            background: #40a9ff;
        }

        .enter-btn.disabled {
            background: #f5f5f5;
            color: #86909c;
            cursor: not-allowed;
        }

        /* 资源库导航栏样式 */
        .resource-tabs {
            background: #fff;
            margin: -24px -24px 24px;
            padding: 0 24px;
            border-bottom: 1px solid #f0f0f0;
        }

        .resource-tabs .layui-nav {
            background: transparent;
            padding: 0;
            height: 56px;
            line-height: 56px;
        }

        .resource-tabs .layui-nav-item {
            margin: 0 32px 0 0;
            background: transparent !important;
        }

        .resource-tabs .layui-nav-item a {
            color: #4e5969;
            font-size: 15px;
            padding: 0;
            margin: 0;
            position: relative;
            background: transparent !important;
            height: 56px;
            line-height: 56px;
        }

        .resource-tabs .layui-nav-item a:hover {
            background: transparent !important;
            color: #1890ff;
        }

        .resource-tabs .layui-this a {
            color: #1890ff !important;
            background: transparent !important;
        }

        .resource-tabs .layui-nav-item a:after {
            content: ''; 
            position: absolute;
            bottom: 0; 
            left: 0; 
            right: 0; 
            height: 0; 
            background: transparent;
            height: 2px;
            background: transparent;
            transition: all 0.3s;
        }

        .resource-tabs .layui-this a:after {
            background: #1890ff;
        }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div class="layui-header">
            <div class="layui-logo layui-hide-xs layui-bg-black">教学管理系统</div>
            <!-- 头部区域（可配合layui 已有的水平导航） -->
            <ul class="layui-nav layui-layout-left">
                <!-- 移动端显示 -->
                <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm" lay-header-event="menuLeft">
                    <i class="layui-icon layui-icon-spread-left"></i>
                </li>
            </ul>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item">
                    <a href="javascript:;" id="qr-scan-btn">
                        <i class="layui-icon layui-icon-camera"></i> 扫码登录客户端
                    </a>
                </li>
                <li class="layui-nav-item layui-hide layui-show-md-inline-block">
                    <a href="javascript:;">
                        {{ session.get('teacher_name')}}
                    </a>
                    <dl class="layui-nav-child">
                        <dd><a href="/logout">退出登录</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item" lay-header-event="menuRight" lay-unselect>
                    <a href="javascript:;">
                        <i class="layui-icon layui-icon-more-vertical"></i>
                    </a>
                </li>
            </ul>
        </div>

        <!-- 侧边栏 -->
        <div class="layui-side">
            <div class="sidebar-nav">
                <!-- 课程栏目 -->
                <div class="nav-section">
                    <div class="nav-section-title">课程</div>
                    <ul class="nav-menu">
                        <li>
                            <a href="/dashboard/" class="{% if request.endpoint == 'dashboard.index' %}active{% endif %}">
                                <i class="layui-icon layui-icon-home"></i>
                                课程
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 资源栏目 -->
                <div class="nav-section">
                    <div class="nav-section-title">资源</div>
                    <ul class="nav-menu">
                        <li>
                            <a href="/dashboard/exercises" class="{% if request.endpoint == 'dashboard.exercises' %}active{% endif %}">
                                <i class="layui-icon layui-icon-edit"></i>
                                习题
                            </a>
                        </li>
                        <li>
                            <a href="/dashboard/papers" class="{% if request.endpoint == 'dashboard.papers' %}active{% endif %}">
                                <i class="layui-icon layui-icon-form"></i>
                                试卷
                            </a>
                        </li>
                        <li>
                            <a href="/dashboard/materials" class="{% if request.endpoint == 'dashboard.materials' %}active{% endif %}">
                                <i class="layui-icon layui-icon-file"></i>
                                课件
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 内容主体区域 -->
        <div class="layui-body">
            <div class="welcome-section">
                <div class="date" id="dashboard-date"></div>
            </div>

            <div class="card-container">
                {% block content %}{% endblock %}
            </div>
        </div>

        <div class="layui-footer">
            <!-- 底部固定区域 -->
            Copyright © 2024 - 2025 智慧课堂系统. All Rights Reserved.
        </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/layui.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qr-scanner@1.4.2/qr-scanner.umd.min.js"></script>
    <script>
        layui.use(['element', 'layer'], function(){
            var element = layui.element;
            var layer = layui.layer;
            element.init();

            // 扫码登录客户端功能
            $('#qr-scan-btn').on('click', function(){
                // 检查是否支持摄像头
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    layer.alert('您的浏览器不支持摄像头功能，请使用现代浏览器。', {icon: 2});
                    return;
                }

                layer.open({
                    type: 1,
                    title: '扫描二维码',
                    area: ['500px', '600px'],
                    content: '<div id="qr-scanner-container" style="text-align: center; padding: 20px;">' +
                            '<video id="qr-video" width="400" height="300" style="border: 1px solid #ccc;"></video>' +
                            '<div id="qr-result" style="margin-top: 20px; color: #666;">正在启动摄像头...</div>' +
                            '<div style="margin-top: 20px;">' +
                            '<button class="layui-btn layui-btn-sm" id="stop-scan-btn">停止扫描</button>' +
                            '</div>' +
                            '</div>',
                    success: function(layero, index){
                        startClientQRScanner(index);
                    }
                });
            });

            // 启动客户端登录二维码扫描
            function startClientQRScanner(layerIndex) {
                const video = document.getElementById('qr-video');
                const resultDiv = document.getElementById('qr-result');
                let stream = null;
                let scanning = true;

                // 停止扫描按钮事件
                $('#stop-scan-btn').on('click', function(){
                    scanning = false;
                    if (stream) {
                        stream.getTracks().forEach(track => track.stop());
                    }
                    layer.close(layerIndex);
                });

                // 启动摄像头
                navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } })
                    .then(function(mediaStream) {
                        stream = mediaStream;
                        video.srcObject = stream;
                        video.play();
                        resultDiv.textContent = '请将客户端二维码对准摄像头';

                        // 开始扫描
                        scanClientQRCode();
                    })
                    .catch(function(err) {
                        resultDiv.textContent = '无法访问摄像头: ' + err.message;
                        console.error('摄像头访问失败:', err);
                    });

                function scanClientQRCode() {
                    if (!scanning) return;

                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');

                    if (video.readyState === video.HAVE_ENOUGH_DATA) {
                        canvas.width = video.videoWidth;
                        canvas.height = video.videoHeight;
                        context.drawImage(video, 0, 0, canvas.width, canvas.height);

                        // 使用QrScanner库解析二维码
                        if (typeof QrScanner !== 'undefined') {
                            QrScanner.scanImage(canvas)
                                .then(result => {
                                    if (result) {
                                        handleClientQRResult(result, layerIndex, stream);
                                        return;
                                    }
                                })
                                .catch(err => {
                                    // 继续扫描
                                });
                        }
                    }

                    if (scanning) {
                        requestAnimationFrame(scanClientQRCode);
                    }
                }
            }

            // 处理客户端登录二维码扫描结果
            function handleClientQRResult(qrData, layerIndex, stream) {
                try {
                    const data = JSON.parse(qrData);

                    if (data.type === 'client_login' && data.teacher_id && data.token) {
                        // 停止摄像头
                        if (stream) {
                            stream.getTracks().forEach(track => track.stop());
                        }
                        layer.close(layerIndex);

                        // 验证并登录客户端
                        const loading = layer.load(1, {shade: [0.3, '#000']});
                        $.ajax({
                            url: '/dashboard/verify_client_login',
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                teacher_id: data.teacher_id,
                                token: data.token,
                                timestamp: data.timestamp
                            },
                            success: function(res){
                                layer.close(loading);
                                if(res.status === 'success'){
                                    layer.msg('客户端登录验证成功！', {icon: 1});
                                    // 可以在这里添加其他逻辑，比如显示客户端连接状态
                                } else {
                                    layer.alert(res.message, {icon: 2});
                                }
                            },
                            error: function(){
                                layer.close(loading);
                                layer.alert('验证请求失败，请稍后重试。', {icon: 2});
                            }
                        });
                    } else {
                        layer.alert('这不是有效的客户端登录二维码', {icon: 2});
                    }
                } catch (e) {
                    layer.alert('二维码格式错误', {icon: 2});
                }
            }
        });

        (function(){
            var date = new Date();
            var weekArr = ['日','一','二','三','四','五','六'];
            var y = date.getFullYear();
            var m = (date.getMonth()+1).toString().padStart(2,'0');
            var d = date.getDate().toString().padStart(2,'0');
            var w = weekArr[date.getDay()];
            document.getElementById('dashboard-date').innerText = `${y}年${m}月${d}日 星期${w}`;
        })();
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
