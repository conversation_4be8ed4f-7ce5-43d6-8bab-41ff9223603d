{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 首页{% endblock %}

{% block styles %}
<style>
    /* 主卡片样式 */
    .layui-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    .layui-card-header {
        font-weight: bold;
        font-size: 16px;
    }
</style>
{% endblock %}

{% block content %}
<div class="layui-row layui-col-space20">
    <!-- 互动区域 -->
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-header">
                <i class="layui-icon layui-icon-website"></i> 教师控制台
            </div>
            <div class="layui-card-body">
                <p>课堂互动功能已集成到桌面客户端</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
layui.use(['element', 'layer'], function(){
    var layer = layui.layer;
    var $ = layui.jquery;
});
</script>
{% endblock %}
