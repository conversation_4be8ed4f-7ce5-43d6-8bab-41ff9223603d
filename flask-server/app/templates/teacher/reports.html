{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 课堂报告{% endblock %}

{% block styles %}
<style>
    .report-card {
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .report-main-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px 8px 0 0;
    }
    .report-title {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    .report-info {
        font-size: 14px;
        opacity: 0.9;
    }
    .report-content {
        padding: 20px;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    .stat-item {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        /* border-left managed by .accent-border-left */
    }
    .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #1e9fff;
        margin-bottom: 5px;
    }
    .stat-label {
        font-size: 14px;
        color: #666;
    }
    .section-title {
        font-size: 16px;
        font-weight: bold;
        margin: 20px 0 10px 0;
        color: #333;
        /* border-left managed by .accent-border-left */
        padding-left: 10px;
    }
    .accent-border-left {
        border-left: 4px solid #1e9fff;
    }
    .attendance-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .student-cell {
        padding: 10px;
        border-radius: 5px;
        text-align: center;
        font-size: 14px;
        color: white;
        position: relative;
    }
    .student-cell .status-dot {
        display: block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: white;
        position: absolute;
        top: 8px;
        right: 8px;
    }
    .student-present {
        background: #52c41a;
    }
    .student-absent {
        background: #ff4d4f;
    }
    .homework-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
    }
    .homework-title {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .homework-stats {
        font-size: 14px;
        color: #666;
    }
    .remarks-section {
        background: #fff9e6;
        border: 1px solid #ffe58f;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }
    .no-class-selected {
        text-align: center;
        padding: 60px;
        color: #999;
    }
    .no-class-selected .layui-icon-tips {
        font-size: 48px;
        color: #d9d9d9;
        margin-bottom: 15px;
    }
    .no-class-selected .no-class-prompt {
        font-size: 18px;
        margin-top: 10px;
        color: #777;
    }
    .no-class-selected .no-class-guidance {
        font-size: 14px;
        color: #aaa;
        margin-top: 8px;
    }
    .no-class-selected .no-class-guidance a {
        color: #1e9fff;
        text-decoration: none;
    }
    .no-class-selected .no-class-guidance a:hover {
        text-decoration: underline;
    }
    .generate-btn {
        margin-bottom: 20px;
    }
    .empty-data-message {
        color: #999;
        text-align: center;
        padding: 15px 0;
    }
</style>
{% endblock %}

{% block content %}
{% if current_class %}
<div class="layui-card">
    <div class="layui-card-header report-main-header">
        <span><i class="layui-icon layui-icon-chart"></i> 报告</span>
        <div>
            <button class="layui-btn layui-btn-sm" id="refreshBtn">
                <i class="layui-icon layui-icon-refresh"></i> 刷新数据
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-normal" id="exportBtn" data-id="{{ current_class.id }}">
                <i class="layui-icon layui-icon-download-circle"></i> 导出报告
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <div id="reportContainer">
            <div class="report-card">
                <div class="report-header">
                    <div class="report-title" id="reportTitle">课堂报告</div>
                    <div class="report-info" id="reportInfo">报告信息</div>
                </div>
                <div class="report-content">
                    <!-- 统计概览 -->
                    <div class="layui-row layui-col-space20">
                        <div class="layui-col-md6">
                            <div class="section-title accent-border-left">统计概览</div>
                            <div class="stats-grid">
                                <div class="stat-item accent-border-left">
                                    <div class="stat-number" id="attendanceCount">0</div>
                                    <div class="stat-label">签到人数</div>
                                </div>
                                <div class="stat-item accent-border-left">
                                    <div class="stat-number" id="totalStudents">0</div>
                                    <div class="stat-label">班级总人数</div>
                                </div>
                                <div class="stat-item accent-border-left" id="attendance-rate-chart" style="height: 150px;"></div>
                                <div class="stat-item accent-border-left" id="interaction-rate-chart" style="height: 150px;"></div>
                                <div class="stat-item accent-border-left">
                                    <div class="stat-number" id="danmakuCount">0</div>
                                    <div class="stat-label">聊天消息总数</div>
                                </div>
                            </div>
                            <div class="section-title accent-border-left">教师备注</div>
                            <div class="remarks-section">
                                <textarea id="remarksText" class="layui-textarea" placeholder="您可以在此为本次课堂添加备注..."></textarea>
                                <button class="layui-btn layui-btn-sm layui-btn-normal" id="saveRemarksBtn" style="margin-top: 10px;">保存备注</button>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="section-title accent-border-left">学生参与度总览</div>
                            <div id="engagement-chart" style="width: 100%; height: 400px; background: #f8f9fa; border-radius: 8px;"></div>
                        </div>
                    </div>

                    <!-- 考勤详情 -->
                    <div class="section-title accent-border-left">考勤详情</div>
                    <div class="attendance-grid" id="attendanceDetails">
                        <!-- 动态生成考勤详情 -->
                    </div>

                    <!-- 作业情况 -->
                    <div class="section-title accent-border-left">作业情况</div>
                    <div id="homeworkDetails">
                        <!-- 动态生成作业详情 -->
                    </div>

                    <!-- 互动答题分析 -->
                    <div class="section-title accent-border-left">互动答题分析</div>
                    <div id="quizDetails">
                        <!-- 动态生成互动答题分析 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="no-class-selected">
    <i class="layui-icon layui-icon-tips"></i>
    <p class="no-class-prompt">请先选择课堂</p>
    <p class="no-class-guidance">
        <a href="/teacher">点击这里</a> 选择要查看报告的课堂
    </p>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
layui.use(['layer', 'element'], function(){
    var layer = layui.layer;
    var element = layui.element;

    var refreshInterval = null;

    // 刷新数据按钮
    $('#refreshBtn').click(function(){
        loadRealtimeData();
    });

    // 导出报告按钮
    $('#exportBtn').click(function(){
        var courseId = $(this).data('id');
        window.open('/teacher/export_report/' + courseId, '_blank');
        layer.msg('报告导出中...');
    });

    // 加载实时数据
    function loadRealtimeData() {
        var courseId = '{{ current_class.id if current_class else "" }}';
        if (!courseId) return;

        $.ajax({
            url: '/teacher/get_realtime_data/' + courseId,
            type: 'GET',
            success: function(res){
                if(res.status === 'success'){
                    displayReport(res.data);
                } else {
                    layer.msg('获取数据失败: ' + res.message);
                }
            },
            error: function(xhr){
                layer.msg('请求失败，请检查网络连接');
            }
        });
    }

    // 显示报告数据
    function displayReport(data) {
        // 更新报告标题和信息
        $('#reportTitle').text(data.course_name + ' - 课堂报告');
        $('#reportInfo').text('更新时间：' + data.report_date + ' | 教室：' + data.classroom_name + ' | 班级：' + data.class_name);

        // 更新统计数据
        $('#attendanceCount').text(data.attendance_count);
        $('#totalStudents').text(data.total_students);
        $('#danmakuCount').text(data.danmaku_count);

        // 渲染仪表盘
        var attendanceRate = data.total_students > 0 ? parseFloat(((data.attendance_count / data.total_students) * 100).toFixed(1)) : 0;
        var interactionRate = data.total_students > 0 ? parseFloat(((data.active_students / data.total_students) * 100).toFixed(1)) : 0;
        renderGaugeChart(attendanceRateChart, '出勤率', attendanceRate);
        renderGaugeChart(interactionRateChart, '互动参与率', interactionRate);

        // 更新备注
        $('#remarksText').val(data.remarks || '');

        // 更新考勤详情
        var attendanceHtml = '';
        if(data.attendance_details && data.attendance_details.length > 0) {
            data.attendance_details.forEach(function(student) {
                var cellClass = student.status === 'present' ? 'student-present' : 'student-absent';
                attendanceHtml += `<div class="student-cell ${cellClass}">`;
                attendanceHtml += student.name;
                attendanceHtml += '<span class="status-dot"></span>';
                attendanceHtml += '</div>';
            });
        } else {
            attendanceHtml = '<p class="empty-data-message">暂无考勤数据</p>';
        }
        $('#attendanceDetails').html(attendanceHtml);

        // 渲染参与度排行榜
        renderEngagementChart(data.engagement_ranking);

        // 更新作业详情
        var homeworkHtml = '';
        if(data.homework_details && data.homework_details.length > 0) {
            data.homework_details.forEach(function(homework) {
                homeworkHtml += '<div class="homework-item">';
                homeworkHtml += '<div class="homework-title">' + homework.title + '</div>';
                homeworkHtml += '<div class="homework-stats">';
                homeworkHtml += '题目数量：' + homework.question_count + '题 | ';
                homeworkHtml += '提交人数：' + homework.submitted_count + '/' + homework.total_students + '人';
                if(homework.total_students > 0) {
                    homeworkHtml += ' | 提交率：' + ((homework.submitted_count / homework.total_students) * 100).toFixed(1) + '%';
                }
                homeworkHtml += '</div>';
                homeworkHtml += '</div>';
            });
        } else {
            homeworkHtml = '<p class="empty-data-message">暂无作业数据</p>';
        }
        $('#homeworkDetails').html(homeworkHtml);

        // 更新互动答题分析
        var quizHtml = '';
        if(data.quiz_details && data.quiz_details.length > 0) {
            data.quiz_details.forEach(function(quiz) {
                var participationRate = quiz.total_students > 0 ? ((quiz.participants_count / quiz.total_students) * 100).toFixed(1) : 0;
                quizHtml += '<div class="homework-item">'; // 复用作业item样式
                quizHtml += '<div class="homework-title">题目：' + quiz.question_text + '</div>';
                quizHtml += '<div class="homework-stats">';
                quizHtml += '参与人数：' + quiz.participants_count + '/' + quiz.total_students + '人 | ';
                quizHtml += '参与率：' + participationRate + '% | ';
                quizHtml += '正确率：' + quiz.accuracy + '% (' + quiz.correct_count + '人正确)';
                quizHtml += '</div>';
                quizHtml += '</div>';
            });
        } else {
            quizHtml = '<p class="empty-data-message">本堂课未进行互动答题</p>';
        }
        $('#quizDetails').html(quizHtml);
    }

    // 启动定时刷新
    function startAutoRefresh() {
        // 每30秒自动刷新一次数据
        refreshInterval = setInterval(function() {
            loadRealtimeData();
        }, 30000);
    }

    // 停止定时刷新
    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    // 页面加载时初始化
    {% if current_class %}
    var engagementChart = echarts.init(document.getElementById('engagement-chart'));
    var attendanceRateChart = echarts.init(document.getElementById('attendance-rate-chart'));
    var interactionRateChart = echarts.init(document.getElementById('interaction-rate-chart'));

    // 立即加载数据
    loadRealtimeData();
    // 启动定时刷新
    startAutoRefresh();

    // 保存备注
    $('#saveRemarksBtn').click(function(){
        var remarks = $('#remarksText').val();
        var courseId = '{{ current_class.id }}';
        $.ajax({
            url: '/teacher/reports/' + courseId + '/remarks',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ remarks: remarks }),
            success: function(res) {
                if (res.status === 'success') {
                    layer.msg('备注保存成功', {icon: 1});
                } else {
                    layer.msg('保存失败: ' + res.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('请求失败', {icon: 2});
            }
        });
    });

    // 渲染仪表盘函数
    function renderGaugeChart(chartInstance, title, value) {
        var option = {
            series: [
                {
                    type: 'gauge',
                    center: ['50%', '60%'],
                    startAngle: 200,
                    endAngle: -20,
                    min: 0,
                    max: 100,
                    splitNumber: 10,
                    itemStyle: {
                        color: '#1E9FFF'
                    },
                    progress: {
                        show: true,
                        width: 12
                    },
                    pointer: {
                        show: false,
                    },
                    axisLine: {
                        lineStyle: {
                            width: 12
                        }
                    },
                    axisTick: {
                        distance: -20,
                        splitNumber: 5,
                        lineStyle: {
                            width: 1,
                            color: '#999'
                        }
                    },
                    splitLine: {
                        distance: -22,
                        length: 8,
                        lineStyle: {
                            width: 2,
                            color: '#999'
                        }
                    },
                    axisLabel: {
                        distance: -15,
                        color: '#999',
                        fontSize: 10
                    },
                    anchor: {
                        show: false
                    },
                    title: {
                        show: true,
                        offsetCenter: [0, '90%'],
                        fontSize: 14,
                        color: '#666'
                    },
                    detail: {
                        valueAnimation: true,
                        width: '60%',
                        lineHeight: 40,
                        borderRadius: 8,
                        offsetCenter: [0, '-15%'],
                        fontSize: 24,
                        fontWeight: 'bolder',
                        formatter: '{value}%',
                        color: 'auto'
                    },
                    data: [
                        {
                            value: value,
                            name: title
                        }
                    ]
                }
            ]
        };
        chartInstance.setOption(option);
    }

    // 渲染图表函数
    function renderEngagementChart(details) {
        if (!details || details.length === 0) {
            engagementChart.clear();
            engagementChart.setOption({
                title: { text: '暂无学生参与度数据', left: 'center', top: 'center', textStyle: { color: '#999' } }
            });
            return;
        }

        // 后端已排序，前端直接使用
        var studentNames = details.map(item => item.student_name);
        var engagementScores = details.map(item => item.engagement_score);

        var option = {
            title: {
                text: '学生参与度总览',
                left: 'center',
                textStyle: { color: '#333', fontWeight: 'bold' }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                boundaryGap: [0, 0.01]
            },
            yAxis: {
                type: 'category',
                data: studentNames.reverse(), // 反转使最高在顶��
                axisLabel: {
                    interval: 0,
                    rotate: 0
                }
            },
            series: [
                {
                    name: '综合参与度得分',
                    type: 'bar',
                    data: engagementScores.reverse(), // 反转数据
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                            { offset: 0, color: '#83bff6' },
                            { offset: 0.5, color: '#188df0' },
                            { offset: 1, color: '#188df0' }
                        ])
                    },
                    emphasis: {
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                                { offset: 0, color: '#2378f7' },
                                { offset: 0.7, color: '#2378f7' },
                                { offset: 1, color: '#83bff6' }
                            ])
                        }
                    }
                }
            ]
        };
        engagementChart.setOption(option);
    }

    // 监听窗口大小变化，自适应图表
    $(window).on('resize', function(){
        engagementChart.resize();
        attendanceRateChart.resize();
        interactionRateChart.resize();
    });

    {% endif %}

    // 页面离开时清理定时器
    $(window).on('beforeunload', function() {
        stopAutoRefresh();
    });
});
</script>
{% endblock %}
