<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理系统</title>
    <link rel="stylesheet" href="/static/css/layui.css">
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <!-- 头部导航 -->
        <div class="layui-header">
            <div class="layui-logo">管理系统</div>
            <ul class="layui-nav layui-layout-left">
                <li class="layui-nav-item layui-this"><a href="javascript:;" onclick="showTab('student')">学生管理</a></li>
                <li class="layui-nav-item"><a href="javascript:;" onclick="showTab('course')">课程管理</a></li>
                <li class="layui-nav-item"><a href="javascript:;" onclick="showTab('schedule')">课程安排</a></li>
            </ul>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item">
                    <a href="javascript:;">管理员</a>
                </li>
            </ul>
        </div>

        <!-- 侧边栏 -->
        <div class="layui-side layui-bg-black">
            <div class="layui-side-scroll">
                <ul class="layui-nav layui-nav-tree" lay-filter="test">
                    <li class="layui-nav-item layui-nav-itemed">
                        <a href="javascript:;">学生管理</a>
                        <dl class="layui-nav-child">
                            <dd class="layui-this"><a href="javascript:;" onclick="showTab('student')">添加学生</a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item">
                        <a href="javascript:;">课程管理</a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" onclick="showTab('course')">课程管理</a></dd>
                            <dd><a href="javascript:;" onclick="showTab('schedule')">课程安排</a></dd>
                        </dl>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="layui-body" style="left: 200px;">
            <div style="padding: 20px;">
                <!-- 学生管理标签页 -->
                <div id="student-tab" class="tab-content">
                    <div class="layui-card">
                        <div class="layui-card-header">添加学生</div>
                        <div class="layui-card-body">
                            <form id="addStudentForm" class="layui-form">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">学号</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="student_id" required lay-verify="required" placeholder="请输入学生学号" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">姓名</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="name" required lay-verify="required" placeholder="请输入学生姓名" autocomplete="off" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">性别</label>
                                    <div class="layui-input-block">
                                        <input type="radio" name="gender" value="男" title="男" checked>
                                        <input type="radio" name="gender" value="女" title="女">
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label">班级</label>
                                    <div class="layui-input-block">
                                        <select name="class_id" lay-verify="required" lay-search="">
                                            <option value="">请选择班级</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn" lay-submit="" lay-filter="addStudent">添加学生</button>
                                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 课程管理标签页 -->
                <div id="course-tab" class="tab-content" style="display: none;">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            课程管理
                            <button class="layui-btn layui-btn-sm" style="float: right;" onclick="showAddCourseForm()">添加课程</button>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-filter="courseTable">
                                <thead>
                                    <tr>
                                        <th>课程名称</th>
                                        <th>课程代码</th>
                                        <th>描述</th>
                                        <th>课程安排数</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="courseTableBody">
                                    <!-- 动态加载课程数据 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 课程安排标签页 -->
                <div id="schedule-tab" class="tab-content" style="display: none;">
                    <div class="layui-card">
                        <div class="layui-card-header">添加课程安排</div>
                        <div class="layui-card-body">
                            <form id="addScheduleForm" class="layui-form">
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">课程</label>
                                        <div class="layui-input-inline">
                                            <select name="course_id" lay-verify="required" lay-search="">
                                                <option value="">请选择课程</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">教师</label>
                                        <div class="layui-input-inline">
                                            <select name="teacher_id" lay-verify="required" lay-search="">
                                                <option value="">请选择教师</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">教室</label>
                                        <div class="layui-input-inline">
                                            <select name="classroom_id" lay-verify="required" lay-search="">
                                                <option value="">请选择教室</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">班级</label>
                                        <div class="layui-input-inline">
                                            <select name="class_id" lay-verify="required" lay-search="">
                                                <option value="">请选择班级</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">星期</label>
                                        <div class="layui-input-inline">
                                            <select name="day_of_week" lay-verify="required">
                                                <option value="">请选择星期</option>
                                                <option value="Monday">星期一</option>
                                                <option value="Tuesday">星期二</option>
                                                <option value="Wednesday">星期三</option>
                                                <option value="Thursday">星期四</option>
                                                <option value="Friday">星期五</option>
                                                <option value="Saturday">星期六</option>
                                                <option value="Sunday">星期日</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">开始时间</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="start_time" placeholder="HH:mm" pattern="\d{2}:\d{2}" autocomplete="off" class="layui-input" lay-verify="required">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">结束时间</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="end_time" placeholder="HH:mm" pattern="\d{2}:\d{2}" autocomplete="off" class="layui-input" lay-verify="required">
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item layui-form-text">
                                    <label class="layui-form-label">描述</label>
                                    <div class="layui-input-block">
                                        <textarea name="description" placeholder="请输入课程描述" class="layui-textarea"></textarea>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn" lay-submit="" lay-filter="addSchedule">添加课程安排</button>
                                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 课程安排列表 -->
                    <div class="layui-card" style="margin-top: 20px;">
                        <div class="layui-card-header">课程安排列表</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-filter="scheduleTable">
                                <thead>
                                    <tr>
                                        <th>课程名称</th>
                                        <th>课程代码</th>
                                        <th>教师</th>
                                        <th>教室</th>
                                        <th>班级</th>
                                        <th>星期</th>
                                        <th>时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="scheduleTableBody">
                                    <!-- 动态加载课程安排数据 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 脚本引用 -->
        <script src="/static/js/layui.js"></script>
        <script>
        layui.use(['element', 'layer', 'form'], function(){
            var element = layui.element;
            var layer = layui.layer;
            var form = layui.form;
            var $ = layui.$;

            // 初始化导航菜单
            element.init();

            // 页面加载时初始化数据
            loadClasses();
            loadCourses();
            loadTeachers();
            loadClassrooms();
            loadSchedules();
            loadCoursesWithDetails();

            // 学生表单提交
            form.on('submit(addStudent)', function(data){
                var studentData = {
                    student_id: data.field.student_id,
                    name: data.field.name,
                    gender: data.field.gender,
                    class_id: data.field.class_id
                };

                $.ajax({
                    url: '/admin/add_student',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(studentData),
                    success: function(res) {
                        if (res.status === 'success') {
                            layer.msg('学生添加成功');
                            $('#addStudentForm')[0].reset();
                            form.render();
                        } else {
                            layer.msg('学生添加失败: ' + res.message);
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请检查网络连接');
                    }
                });
                return false;
            });

            // 课程安排表单提交
            form.on('submit(addSchedule)', function(data){
                var scheduleData = {
                    course_id: data.field.course_id,
                    teacher_id: data.field.teacher_id,
                    classroom_id: data.field.classroom_id,
                    class_id: data.field.class_id,
                    day_of_week: data.field.day_of_week,
                    start_time: data.field.start_time,
                    end_time: data.field.end_time,
                    description: data.field.description
                };

                $.ajax({
                    url: '/admin/add_course_schedule',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(scheduleData),
                    success: function(res) {
                        if (res.status === 'success') {
                            layer.msg('课程安排添加成功');
                            $('#addScheduleForm')[0].reset();
                            form.render();
                            loadSchedules(); // 刷新课程安排列表
                        } else {
                            layer.msg('课程安排添加失败: ' + res.message);
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请检查网络连接');
                    }
                });
                return false;
            });

            // 加载班级列表
            function loadClasses() {
                $.ajax({
                    url: '/admin/get_classes',
                    type: 'GET',
                    success: function(res) {
                        if (res.status === 'success') {
                            var classSelects = $('select[name="class_id"]');
                            classSelects.empty().append('<option value="">请选择班级</option>');
                            res.classes.forEach(function(cls) {
                                classSelects.append('<option value="' + cls.id + '">' + cls.name + ' (' + cls.grade + ')</option>');
                            });
                            form.render('select');
                        }
                    }
                });
            }

            // 加载课程列表
            function loadCourses() {
                $.ajax({
                    url: '/admin/get_courses',
                    type: 'GET',
                    success: function(res) {
                        if (res.status === 'success') {
                            var courseSelect = $('select[name="course_id"]');
                            courseSelect.empty().append('<option value="">请选择课程</option>');
                            res.courses.forEach(function(course) {
                                courseSelect.append('<option value="' + course.id + '">' + course.name + ' (' + course.code + ')</option>');
                            });
                            form.render('select');
                        }
                    }
                });
            }

            // 加载教师列表
            function loadTeachers() {
                $.ajax({
                    url: '/admin/get_teachers',
                    type: 'GET',
                    success: function(res) {
                        if (res.status === 'success') {
                            var teacherSelect = $('select[name="teacher_id"]');
                            teacherSelect.empty().append('<option value="">请选择教师</option>');
                            res.teachers.forEach(function(teacher) {
                                teacherSelect.append('<option value="' + teacher.teacher_id + '">' + teacher.name + '</option>');
                            });
                            form.render('select');
                        }
                    }
                });
            }

            // 加载教室列表
            function loadClassrooms() {
                $.ajax({
                    url: '/admin/get_classrooms',
                    type: 'GET',
                    success: function(res) {
                        if (res.status === 'success') {
                            var classroomSelect = $('select[name="classroom_id"]');
                            classroomSelect.empty().append('<option value="">请选择教室</option>');
                            res.classrooms.forEach(function(classroom) {
                                classroomSelect.append('<option value="' + classroom.id + '">' + classroom.name + ' (容量:' + classroom.capacity + ')</option>');
                            });
                            form.render('select');
                        }
                    }
                });
            }

            // 加载课程安排列表
            function loadSchedules() {
                $.ajax({
                    url: '/admin/get_course_schedules',
                    type: 'GET',
                    success: function(res) {
                        if (res.status === 'success') {
                            var tbody = $('#scheduleTableBody');
                            tbody.empty();

                            res.schedules.forEach(function(schedule) {
                                var dayMap = {
                                    'Monday': '星期一',
                                    'Tuesday': '星期二',
                                    'Wednesday': '星期三',
                                    'Thursday': '星期四',
                                    'Friday': '星期五',
                                    'Saturday': '星期六',
                                    'Sunday': '星期日'
                                };

                                var statusMap = {
                                    'scheduled': '已安排',
                                    'in_progress': '进行中',
                                    'completed': '已完成',
                                    'cancelled': '已取消'
                                };

                                var row = '<tr>' +
                                    '<td>' + schedule.course_name + '</td>' +
                                    '<td>' + schedule.course_code + '</td>' +
                                    '<td>' + schedule.teacher_name + '</td>' +
                                    '<td>' + schedule.classroom_name + '</td>' +
                                    '<td>' + schedule.class_name + '</td>' +
                                    '<td>' + dayMap[schedule.day_of_week] + '</td>' +
                                    '<td>' + schedule.start_time + ' - ' + schedule.end_time + '</td>' +
                                    '<td>' + statusMap[schedule.status] + '</td>' +
                                    '<td><button class="layui-btn layui-btn-danger layui-btn-xs" onclick="deleteSchedule(\'' + schedule.id + '\')">删除</button></td>' +
                                    '</tr>';
                                tbody.append(row);
                            });
                        }
                    }
                });
            }
        });

        // 标签页切换函数（全局作用域）
        window.showTab = function(tabName) {
            layui.use(['jquery'], function(){
                var $ = layui.$;

                // 隐藏所有标签页内容
                $('.tab-content').hide();

                // 显示指定的标签页
                $('#' + tabName + '-tab').show();

                // 更新头部导航状态
                $('.layui-nav.layui-layout-left .layui-nav-item').removeClass('layui-this');
                $('.layui-nav.layui-layout-left a[onclick="showTab(\'' + tabName + '\')"]').parent().addClass('layui-this');

                // 更新侧边栏导航状态
                $('.layui-nav-tree .layui-nav-child dd').removeClass('layui-this');
                $('.layui-nav-tree a[onclick="showTab(\'' + tabName + '\')"]').parent().addClass('layui-this');
            });
        };

        // 查看签到情况（全局作用域）
        window.viewAttendance = function(scheduleId) {
            layui.use(['layer'], function(){
                var layer = layui.layer;
                var $ = layui.$;

                $.ajax({
                    url: '/admin/get_schedule_attendance/' + scheduleId,
                    type: 'GET',
                    success: function(res) {
                        if (res.status === 'success') {
                            var content = '<table class="layui-table">' +
                                '<thead><tr><th>学号</th><th>姓名</th><th>性别</th><th>签到时间</th><th>状态</th></tr></thead>' +
                                '<tbody>';

                            res.attendance.forEach(function(record) {
                                var statusText = record.status === 'present' ? '已签到' : '未签到';
                                var signinTime = record.signin_time || '未签到';
                                content += '<tr>' +
                                    '<td>' + record.student_id + '</td>' +
                                    '<td>' + record.student_name + '</td>' +
                                    '<td>' + record.gender + '</td>' +
                                    '<td>' + signinTime + '</td>' +
                                    '<td>' + statusText + '</td>' +
                                    '</tr>';
                            });

                            content += '</tbody></table>';

                            layer.open({
                                type: 1,
                                title: '学生签到情况',
                                content: content,
                                area: ['800px', '600px'],
                                maxmin: true
                            });
                        } else {
                            layer.msg('获取签到信息失败: ' + res.message);
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请检查网络连接');
                    }
                });
            });
        };

        // 课程管理相关函数
        function loadCoursesWithDetails() {
            layui.use(['layer'], function(){
                var $ = layui.$;

                $.ajax({
                    url: '/admin/get_courses_with_details',
                    type: 'GET',
                    success: function(res) {
                        if (res.status === 'success') {
                            var tbody = $('#courseTableBody');
                            tbody.empty();

                            res.courses.forEach(function(course) {
                                var createTime = new Date(course.created_at).toLocaleDateString();
                                var description = course.description || '无描述';
                                if (description.length > 30) {
                                    description = description.substring(0, 30) + '...';
                                }

                                var row = '<tr>' +
                                    '<td>' + course.name + '</td>' +
                                    '<td>' + course.code + '</td>' +
                                    '<td>' + description + '</td>' +
                                    '<td>' + course.schedule_count + '</td>' +
                                    '<td>' + createTime + '</td>' +
                                    '<td>' +
                                        '<button class="layui-btn layui-btn-xs" onclick="editCourse(\'' + course.id + '\')">编辑</button> ' +
                                        '<button class="layui-btn layui-btn-danger layui-btn-xs" onclick="deleteCourse(\'' + course.id + '\')">删除</button>' +
                                    '</td>' +
                                    '</tr>';
                                tbody.append(row);
                            });
                        }
                    }
                });
            });
        }

        window.showAddCourseForm = function() {
            layui.use(['layer', 'form'], function(){
                var layer = layui.layer;
                var form = layui.form;
                var $ = layui.$;

                var content = '<form id="courseForm" class="layui-form" style="padding: 20px;" onsubmit="return false;">' +
                    '<div class="layui-form-item">' +
                        '<label class="layui-form-label">课程名称</label>' +
                        '<div class="layui-input-block">' +
                            '<input type="text" name="name" required lay-verify="required" placeholder="请输入课程名称" autocomplete="off" class="layui-input">' +
                        '</div>' +
                    '</div>' +
                    '<div class="layui-form-item">' +
                        '<label class="layui-form-label">课程代码</label>' +
                        '<div class="layui-input-block">' +
                            '<input type="text" name="code" required lay-verify="required" placeholder="请输入课程代码" autocomplete="off" class="layui-input">' +
                        '</div>' +
                    '</div>' +
                    '<div class="layui-form-item layui-form-text">' +
                        '<label class="layui-form-label">课程描述</label>' +
                        '<div class="layui-input-block">' +
                            '<textarea name="description" placeholder="请输入课程描述" class="layui-textarea"></textarea>' +
                        '</div>' +
                    '</div>' +
                    '<div class="layui-form-item">' +
                        '<div class="layui-input-block">' +
                            '<button id="saveCourseBtn" class="layui-btn">保存</button>' +
                            '<button type="reset" class="layui-btn layui-btn-primary">重置</button>' +
                        '</div>' +
                    '</div>' +
                '</form>';

                layer.open({
                    type: 1,
                    title: '添加课程',
                    content: content,
                    area: ['500px', '400px'],
                    success: function(layero, index) {
                        form.render(); // 渲染表单元素

                        // 使用原生 jQuery click 事件代替 lay-submit
                        $('#saveCourseBtn').on('click', function() {
                            var courseData = {
                                name: $('#courseForm input[name="name"]').val(),
                                code: $('#courseForm input[name="code"]').val(),
                                description: $('#courseForm textarea[name="description"]').val()
                            };

                            if (!courseData.name || !courseData.code) {
                                layer.msg('课程名称和代码不能为空');
                                return;
                            }

                            $.ajax({
                                url: '/admin/add_course',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify(courseData),
                                success: function(res) {
                                    if (res.status === 'success') {
                                        layer.msg('课程添加成功');
                                        layer.close(index);
                                        loadCoursesWithDetails();
                                        loadCourses();
                                    } else {
                                        layer.msg('课程添加失败: ' + res.message);
                                    }
                                },
                                error: function() {
                                    layer.msg('请求失败，请检查网络连接');
                                }
                            });
                        });
                    }
                });
            });
        };

        window.editCourse = function(courseId) {
            layui.use(['layer', 'form'], function(){
                var layer = layui.layer;
                var form = layui.form;
                var $ = layui.$;

                // 先获取课程信息
                $.ajax({
                    url: '/admin/get_course/' + courseId,
                    type: 'GET',
                    success: function(res) {
                        if (res.status === 'success') {
                            var course = res.course;

                            var content = '<form id="editCourseForm" class="layui-form" style="padding: 20px;">' +
                                '<div class="layui-form-item">' +
                                    '<label class="layui-form-label">课程名称</label>' +
                                    '<div class="layui-input-block">' +
                                        '<input type="text" name="name" value="' + course.name + '" required lay-verify="required" placeholder="请输入课程名称" autocomplete="off" class="layui-input">' +
                                    '</div>' +
                                '</div>' +
                                '<div class="layui-form-item">' +
                                    '<label class="layui-form-label">课程代码</label>' +
                                    '<div class="layui-input-block">' +
                                        '<input type="text" name="code" value="' + course.code + '" required lay-verify="required" placeholder="请输入课程代码" autocomplete="off" class="layui-input">' +
                                    '</div>' +
                                '</div>' +
                                '<div class="layui-form-item layui-form-text">' +
                                    '<label class="layui-form-label">课程描述</label>' +
                                    '<div class="layui-input-block">' +
                                        '<textarea name="description" placeholder="请输入课程描述" class="layui-textarea">' + (course.description || '') + '</textarea>' +
                                    '</div>' +
                                '</div>' +
                                '<div class="layui-form-item">' +
                                    '<div class="layui-input-block">' +
                                        '<button class="layui-btn" lay-submit lay-filter="updateCourse">保存</button>' +
                                        '<button type="reset" class="layui-btn layui-btn-primary">重置</button>' +
                                    '</div>' +
                                '</div>' +
                            '</form>';

                            layer.open({
                                type: 1,
                                title: '编辑课程',
                                content: content,
                                area: ['500px', '400px'],
                                success: function(layero, index) {
                                    form.render();

                                    form.on('submit(updateCourse)', function(data){
                                        var courseData = {
                                            name: data.field.name,
                                            code: data.field.code,
                                            description: data.field.description
                                        };

                                        $.ajax({
                                            url: '/admin/update_course/' + courseId,
                                            type: 'PUT',
                                            contentType: 'application/json',
                                            data: JSON.stringify(courseData),
                                            success: function(res) {
                                                if (res.status === 'success') {
                                                    layer.msg('课程更新成功');
                                                    layer.close(index);
                                                    loadCoursesWithDetails();
                                                    loadCourses(); // 刷新课程下拉列表
                                                } else {
                                                    layer.msg('课程更新失败: ' + res.message);
                                                }
                                            },
                                            error: function() {
                                                layer.msg('请求失败，请检查网络连接');
                                            }
                                        });
                                        return false;
                                    });
                                }
                            });
                        } else {
                            layer.msg('获取课程信息失败: ' + res.message);
                        }
                    },
                    error: function() {
                        layer.msg('请求失败，请检查网络连接');
                    }
                });
            });
        };

        window.deleteCourse = function(courseId) {
            layui.use(['layer'], function(){
                var layer = layui.layer;
                var $ = layui.$;

                layer.confirm('确定要删除这个课程吗？删除后不可恢复！', {
                    btn: ['确定', '取消']
                }, function(index) {
                    $.ajax({
                        url: '/admin/delete_course/' + courseId,
                        type: 'DELETE',
                        success: function(res) {
                            if (res.status === 'success') {
                                layer.msg('课程删除成功');
                                layer.close(index);
                                loadCoursesWithDetails();
                                loadCourses(); // 刷新课程下拉列表
                            } else {
                                layer.msg('课程删除失败: ' + res.message);
                            }
                        },
                        error: function() {
                            layer.msg('请求失败，请检查网络连接');
                        }
                    });
                });
            });
        };

        window.deleteSchedule = function(scheduleId) {
            layui.use(['layer'], function(){
                var layer = layui.layer;
                var $ = layui.$;

                layer.confirm('确定要删除这个课程安排吗？', {
                    btn: ['确定', '取消']
                }, function(index) {
                    $.ajax({
                        url: '/admin/delete_course_schedule/' + scheduleId,
                        type: 'DELETE',
                        success: function(res) {
                            if (res.status === 'success') {
                                layer.msg('课程安排删除成功');
                                loadSchedules(); // 刷新课程安排列表
                            } else {
                                layer.msg('删除失败: ' + res.message);
                            }
                        },
                        error: function() {
                            layer.msg('请求失败，请检查网络连接');
                        }
                    });
                });
            });
        };
        </script>

    </div>
</body>
</html>