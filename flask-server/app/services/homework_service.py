"""
作业管理服务
"""
import json
import uuid
from datetime import datetime
from app.models.database import get_db

class HomeworkService:
    """处理与作业相关的业务逻辑"""

    def get_homework_list_for_teacher(self, teacher_id, course_schedule_id):
        """
        获取指定课程下某个老师的作业列表
        """
        try:
            conn = get_db()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT h.id, h.title, h.description, h.created_at, h.status,
                       h.deadline, h.question_count, h.submitted_count, h.total_students
                FROM homework h
                WHERE h.course_schedule_id = ? AND h.teacher_id = ?
                ORDER BY h.created_at DESC
            """, (course_schedule_id, teacher_id))
            
            homework_list = cursor.fetchall()
            conn.close()
            
            return True, "获取成功", [dict(row) for row in homework_list]
        except Exception as e:
            return False, f"获取作业列表失败: {str(e)}", None

    # 后续将添加更多方法，如 create_homework, publish_homework 等

    def create_homework_from_paper(self, teacher_id, course_id, paper_id, title, deadline, description):
        """
        从资源库导入试卷作为作业
        """
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 1. 验证课程权限
            cursor.execute("SELECT class_id FROM course_schedules WHERE id = ? AND teacher_id = ?", (course_id, teacher_id))
            schedule = cursor.fetchone()
            if not schedule:
                conn.close()
                return False, "无权限访问该课程", None

            # 2. 获取原试卷数据
            cursor.execute("SELECT data FROM papers WHERE id = ? AND teacher_id = ?", (paper_id, teacher_id))
            paper = cursor.fetchone()
            if not paper:
                conn.close()
                return False, "试卷不存在或无权限", None

            # 3. 解析试卷数据获取题目数量
            paper_data = json.loads(paper['data'])
            question_count = len(paper_data.get('questions', []))

            # 4. 获取班级学生总数
            cursor.execute("SELECT COUNT(*) as total FROM students WHERE class_id = ?", (schedule['class_id'],))
            total_students = cursor.fetchone()['total']

            # 5. 创建新的作业
            homework_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()
            
            # 确保截止时间格式正确
            if deadline:
                deadline = f"{deadline} 23:59:59"

            cursor.execute("""
                INSERT INTO homework (id, title, description, teacher_id, course_schedule_id, created_at, data,
                                    status, deadline, question_count, submitted_count, total_students)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (homework_id, title, description, teacher_id, course_id, current_time,
                  paper['data'], 'draft', deadline, question_count, 0, total_students))

            conn.commit()
            conn.close()

            return True, "作业创建成功", homework_id
        except Exception as e:
            return False, f"创建作业失败: {str(e)}", None

    def create_interactive_session(self, teacher_id, course_id, paper_id, title):
        """
        从资源库导入试卷作为互动答题.
        """
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 1. 验证课程权限
            cursor.execute("SELECT class_id FROM course_schedules WHERE id = ? AND teacher_id = ?", (course_id, teacher_id))
            schedule = cursor.fetchone()
            if not schedule:
                conn.close()
                return False, "无权限访问该课程或课程不存在", None

            # 2. 获取原试卷数据
            cursor.execute("SELECT data FROM papers WHERE id = ? AND teacher_id = ?", (paper_id, teacher_id))
            paper = cursor.fetchone()
            if not paper:
                conn.close()
                return False, "试卷不存在或无权限", None

            # 3. 解析试卷数据获取题目数量
            paper_data = json.loads(paper['data'])
            # The data can be a list of question IDs or a dict with a 'questions' key
            if isinstance(paper_data, dict):
                questions = paper_data.get('questions', [])
            elif isinstance(paper_data, list):
                questions = paper_data
            else:
                questions = []
            question_count = len(questions)

            # 4. 获取班级学生总数
            cursor.execute("SELECT COUNT(*) as total FROM students WHERE class_id = ?", (schedule['class_id'],))
            total_students = cursor.fetchone()['total']

            # 5. 创建新的互动答题 (类型为 'interactive', 状态为 'published')
            homework_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO homework (id, title, teacher_id, course_schedule_id, created_at, data,
                                    status, type, question_count, total_students, submitted_count)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (homework_id, title, teacher_id, course_id, current_time,
                  paper['data'], 'published', 'interactive', question_count, total_students, 0))

            conn.commit()
            conn.close()

            return True, "互动答题创建成功", homework_id
        except Exception as e:
            import traceback
            traceback.print_exc()
            return False, f"创建互动答题失败: {str(e)}", None

    def publish_homework(self, teacher_id, homework_id):
        """发布作业"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 1. 验证作业权限
            cursor.execute("SELECT id FROM homework WHERE id = ? AND teacher_id = ?", (homework_id, teacher_id))
            if not cursor.fetchone():
                conn.close()
                return False, "作业不存在或无权限"

            # 2. 更新作业状态
            cursor.execute("UPDATE homework SET status = 'published' WHERE id = ?", (homework_id,))
            conn.commit()
            conn.close()

            return True, "作业发布成功"
        except Exception as e:
            return False, f"发布失败: {str(e)}"

    def delete_homework(self, teacher_id, homework_id):
        """删除作业"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 1. 验证作业权限
            cursor.execute("SELECT id FROM homework WHERE id = ? AND teacher_id = ?", (homework_id, teacher_id))
            if not cursor.fetchone():
                conn.close()
                return False, "作业不存在或无权限"

            # 2. 删除相关的成绩记录 (包括两种表)
            cursor.execute("DELETE FROM homework_results WHERE homework_id = ?", (homework_id,))
            cursor.execute("DELETE FROM quiz_results WHERE homework_id = ?", (homework_id,))

            # 3. 删除作业
            cursor.execute("DELETE FROM homework WHERE id = ?", (homework_id,))

            conn.commit()
            conn.close()

            return True, "作业删除成功"
        except Exception as e:
            return False, f"删除失败: {str(e)}"

    def get_homework_details(self, teacher_id, homework_id):
        """获取作业详情，统一处理并返回完整的题目对象列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT h.id, h.title, h.description, h.status, h.deadline, h.data,
                       c.name as course_name, cls.name as class_name
                FROM homework h
                LEFT JOIN course_schedules cs ON h.course_schedule_id = cs.id
                LEFT JOIN courses c ON cs.course_id = c.id
                LEFT JOIN classes cls ON cs.class_id = cls.id
                WHERE h.id = ? AND h.teacher_id = ?
            """, (homework_id, teacher_id))
            
            homework_row = cursor.fetchone()
            if not homework_row:
                conn.close()
                return False, "作业不存在或无权限", None

            homework_dict = dict(homework_row)
            
            try:
                homework_data = json.loads(homework_dict['data']) if homework_dict['data'] else {}
                question_items = homework_data.get('questions', [])
                
                resolved_questions = []
                if question_items:
                    # 检查第一项以判断格式：是ID列表还是对象列表
                    if isinstance(question_items[0], str):
                        placeholders = ','.join(['?'] * len(question_items))
                        cursor.execute(f"SELECT * FROM exercises WHERE id IN ({placeholders})", question_items)
                        questions_map = {str(row['id']): dict(row) for row in cursor.fetchall()}
                        for q_id in question_items:
                            if q_id in questions_map:
                                resolved_questions.append(questions_map[q_id])
                    elif isinstance(question_items[0], dict):
                        for i, q_obj in enumerate(question_items):
                            if 'id' not in q_obj or not q_obj['id']:
                                q_obj['id'] = f"q-{i}"  # 分配临时ID
                            resolved_questions.append(q_obj)
                
                # 确保所有题目的选项都是列表格式
                for q in resolved_questions:
                    if q.get('options') and isinstance(q['options'], str):
                        try:
                            q['options'] = json.loads(q['options'])
                        except json.JSONDecodeError:
                            q['options'] = []

                homework_dict['questions'] = resolved_questions
            except (json.JSONDecodeError, TypeError, IndexError):
                homework_dict['questions'] = []
            
            del homework_dict['data']

            conn.close()
            return True, "获取成功", homework_dict
        except Exception as e:
            return False, f"获取作业详情失败: {str(e)}", None

    def get_homework_submissions(self, teacher_id, homework_id):
        """获取作业的学生提交列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证老师对该作业的权限
            cursor.execute("SELECT id FROM homework WHERE id = ? AND teacher_id = ?", (homework_id, teacher_id))
            if not cursor.fetchone():
                conn.close()
                return False, "作业不存在或无权限", None

            # 获取提交记录
            cursor.execute("""
                SELECT hr.student_id, s.name as student_name, hr.score, hr.submitted_at
                FROM homework_results hr
                JOIN students s ON hr.student_id = s.student_id
                WHERE hr.homework_id = ?
                ORDER BY hr.submitted_at DESC
            """, (homework_id,))
            
            submissions = [dict(row) for row in cursor.fetchall()]
            conn.close()
            
            return True, "获取成功", submissions
        except Exception as e:
            return False, f"获取提交列表失败: {str(e)}", None

    def create_homework_manually(self, teacher_id, data):
        """手动创建作业"""
        try:
            # 验证必要字段
            required_fields = ['title', 'course_id', 'questions', 'deadline', 'status']
            for field in required_fields:
                if field not in data:
                    return False, f'缺少必要字段: {field}', None

            conn = get_db()
            cursor = conn.cursor()

            # 处理截止时间
            deadline = data['deadline']
            if deadline:
                deadline = f"{deadline} 23:59:59"

            # 创建作业记录
            homework_id = str(uuid.uuid4())
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            homework_data = {
                'questions': data['questions'],
                'total_score': data.get('total_score', 100)
            }

            cursor.execute("""
                INSERT INTO homework (
                    id, title, description, course_schedule_id, teacher_id,
                    deadline, status, created_at, data, question_count
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                homework_id,
                data['title'],
                data.get('description', ''),
                data['course_id'],
                teacher_id,
                deadline,
                data['status'],
                current_time,
                json.dumps(homework_data),
                len(data['questions'])
            ))

            conn.commit()
            conn.close()

            return True, '作业保存成功', homework_id
        except Exception as e:
            return False, str(e), None

    def get_homework_analysis(self, teacher_id, homework_id):
        """获取作业分析数据"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 1. 获取作业详情
            success, message, homework = self.get_homework_details(teacher_id, homework_id)
            if not success:
                conn.close()
                return False, message, None

            # 2. 获取作业提交结果
            success, message, results = self.get_homework_submissions(teacher_id, homework_id)
            if not success:
                conn.close()
                return False, message, None

            # 3. 计算题目统计
            question_stats = []
            questions = homework.get('questions', [])
            if questions and results:
                for idx, q in enumerate(questions):
                    question_stats.append({
                        'index': idx + 1,
                        'type_name': q.get('type', ''),
                        'text': q.get('question', ''),
                        'correct_answer': q.get('answer', ''),
                        'correct_count': 0,
                        'total_count': 0,
                        'correct_rate': 0.0
                    })
                
                for res in results:
                    cursor.execute("SELECT answers FROM homework_results WHERE homework_id = ? AND student_id = ?", (homework_id, res['student_id']))
                    ans_row = cursor.fetchone()
                    try:
                        answers = json.loads(ans_row['answers']) if ans_row and ans_row['answers'] else {}
                    except (json.JSONDecodeError, TypeError):
                        answers = {}

                    for idx, q in enumerate(questions):
                        qid = q.get('id', f"q-{idx}")

                        if qid in answers:
                            question_stats[idx]['total_count'] += 1
                            if str(answers[qid]).strip() == str(q.get('answer', '')).strip():
                                question_stats[idx]['correct_count'] += 1
                
                for stat in question_stats:
                    if stat['total_count'] > 0:
                        stat['correct_rate'] = round((stat['correct_count'] / stat['total_count']) * 100, 2)

            # 4. 计算成绩分布
            score_distribution = [0] * 10
            scores = [res['score'] for res in results]
            for score in scores:
                idx = int(score // 10) if score is not None else 0
                if idx >= 10: idx = 9
                if idx < 0: idx = 0
                score_distribution[idx] += 1

            # 5. 计算总体统计指标
            valid_scores = [s for s in scores if s is not None]
            stats = {
                'avg_score': round(sum(valid_scores) / len(valid_scores), 2) if valid_scores else 0,
                'max_score': max(valid_scores) if valid_scores else 0,
                'min_score': min(valid_scores) if valid_scores else 0,
                'pass_rate': round((len([s for s in valid_scores if s >= 60]) / len(valid_scores)) * 100, 2) if valid_scores else 0,
                'excellent_rate': round((len([s for s in valid_scores if s >= 85]) / len(valid_scores)) * 100, 2) if valid_scores else 0,
                'count': len(valid_scores)
            }
            
            conn.close()

            analysis_data = {
                "homework": homework,
                "results": results,
                "question_stats": question_stats,
                "score_distribution": score_distribution,
                "stats": stats
            }
            return True, "分析数据获取成功", analysis_data

        except Exception as e:
            return False, f"获取分析数据失败: {str(e)}", None

    def get_homework_analysis(self, teacher_id, homework_id):
        """获取作业分析数据"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 1. 获取作业详情
            success, message, homework = self.get_homework_details(teacher_id, homework_id)
            if not success:
                conn.close()
                return False, message, None

            # 2. 获取作业提交结果
            success, message, results = self.get_homework_submissions(teacher_id, homework_id)
            if not success:
                conn.close()
                return False, message, None

            # 3. 计算题目统计
            question_stats = []
            questions = homework.get('questions', [])
            if questions and results:
                # 初始化统计结构
                for idx, q in enumerate(questions):
                    question_stats.append({
                        'index': idx + 1,
                        'type_name': q.get('type', ''),
                        'text': q.get('question', ''),
                        'correct_answer': q.get('answer', ''),
                        'correct_count': 0,
                        'total_count': 0,
                        'correct_rate': 0.0
                    })
                
                # 遍历答案计算正确率
                for res in results:
                    try:
                        # homework_results.answers 字段需要被获取
                        cursor.execute("SELECT answers FROM homework_results WHERE homework_id = ? AND student_id = ?", (homework_id, res['student_id']))
                        ans_row = cursor.fetchone()
                        answers = json.loads(ans_row['answers']) if ans_row and ans_row['answers'] else {}
                    except (json.JSONDecodeError, TypeError):
                        answers = {}

                    for idx, q in enumerate(questions):
                        qid = q.get('id') # 假设题目有ID
                        if not qid: # 如果没有ID，使用索引作为key
                            qid = f"q-{idx}"

                        if qid in answers:
                            question_stats[idx]['total_count'] += 1
                            # 假设评分逻辑：答案完全匹配则算对
                            if str(answers[qid]).strip() == str(q.get('answer', '')).strip():
                                question_stats[idx]['correct_count'] += 1
                
                # 计算最终正确率
                for stat in question_stats:
                    if stat['total_count'] > 0:
                        stat['correct_rate'] = round((stat['correct_count'] / stat['total_count']) * 100, 2)

            # 4. 计算成绩分布
            score_distribution = [0] * 10
            scores = [res['score'] for res in results]
            for score in scores:
                idx = int(score // 10)
                if idx >= 10: idx = 9
                if idx < 0: idx = 0
                score_distribution[idx] += 1

            # 5. 计算总体统计指标
            stats = {
                'avg_score': round(sum(scores) / len(scores), 2) if scores else 0,
                'max_score': max(scores) if scores else 0,
                'min_score': min(scores) if scores else 0,
                'pass_rate': round((len([s for s in scores if s >= 60]) / len(scores)) * 100, 2) if scores else 0,
                'excellent_rate': round((len([s for s in scores if s >= 85]) / len(scores)) * 100, 2) if scores else 0,
                'count': len(scores)
            }
            
            conn.close()

            analysis_data = {
                "homework": homework,
                "results": results,
                "question_stats": question_stats,
                "score_distribution": score_distribution,
                "stats": stats
            }
            return True, "分析数据获取成功", analysis_data

        except Exception as e:
            return False, f"获取分析数据失败: {str(e)}", None

    @staticmethod
    def get_papers_by_teacher(teacher_id):
        """获取指定教师的所有试卷"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            cursor.execute("SELECT id, title, description, created_at FROM papers WHERE teacher_id = ? ORDER BY created_at DESC", (teacher_id,))
            papers = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return papers
        except Exception as e:
            print(f"Error getting papers for teacher {teacher_id}: {e}")
            return []
