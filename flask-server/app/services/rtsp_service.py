# flask-server/app/services/rtsp_service.py
import subprocess
import threading
import time
import os
import signal
from pathlib import Path

class RTSPStreamService:
    """RTSP流媒体服务"""
    
    def __init__(self, rtsp_port=8554):
        self.rtsp_port = rtsp_port
        self.active_streams = {}  # stream_name -> process
        self.server_process = None
        
    def start_server(self):
        """启动RTSP服务器"""
        try:
            # 使用mediamtx作为RTSP服务器
            # 这里假设已经安装了mediamtx
            cmd = [
                'mediamtx',
                f'--rtsp-address=:{self.rtsp_port}',
                '--log-level=info'
            ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            print(f"RTSP服务器已启动，端口: {self.rtsp_port}")
            return True
            
        except Exception as e:
            print(f"启动RTSP服务器失败: {e}")
            return False
    
    def stop_server(self):
        """停止RTSP服务器"""
        if self.server_process:
            try:
                # 停止所有活跃的流
                for stream_name in list(self.active_streams.keys()):
                    self.stop_stream(stream_name)
                
                # 停止服务器进程
                os.killpg(os.getpgid(self.server_process.pid), signal.SIGTERM)
                self.server_process.wait(timeout=5)
                print("RTSP服务器已停止")
                
            except Exception as e:
                print(f"停止RTSP服务器失败: {e}")
                # 强制杀死进程
                try:
                    os.killpg(os.getpgid(self.server_process.pid), signal.SIGKILL)
                except:
                    pass
            
            self.server_process = None
    
    def create_stream_endpoint(self, stream_name):
        """创建流端点"""
        # mediamtx会自动创建端点，这里只需要返回URL
        rtsp_url = f"rtsp://localhost:{self.rtsp_port}/{stream_name}"
        return rtsp_url
    
    def start_webrtc_to_rtsp_bridge(self, stream_name, webrtc_offer):
        """启动WebRTC到RTSP的桥接"""
        try:
            # 这里需要实现WebRTC到RTSP的转换
            # 可以使用GStreamer或FFmpeg
            
            # 简化实现：使用FFmpeg从WebRTC接收流并推送到RTSP
            rtsp_url = f"rtsp://localhost:{self.rtsp_port}/{stream_name}"
            
            # 创建一个虚拟的推流进程（实际应该处理WebRTC信令）
            cmd = [
                'ffmpeg',
                '-f', 'lavfi',
                '-i', 'testsrc=duration=3600:size=1280x720:rate=30',
                '-f', 'lavfi', 
                '-i', 'sine=frequency=1000:duration=3600',
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-c:a', 'aac',
                '-f', 'rtsp',
                rtsp_url
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            self.active_streams[stream_name] = process
            print(f"WebRTC到RTSP桥接已启动: {stream_name}")
            
            return True
            
        except Exception as e:
            print(f"启动WebRTC到RTSP桥接失败: {e}")
            return False
    
    def stop_stream(self, stream_name):
        """停止指定的流"""
        if stream_name in self.active_streams:
            try:
                process = self.active_streams[stream_name]
                os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                process.wait(timeout=5)
                print(f"流已停止: {stream_name}")
                
            except Exception as e:
                print(f"停止流失败: {e}")
                # 强制杀死进程
                try:
                    process = self.active_streams[stream_name]
                    os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                except:
                    pass
            
            del self.active_streams[stream_name]
            return True
        
        return False
    
    def get_stream_info(self, stream_name):
        """获取流信息"""
        if stream_name in self.active_streams:
            process = self.active_streams[stream_name]
            return {
                'stream_name': stream_name,
                'status': 'active' if process.poll() is None else 'stopped',
                'rtsp_url': f"rtsp://localhost:{self.rtsp_port}/{stream_name}",
                'pid': process.pid
            }
        return None
    
    def list_active_streams(self):
        """列出所有活跃的流"""
        active = []
        for stream_name, process in self.active_streams.items():
            if process.poll() is None:  # 进程仍在运行
                active.append({
                    'stream_name': stream_name,
                    'rtsp_url': f"rtsp://localhost:{self.rtsp_port}/{stream_name}",
                    'pid': process.pid
                })
            else:
                # 清理已停止的流
                del self.active_streams[stream_name]
        
        return active

class WebRTCToRTSPBridge:
    """WebRTC到RTSP的桥接器"""
    
    def __init__(self, rtsp_service):
        self.rtsp_service = rtsp_service
        self.bridges = {}  # stream_name -> bridge_info
    
    def create_bridge(self, stream_name, student_id):
        """创建WebRTC到RTSP的桥接"""
        try:
            # 创建RTSP端点
            rtsp_url = self.rtsp_service.create_stream_endpoint(stream_name)
            
            # 这里应该创建WebRTC PeerConnection
            # 并设置媒体流转发到RTSP
            
            bridge_info = {
                'stream_name': stream_name,
                'student_id': student_id,
                'rtsp_url': rtsp_url,
                'status': 'created',
                'created_at': time.time()
            }
            
            self.bridges[stream_name] = bridge_info
            
            return bridge_info
            
        except Exception as e:
            print(f"创建WebRTC桥接失败: {e}")
            return None
    
    def handle_webrtc_offer(self, stream_name, offer):
        """处理WebRTC Offer"""
        if stream_name not in self.bridges:
            return None
        
        try:
            # 这里应该处理WebRTC信令
            # 创建Answer并返回
            
            # 简化实现：直接启动推流
            success = self.rtsp_service.start_webrtc_to_rtsp_bridge(stream_name, offer)
            
            if success:
                self.bridges[stream_name]['status'] = 'active'
                
                # 返回模拟的WebRTC Answer
                answer = {
                    'type': 'answer',
                    'sdp': 'v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n'
                }
                
                return answer
            
        except Exception as e:
            print(f"处理WebRTC Offer失败: {e}")
        
        return None
    
    def handle_ice_candidate(self, stream_name, candidate):
        """处理ICE候选"""
        if stream_name not in self.bridges:
            return False
        
        try:
            # 这里应该处理ICE候选
            print(f"收到ICE候选: {stream_name}")
            return True
            
        except Exception as e:
            print(f"处理ICE候选失败: {e}")
            return False
    
    def remove_bridge(self, stream_name):
        """移除桥接"""
        if stream_name in self.bridges:
            # 停止RTSP流
            self.rtsp_service.stop_stream(stream_name)
            
            # 清理桥接信息
            del self.bridges[stream_name]
            
            print(f"WebRTC桥接已移除: {stream_name}")
            return True
        
        return False
    
    def get_bridge_info(self, stream_name):
        """获取桥接信息"""
        return self.bridges.get(stream_name)
    
    def list_active_bridges(self):
        """列出活跃的桥接"""
        return list(self.bridges.values())

# 全局RTSP服务实例
rtsp_service = RTSPStreamService()
webrtc_bridge = WebRTCToRTSPBridge(rtsp_service)

def initialize_rtsp_service():
    """初始化RTSP服务"""
    return rtsp_service.start_server()

def cleanup_rtsp_service():
    """清理RTSP服务"""
    rtsp_service.stop_server()
