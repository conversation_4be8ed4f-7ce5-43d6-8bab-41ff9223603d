// add_homework.js
// 专用于 add_homework.html 的题目数据收集与相关功能

// 获取所有题目数据
function getQuestionData() {
    const questions = [];
    const questionDivs = document.querySelectorAll('.question');
    
    questionDivs.forEach((questionDiv, index) => {
        const questionId = questionDiv.id.split('-')[1];
        const desc = document.getElementById(`desc-${questionId}`).value;
        const score = parseInt(document.getElementById(`score-${questionId}`).value);
        const typeElement = document.getElementById(`type-${questionId}`);
        const type = typeElement ? typeElement.value : "single";

        const question = {
            type: type,
            desc: desc,
            score: score
        };

        if (type === "single" || type === "multiple") {
            const optionsContainer = document.getElementById(`options-container-${questionId}`);
            if (optionsContainer) {
                const optionItems = optionsContainer.querySelectorAll('.option-item');
                question.options = [];

                optionItems.forEach(item => {
                    const optionText = item.querySelector('.option-text');
                    if (optionText) {
                        question.options.push(optionText.value || '');
                    }
                });

                const answerElement = document.getElementById(`answer-${questionId}`);
                question.answer = answerElement ? answerElement.value : '';
            }
        } else if (type === "judge") {
            question.options = ["正确", "错误"];
            const answerElement = document.getElementById(`answer-${questionId}`);
            question.answer = answerElement ? answerElement.value : '正确';
        } else {
            const answerElement = document.getElementById(`answer-${questionId}`);
            question.answer = answerElement ? answerElement.value : '';
        }

        questions.push(question);
    });

    return questions;
}

// 计算总分
function calculateTotalScore() {
    let totalScore = 0;
    const scoreInputs = document.querySelectorAll('.question-score input[type="number"]');
    scoreInputs.forEach(input => {
        totalScore += parseInt(input.value) || 0;
    });
    document.getElementById('total-score').textContent = totalScore;
}

// 监听分值变化，实时更新总分
document.addEventListener('input', function(e) {
    if (e.target.matches('.question-score input[type="number"]')) {
        calculateTotalScore();
    }
});
