layui.use(['form', 'layer', 'element'], function() {
    const form = layui.form;
    const layer = layui.layer;
    const element = layui.element;
    const $ = layui.jquery;

    const examId = window.EXAM_ID;
    const questionsContainer = $('#questions-container');
    const questionNavContainer = $('#question-nav-container');
    const submitBtn = $('#submit-exam-btn');

    let questionData = []; // 存放所有题目信息
    let studentAnswers = {}; // 用于存储学生答案, key为question_id, value为答案

    // 页面加载时获取试卷数据
    async function loadExam() {
        if (!examId) {
            layer.alert('无法获取试卷ID，请返回重试。', { icon: 2 }, function() {
                history.back();
            });
            return;
        }

        try {
            const response = await fetch(`/api/student/exam/${examId}`);
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            if (data.status === 'success' && data.exam) {
                questionData = data.exam.questions;
                renderExam(questionData);
                submitBtn.prop('disabled', false); // 启用提交按钮
            } else {
                throw new Error(data.message || '加载试卷失败');
            }
        } catch (error) {
            console.error('加载试卷失败:', error);
            questionsContainer.html(`<div class="loading-spinner"><p>加载失败: ${error.message}</p></div>`);
            layer.alert(`加载试卷失败: ${error.message}`, { icon: 2 });
        }
    }

    // 渲染整个试卷
    function renderExam(questions) {
        questionsContainer.empty();
        questionNavContainer.empty();

        if (!questions || questions.length === 0) {
            questionsContainer.html('<p>该试卷没有题目。</p>');
            return;
        }

        questions.forEach((q, index) => {
            const questionId = q.id || index; // 使用ID或索引作为唯一标识
            studentAnswers[questionId] = studentAnswers[questionId] || null; // 初始化答案
            const questionHtml = renderQuestion(q, index + 1, questionId);
            questionsContainer.append(questionHtml);
            const navBtn = $(`<a href="#q-${index + 1}" class="layui-btn layui-btn-primary layui-btn-sm" data-question-id="${questionId}">${index + 1}</a>`);
            questionNavContainer.append(navBtn);
        });

        form.render(); // 重新渲染LayUI表单元素
        bindNavEvents();
    }

    // 渲染单个题目
    function renderQuestion(q, index, questionId) {
        let optionsHtml = '';
        const currentAnswer = studentAnswers[questionId];
        const inputName = `q_${questionId}`;

        if (q.type === 'single' || q.type === 'judge') {
            const options = q.type === 'judge' ? ['正确', '错误'] : (q.options || []);
            options.forEach(opt => {
                const checked = (currentAnswer === opt) ? 'checked' : '';
                optionsHtml += `<input type="radio" name="${inputName}" value="${opt}" title="${opt}" lay-skin="primary" ${checked}>`;
            });
        } else if (q.type === 'multiple') {
            const currentAnswers = currentAnswer ? currentAnswer.split(',') : [];
            (q.options || []).forEach(opt => {
                const checked = currentAnswers.includes(opt) ? 'checked' : '';
                optionsHtml += `<input type="checkbox" name="${inputName}" value="${opt}" title="${opt}" lay-skin="primary" ${checked}>`;
            });
        } else if (q.type === 'fill') {
            const value = currentAnswer || '';
            optionsHtml = `<input type="text" name="${inputName}" placeholder="请输入答案" class="layui-input" value="${value}">`;
        } else if (q.type === 'essay') {
            const value = currentAnswer || '';
            optionsHtml = `<textarea name="${inputName}" placeholder="请输入答案" class="layui-textarea">${value}</textarea>`;
        }

        return `
            <div class="question-item" id="q-${index}">
                <div class="question-title">
                    <span>${index}. ${q.question || q.desc}</span>
                    <span class="layui-badge layui-bg-gray">${q.difficulty || 'normal'}</span>
                </div>
                <div class="question-options">
                    ${optionsHtml}
                </div>
            </div>
        `;
    }

    // 绑定答题卡导航事件和表单事件
    function bindNavEvents() {
        questionNavContainer.on('click', 'a', function(e) {
            e.preventDefault();
            const targetId = $(this).attr('href');
            $('html, body').animate({
                scrollTop: $(targetId).offset().top - 80 // 留出顶部导航栏空间
            }, 300);
        });

        // 监听表单项变化，更新答案存储和答题卡状态
        form.on('radio', function(data) {
            const questionId = data.elem.name.split('_')[1];
            studentAnswers[questionId] = data.value;
            updateNavStatus(questionId, true);
        });

        form.on('checkbox', function(data) {
            const questionId = data.elem.name.split('_')[1];
            const checkedValues = $(`input[name="q_${questionId}"]:checked`).map(function() {
                return this.value;
            }).get();
            studentAnswers[questionId] = checkedValues.join(',');
            updateNavStatus(questionId, checkedValues.length > 0);
        });

        questionsContainer.on('input', 'input[type="text"], textarea', function() {
            const questionId = this.name.split('_')[1];
            studentAnswers[questionId] = $(this).val().trim();
            updateNavStatus(questionId, studentAnswers[questionId] !== '');
        });
    }

    // 更新答题卡按钮状态
    function updateNavStatus(questionId, isAnswered) {
        const navBtn = questionNavContainer.find(`a[data-question-id="${questionId}"]`);
        if (navBtn.length) {
            if (isAnswered) {
                navBtn.removeClass('layui-btn-primary').addClass('layui-btn-normal');
            } else {
                navBtn.removeClass('layui-btn-normal').addClass('layui-btn-primary');
            }
        }
    }

    // 提交答案
    submitBtn.on('click', function() {
        layer.confirm('确定要提交答卷吗？提交后将无法修改。', { icon: 3, title: '确认提交' }, function(index) {
            layer.close(index);
            submitAnswers();
        });
    });

    async function submitAnswers() {
        const answers = Object.keys(studentAnswers).map(questionId => {
            return {
                question_id: questionId,
                answer: studentAnswers[questionId] || ""
            };
        });

        const payload = {
            exam_id: examId,
            answers: answers
        };

        submitBtn.prop('disabled', true).find('i').show();
        const originalText = submitBtn.find('span').text();
        submitBtn.find('span').text('正在提交...');

        try {
            const response = await fetch('/api/student/exam/submit', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                layer.alert(`提交成功！您的得分是：${result.score}`, {
                    icon: 1,
                    title: '提交完成',
                    closeBtn: 0
                }, function(i) {
                    layer.close(i);
                    window.location.href = "/student/homework";
                });
            } else {
                throw new Error(result.message || '提交失败，请重试');
            }
        } catch (error) {
            console.error('提交失败:', error);
            layer.alert(`提交失败: ${error.message}`, { icon: 2 });
            submitBtn.prop('disabled', false).find('i').hide();
            submitBtn.find('span').text(originalText);
        }
    }

    // 初始化
    loadExam();
});
