let questionCount = 0;

function addQuestion(type) {
    const questionsDiv = document.getElementById("questions");
    const noQuestionsMessage = document.getElementById("no-questions-message");

    // 如果存在提示文字，则移除
    if (noQuestionsMessage) {
        noQuestionsMessage.remove();
    }

    const questionDiv = document.createElement("div");
    questionDiv.className = "layui-form-item question";

    // 动态计算当前题目数量作为新题目的序号
    const currentQuestionCount = questionsDiv.querySelectorAll('.question').length;
    questionDiv.id = `question-${currentQuestionCount}`;

    let html = `
        <input type="hidden" id="type-${currentQuestionCount}" value="${type}">
        <div class="question-score">
            <div class="question-score-left">
                <label>分值：</label>
                <input type="number" id="score-${currentQuestionCount}" lay-verify="required|number" min="1" value="5" autocomplete="off" class="layui-input">
            </div>
            <button type="button" class="delete-btn" onclick="removeQuestion(${currentQuestionCount})">删除题目</button>
        </div>
        <div class="layui-form-item question-header">
            <span class="question-number">${currentQuestionCount + 1}.</span>
            <input type="text" id="desc-${currentQuestionCount}" lay-verify="required" placeholder="请输入题目" autocomplete="off" class="layui-input">
        </div>
    `;

    if (type === "single" || type === "multiple") {
        const inputType = type === "single" ? "radio" : "checkbox";
        html += `<div class="options-container" id="options-container-${currentQuestionCount}">`;

        // 默认添加4个选项
        for (let i = 0; i < 4; i++) {
            const optionLetter = String.fromCharCode(65 + i); // A, B, C, D...
            const isFirst = i === 0; // 默认第一个选项为正确答案

        html += `
                <div class="option-item ${isFirst ? 'correct' : ''}" data-index="${i}">
                    <div class="option-content">
                        <input type="${inputType}" name="option-${currentQuestionCount}" ${isFirst ? 'checked' : ''} class="option-checkbox" title="正确答案">
                        <span class="option-letter">${optionLetter}.</span>
                        <input type="text" class="layui-input option-text" placeholder="请输入选项内容">
                        ${isFirst ? '<span class="correct-answer">正确答案</span>' : ''}
                    </div>
                    <button type="button" class="option-delete-btn" onclick="deleteOption(${currentQuestionCount}, ${i})" title="删除选项"></button>
                </div>
        `;
    }

        html += `
            <div class="add-option">
                <button type="button" class="add-option-btn" onclick="addOption(${currentQuestionCount})">
                    <i class="layui-icon layui-icon-add-1"></i>
                    添加选项
                </button>
            </div>
        </div>
        <input type="hidden" id="answer-${currentQuestionCount}" value="A">
        `;
    }

    if (type === "judge") {
        html += `
            <div class="options-container">
                <div class="option-item judge-item correct" data-value="正确">
                    <div class="option-content">
                        <input type="radio" name="option-${currentQuestionCount}" checked class="option-checkbox" title="正确答案">
                        <span class="option-letter">A.</span>
                        <span class="fixed-option">正确</span>
                        <span class="correct-answer">正确答案</span>
                    </div>
                </div>
                <div class="option-item judge-item" data-value="错误">
                    <div class="option-content">
                        <input type="radio" name="option-${currentQuestionCount}" class="option-checkbox" title="错误">
                        <span class="option-letter">B.</span>
                        <span class="fixed-option">错误</span>
                    </div>
                </div>
            </div>
            <input type="hidden" id="answer-${currentQuestionCount}" value="正确">
        `;
    }

    if (type === "fill") {
        html += `
            <div class="layui-form-item fill-answer">
                <label class="layui-form-label">填空题答案：</label>
                <div class="layui-input-block">
                    <input type="text" id="answer-${currentQuestionCount}" lay-verify="required" autocomplete="off" class="layui-input">
                </div>
            </div>
        `;
    }

    if (type === "subjective") {
        html += `
            <div class="layui-form-item subjective-answer">
                <label class="layui-form-label">主观题答案：</label>
                <div class="layui-input-block">
                    <textarea id="answer-${currentQuestionCount}" lay-verify="required" class="layui-textarea" rows="4"></textarea>
                </div>
            </div>
        `;
    }


    questionDiv.innerHTML = html;
    questionsDiv.appendChild(questionDiv);

    // 每次添加题目后重新计算总分
    calculateTotalScore();

    // 为分值输入框绑定 input 事件，实时更新总分
    const scoreInput = document.getElementById(`score-${currentQuestionCount}`);
    scoreInput.addEventListener('input', calculateTotalScore);

    // 为选项添加事件监听
    if (type === "single" || type === "multiple") {
        const optionItems = questionDiv.querySelectorAll('.option-item');
        optionItems.forEach((item, index) => {
            const checkbox = item.querySelector('.option-checkbox');

            // 绑定整个选项项的点击事件
            item.addEventListener('click', function(e) {
                // 如果点击的是删除按钮或文本输入框，不处理
                if (e.target.classList.contains('option-delete-btn') ||
                    e.target.closest('.option-delete-btn') ||
                    e.target.classList.contains('option-text')) {
                    return;
                }

                // 阻止事件冒泡
                e.preventDefault();
                e.stopPropagation();

                if (type === "single") {
                    // 单选：移除所有正确答案标识
                    optionItems.forEach(optionItem => {
                        optionItem.querySelector('.option-checkbox').checked = false;
                        optionItem.classList.remove('correct');
                        const correctSpan = optionItem.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    });

                    // 设置当前为正确答案
                    checkbox.checked = true;
                    item.classList.add('correct');
                    if (!item.querySelector('.correct-answer')) {
                        const correctSpan = document.createElement('span');
                        correctSpan.className = 'correct-answer';
                        correctSpan.textContent = '正确答案';
                        item.querySelector('.option-content').appendChild(correctSpan);
                    }

                    // 更新答案值
                    document.getElementById(`answer-${currentQuestionCount}`).value = String.fromCharCode(65 + index);
                } else if (type === "multiple") {
                    // 多选：切换选中状态
                    const isChecked = checkbox.checked;
                    checkbox.checked = !isChecked;

                    if (!isChecked) {
                        item.classList.add('correct');
                        if (!item.querySelector('.correct-answer')) {
                            const correctSpan = document.createElement('span');
                            correctSpan.className = 'correct-answer';
                            correctSpan.textContent = '正确答案';
                            item.querySelector('.option-content').appendChild(correctSpan);
                        }
                    } else {
                        item.classList.remove('correct');
                        const correctSpan = item.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    }

                    // 更新多选答案，收集所有选中的选项
                    const selectedOptions = [];
                    optionItems.forEach((optionItem, idx) => {
                        if (optionItem.querySelector('.option-checkbox').checked) {
                            selectedOptions.push(String.fromCharCode(65 + idx));
                        }
                    });
                    document.getElementById(`answer-${currentQuestionCount}`).value = selectedOptions.join(',');
                }
            });

            // 单独绑定复选框的点击事件
            checkbox.addEventListener('click', function(e) {
                e.stopPropagation();
                // 触发父级的点击事件
                item.click();
            });

            // 阻止文本框的点击事件冒泡
            const optionText = item.querySelector('.option-text');
            if (optionText) {
                optionText.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });
    } else if (type === "judge") {
        // 为判断题添加事件监听
        const judgeItems = questionDiv.querySelectorAll('.judge-item');
        judgeItems.forEach((item) => {
            const radio = item.querySelector('.option-checkbox');
            const answer = item.getAttribute('data-value');

            item.addEventListener('click', function(e) {
                if (e.target.closest('.judge-item')) {
                    // 移除所有正确答案标识
                    judgeItems.forEach(judgeItem => {
                        judgeItem.querySelector('.option-checkbox').checked = false;
                        const correctSpan = judgeItem.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    });

                    // 设置当前为正确答案
                    radio.checked = true;
                    item.classList.add('correct');
                    if (!item.querySelector('.correct-answer')) {
                        const correctSpan = document.createElement('span');
                        correctSpan.className = 'correct-answer';
                        correctSpan.textContent = '正确答案';
                        item.querySelector('.option-content').appendChild(correctSpan);
                    }

                    // 更新答案值
                    document.getElementById(`answer-${currentQuestionCount}`).value = answer;
                }
            });
        });
    }

    questionCount++;
}

function deleteOption(questionId, optionIndex) {
    const container = document.getElementById(`options-container-${questionId}`);
    const options = container.querySelectorAll('.option-item');

    if (options.length <= 2) {
        layer.msg('至少需要保留2个选项', {icon: 2});
        return;
    }

    // 删除选项
    options[optionIndex].remove();

    // 重新排列选项字母标识
    const remainingOptions = container.querySelectorAll('.option-item');
    remainingOptions.forEach((item, index) => {
        item.dataset.index = index;
        item.querySelector('.option-letter').textContent = String.fromCharCode(65 + index) + '.';
    });

    // 更新答案
    updateAnswer(questionId);
}

function addOption(questionId) {
    const container = document.getElementById(`options-container-${questionId}`);
    const options = container.querySelectorAll('.option-item');
    const newIndex = options.length;
    const optionLetter = String.fromCharCode(65 + newIndex);

    const newOptionDiv = document.createElement('div');
    newOptionDiv.className = 'option-item';
    newOptionDiv.dataset.index = newIndex;

    const type = document.getElementById(`type-${questionId}`).value;
    const inputType = type === "single" ? "radio" : "checkbox";

    newOptionDiv.innerHTML = `
        <div class="option-content">
            <input type="${inputType}" name="option-${questionId}" class="option-checkbox">
            <span class="option-letter">${optionLetter}.</span>
            <input type="text" class="layui-input option-text" placeholder="请输入选项内容">
        </div>
        <button type="button" class="option-delete-btn" onclick="deleteOption(${questionId}, ${newIndex})" title="删除选项"></button>
    `;

    // 在添加按钮前插入新选项
    const addOptionDiv = container.querySelector('.add-option');
    container.insertBefore(newOptionDiv, addOptionDiv);

    // 为新选项添加事件监听
    const checkbox = newOptionDiv.querySelector('.option-checkbox');

    // 绑定整个选项项的点击事件
    newOptionDiv.addEventListener('click', function(e) {
        // 如果点击的是删除按钮或文本输入框，不处理
        if (e.target.classList.contains('option-delete-btn') ||
            e.target.closest('.option-delete-btn') ||
            e.target.classList.contains('option-text')) {
            return;
        }

        // 阻止事件冒泡
        e.preventDefault();
        e.stopPropagation();

        if (type === "single") {
            // 单选：移除所有正确答案标识
            const allOptions = container.querySelectorAll('.option-item');
            allOptions.forEach(item => {
                item.querySelector('.option-checkbox').checked = false;
                item.classList.remove('correct');
                const correctSpan = item.querySelector('.correct-answer');
                if (correctSpan) correctSpan.remove();
            });

            // 设置当前为正确答案
            checkbox.checked = true;
            newOptionDiv.classList.add('correct');
            if (!newOptionDiv.querySelector('.correct-answer')) {
                const correctSpan = document.createElement('span');
                correctSpan.className = 'correct-answer';
                correctSpan.textContent = '正确答案';
                newOptionDiv.querySelector('.option-content').appendChild(correctSpan);
            }

            // 更新答案值
            document.getElementById(`answer-${questionId}`).value = optionLetter;
        } else if (type === "multiple") {
            // 多选：切换选中状态
            const isChecked = checkbox.checked;
            checkbox.checked = !isChecked;

            if (!isChecked) {
                newOptionDiv.classList.add('correct');
                if (!newOptionDiv.querySelector('.correct-answer')) {
                    const correctSpan = document.createElement('span');
                    correctSpan.className = 'correct-answer';
                    correctSpan.textContent = '正确答案';
                    newOptionDiv.querySelector('.option-content').appendChild(correctSpan);
                }
            } else {
                newOptionDiv.classList.remove('correct');
                const correctSpan = newOptionDiv.querySelector('.correct-answer');
                if (correctSpan) correctSpan.remove();
            }

            // 更新答案
            updateAnswer(questionId);
        }
    });

    // 单独绑定复选框的点击事件
    checkbox.addEventListener('click', function(e) {
        e.stopPropagation();
        // 触发父级的点击事件
        newOptionDiv.click();
    });

    // 阻止文本框的点击事件冒泡
    const optionText = newOptionDiv.querySelector('.option-text');
    if (optionText) {
        optionText.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
}

function updateAnswer(questionId) {
    const container = document.getElementById(`options-container-${questionId}`);
    const type = document.getElementById(`type-${questionId}`).value;
    const options = container.querySelectorAll('.option-item');

    if (type === "multiple") {
        const selectedOptions = [];
        options.forEach((item, index) => {
            if (item.querySelector('.option-checkbox').checked) {
                selectedOptions.push(String.fromCharCode(65 + index));
            }
        });
        document.getElementById(`answer-${questionId}`).value = selectedOptions.join(',');
    } else if (type === "single") {
        let selectedOption = 'A'; // 默认值
        options.forEach((item, index) => {
            if (item.querySelector('.option-checkbox').checked) {
                selectedOption = String.fromCharCode(65 + index);
            }
        });
        document.getElementById(`answer-${questionId}`).value = selectedOption;
    }
}

function removeQuestion(id) {
    layer.confirm('确定要删除此题吗？', {icon: 3, title:'提示'}, function(index){
        layer.close(index);
        const questionDiv = document.getElementById(`question-${id}`);
        questionDiv.remove();

        // 检查是否还有题目存在
        const questionsDiv = document.getElementById("questions");
        if (questionsDiv.childElementCount === 0) {
            // 如果没有题目了，重新显示提示文字
            const noQuestionsMessage = document.createElement("p");
            noQuestionsMessage.id = "no-questions-message";
            noQuestionsMessage.style.color = "gray";
            noQuestionsMessage.style.textAlign = "center";
            noQuestionsMessage.textContent = "点击左侧按钮添加题目";
            questionsDiv.appendChild(noQuestionsMessage);
        } else {
            updateQuestionNumbers();
        }
        // 每次删除题目后重新计算总分
        calculateTotalScore();
    });
}

// 重新计算所有题目的序号
function updateQuestionNumbers() {
    const questions = document.querySelectorAll('.question');
    questions.forEach((question, index) => {
        // 更新序号显示
        const questionNumber = question.querySelector('.question-number');
        if (questionNumber) {
            questionNumber.textContent = `${index + 1}.`;
        }
    });
}

function submitPaper() {
    // 表单验证
    let isValid = true;
    const paperTitleInput = document.getElementById("paper-title").value;
    if (!paperTitleInput) {
        layer.msg('请输入试卷标题', {icon: 2});
        return;
    }

    const courseScheduleElement = document.getElementById("course-schedule");
    const courseScheduleId = courseScheduleElement ? courseScheduleElement.value : '';

    // 只有在课堂试卷模式下才需要验证课程关联
    if (courseScheduleElement && courseScheduleElement.value !== '' && !courseScheduleId) {
        layer.msg('请选择关联的课程', {icon: 2});
        return;
    }

    const questions = [];
    for (let i = 0; i < questionCount; i++) {
        const questionDiv = document.getElementById(`question-${i}`);
        if (!questionDiv) continue;

        const desc = document.getElementById(`desc-${i}`).value;
        const score = parseInt(document.getElementById(`score-${i}`).value);
        const typeElement = document.getElementById(`type-${i}`);
        const type = typeElement ? typeElement.value : "single"; // 使用隐藏字段保存的题目类型

        if (!desc) {
            layer.msg(`请填写第${i+1}题的题目内容`, {icon: 2});
            isValid = false;
            break;
        }

        if (isNaN(score) || score <= 0) {
            layer.msg(`请为第${i+1}题设置有效的分值`, {icon: 2});
            isValid = false;
            break;
        }

        const question = {
            id: `q-${i}`,
            type: type,
            desc: desc,
            score: score
        };

        if (type === "single" || type === "multiple") {
            // 根据实际选中的选项确定答案
            const optionsContainer = document.getElementById(`options-container-${i}`);
            if (optionsContainer) {
                // 收集选项内容
                const optionItems = optionsContainer.querySelectorAll('.option-item');
                question.options = [];

                optionItems.forEach(item => {
                    const optionText = item.querySelector('.option-text');
                    if (optionText) {
                        question.options.push(optionText.value || '');
                    } else {
                        const fixedOption = item.querySelector('.fixed-option');
                        if (fixedOption) {
                            question.options.push(fixedOption.textContent || '');
                        }
                    }
                });

                // 收集正确答案
                if (type === "single") {
                    // 单选题：找到选中的选项
                    const selectedItem = optionsContainer.querySelector('.option-item .option-checkbox:checked');
                    if (selectedItem) {
                        const index = Array.from(optionItems).findIndex(item =>
                            item.contains(selectedItem));
                        question.answer = String.fromCharCode(65 + index);
                    } else {
                        question.answer = "A"; // 默认值
                    }
                } else {
                    // 多选题：找到所有选中的选项
                    const selectedAnswers = [];
                    optionItems.forEach((item, index) => {
                        const checkbox = item.querySelector('.option-checkbox');
                        if (checkbox && checkbox.checked) {
                            selectedAnswers.push(String.fromCharCode(65 + index));
                        }
                    });
                    question.answer = selectedAnswers.length > 0 ? selectedAnswers.join(',') : "A";
                }
            }
        } else if (type === "judge") {
            // 判断题答案
            const judgeItems = questionDiv.querySelectorAll('.judge-item');
            let selectedAnswer = "正确"; // 默认值

            judgeItems.forEach(item => {
                const radio = item.querySelector('.option-checkbox');
                if (radio && radio.checked) {
                    selectedAnswer = item.getAttribute('data-value');
                }
            });

            question.answer = selectedAnswer;
            question.options = ["正确", "错误"];
        } else {
            // 填空题和主观题
            question.answer = document.getElementById(`answer-${i}`).value;
        }

        questions.push(question);
    }

    // 从页面输入框中获取试卷标题和描述
    const paperTitle = paperTitleInput;
    const paperDescription = document.getElementById("paper-description").value;
    const paperTime = document.getElementById("paper-time").value;

    const paper = {
        id: `paper-${Date.now()}`,
        title: paperTitle || "未命名试卷", // 如果未填写标题，默认为"未命名试卷"
        description: paperDescription || "", // 如果未填写描述，默认为空字符串
        course_schedule_id: courseScheduleId || null, // 关联的课程安排ID，资源库试卷为null
        questions: questions,
        paper_time: paperTime
    };

    if (!isValid) return;

    const loading = layer.load(1, {
        shade: [0.1, '#fff']
    });

    fetch("/dashboard/add_paper", {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify(paper)
    })
        .then(response => response.json())
        .then(data => {
            layer.close(loading);
            if (data.status === "success") {
                layer.msg("试卷保存成功！", {icon: 1});
                setTimeout(function() {
                        window.location.href = '/dashboard/papers';
                    }, 1500);
            } else {
                layer.msg(`保存失败：${data.message}`, {icon: 2});
            }
        })
        .catch(error => {
            layer.close(loading);
            layer.msg(`请求失败：${error}`, {icon: 2});
        });
}

function handleCancelEdit() {
    layer.confirm('试卷尚未保存，确定要取消吗？',{
        title: '提示',
        btn: ['确定', '继续编辑'],
        icon: 3
    }, function(index){
        layer.close(index);//点击确定
        window.location.href = '/dashboard/papers';
    }, function(index){
        layer.close(index);//点击继续编辑
    });
}
// 计算总分
function calculateTotalScore() {
    let totalScore = 0;
    const scoreInputs = document.querySelectorAll('.question-score input[type="number"]');
    scoreInputs.forEach(input => {
        totalScore += parseInt(input.value) || 0;
    });
    document.getElementById('total-score').textContent = totalScore;
}

// 初始化时计算总分
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;

    form.render();
    calculateTotalScore();
});

// 导入习题相关功能
let importExercises = [];
let selectedExercises = [];
let importCurrentFilters = {
    keyword: '',
    types: [],
    difficulties: []
};

// 打开导入习题弹窗
function openImportExerciseModal() {
    layer.open({
        type: 1,
        title: '从资源库导入习题',
        content: $('#importExerciseModal'),
        area: ['900px', '700px'],
        success: function() {
            // 重置选择状态
            selectedExercises = [];
            importCurrentFilters = {
                keyword: '',
                types: [],
                difficulties: []
            };

            // 清空搜索框
            $('#importSearchInput').val('');
            $('#importClearSearchBtn').hide();

            // 重置筛选条件
            $('input[name="importTypeFilter"]').prop('checked', false);
            $('input[name="importDifficultyFilter"]').prop('checked', false);
            $('#importFilterText').text('全部');

            // 加载习题列表
            loadImportExercises();

            // 绑定事件
            bindImportEvents();
        }
    });
}

// 绑定导入弹窗事件
function bindImportEvents() {
    // 搜索功能
    $('#importSearchBtn').off('click').on('click', function() {
        importCurrentFilters.keyword = $('#importSearchInput').val().trim();
        loadImportExercises();
    });

    $('#importSearchInput').off('keypress').on('keypress', function(e) {
        if (e.which === 13) {
            $('#importSearchBtn').click();
        }
    });

    $('#importSearchInput').off('input').on('input', function() {
        const value = $(this).val();
        $('#importClearSearchBtn').toggle(value.length > 0);
    });

    $('#importClearSearchBtn').off('click').on('click', function() {
        $('#importSearchInput').val('');
        $(this).hide();
        importCurrentFilters.keyword = '';
        loadImportExercises();
    });

    // 筛选功能
    $('#importFilterBtn').off('click').on('click', function(e) {
        e.stopPropagation();
        $('#importFilterPanel').toggle();
    });

    $(document).off('click.importFilter').on('click.importFilter', function(e) {
        if (!$(e.target).closest('#importFilterPanel, #importFilterBtn').length) {
            $('#importFilterPanel').hide();
        }
    });

    $('#importClearFilterBtn').off('click').on('click', function() {
        $('input[name="importTypeFilter"]').prop('checked', false);
        $('input[name="importDifficultyFilter"]').prop('checked', false);
        $('#importFilterText').text('全部');
    });

    $('#importConfirmFilterBtn').off('click').on('click', function() {
        // 获取选中的题型
        const selectedTypes = [];
        $('input[name="importTypeFilter"]:checked').each(function() {
            selectedTypes.push($(this).val());
        });

        // 获取选中的难度
        const selectedDifficulties = [];
        $('input[name="importDifficultyFilter"]:checked').each(function() {
            selectedDifficulties.push($(this).val());
        });

        importCurrentFilters.types = selectedTypes;
        importCurrentFilters.difficulties = selectedDifficulties;

        // 更新筛选文本
        updateImportFilterText();

        // 隐藏筛选面板
        $('#importFilterPanel').hide();

        // 重新加载数据
        loadImportExercises();
    });

    // 全选功能
    $('#selectAllExercises').off('change').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.exercise-checkbox').prop('checked', isChecked);

        if (isChecked) {
            selectedExercises = [...importExercises];
        } else {
            selectedExercises = [];
        }

        updateImportButtonState();
    });

    // 确认导入
    $('#confirmImportBtn').off('click').on('click', function() {
        if (selectedExercises.length === 0) {
            layer.msg('请选择要导入的习题', {icon: 2});
            return;
        }

        // 导入选中的习题
        importSelectedExercises();
        layer.closeAll();
    });
}

// 加载导入习题列表
function loadImportExercises() {
    const params = new URLSearchParams();

    if (importCurrentFilters.keyword) {
        params.append('keyword', importCurrentFilters.keyword);
    }

    if (importCurrentFilters.types.length > 0) {
        params.append('types', importCurrentFilters.types.join(','));
    }

    if (importCurrentFilters.difficulties.length > 0) {
        params.append('difficulties', importCurrentFilters.difficulties.join(','));
    }

    fetch(`/dashboard/get_exercises?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                importExercises = data.exercises;
                renderImportExerciseList();
                $('#importQuestionCountText').text(`共 ${importExercises.length} 道题`);
            } else {
                layer.msg('加载习题失败: ' + data.message, {icon: 2});
            }
        })
        .catch(error => {
            layer.msg('请求失败: ' + error, {icon: 2});
        });
}

// 渲染导入习题列表
function renderImportExerciseList() {
    const tbody = $('#importExerciseTableBody');

    if (importExercises.length === 0) {
        tbody.html(`
            <tr>
                <td colspan="6" style="text-align: center; color: #999; padding: 40px;">
                    暂无符合条件的习题
                </td>
            </tr>
        `);
        return;
    }

    let html = '';
    importExercises.forEach((exercise, index) => {
        const typeMap = {
            'single': '单选题',
            'multiple': '多选题',
            'judge': '判断题',
            'fill': '填空题',
            'subjective': '主观题'
        };

        const difficultyMap = {
            'easy': '简单',
            'medium': '中等',
            'hard': '困难'
        };

        const typeClass = `type-${exercise.type}`;
        const difficultyClass = `difficulty-${exercise.difficulty}`;

        html += `
            <tr>
                <td>
                    <input type="checkbox" class="exercise-checkbox" data-exercise-id="${exercise.id}" lay-skin="primary">
                </td>
                <td>${index + 1}</td>
                <td>
                    <span class="layui-badge ${typeClass}">${typeMap[exercise.type] || exercise.type}</span>
                </td>
                <td style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${exercise.question}">
                    ${exercise.question}
                </td>
                <td>
                    <span class="layui-badge ${difficultyClass}">${difficultyMap[exercise.difficulty] || exercise.difficulty}</span>
                </td>
                <td>${new Date(exercise.created_at).toLocaleDateString()}</td>
            </tr>
        `;
    });

    tbody.html(html);

    // 绑定复选框事件
    $('.exercise-checkbox').off('change').on('change', function() {
        const exerciseId = $(this).data('exercise-id');
        const exercise = importExercises.find(ex => ex.id === exerciseId);

        if ($(this).prop('checked')) {
            if (!selectedExercises.find(ex => ex.id === exerciseId)) {
                selectedExercises.push(exercise);
            }
        } else {
            selectedExercises = selectedExercises.filter(ex => ex.id !== exerciseId);
        }

        updateImportButtonState();
        updateSelectAllState();
    });
}

// 更新筛选文本
function updateImportFilterText() {
    const typeCount = importCurrentFilters.types.length;
    const difficultyCount = importCurrentFilters.difficulties.length;

    if (typeCount === 0 && difficultyCount === 0) {
        $('#importFilterText').text('全部');
    } else {
        const parts = [];
        if (typeCount > 0) {
            parts.push(`${typeCount}种题型`);
        }
        if (difficultyCount > 0) {
            parts.push(`${difficultyCount}种难度`);
        }
        $('#importFilterText').text(parts.join(', '));
    }
}

// 更新导入按钮状态
function updateImportButtonState() {
    const btn = $('#confirmImportBtn');
    if (selectedExercises.length > 0) {
        btn.removeClass('layui-btn-disabled').prop('disabled', false);
        btn.html(`<i class="layui-icon layui-icon-ok"></i> 导入选中习题 (${selectedExercises.length})`);
    } else {
        btn.addClass('layui-btn-disabled').prop('disabled', true);
        btn.html('<i class="layui-icon layui-icon-ok"></i> 导入选中习题');
    }
}

// 更新全选状态
function updateSelectAllState() {
    const totalCheckboxes = $('.exercise-checkbox').length;
    const checkedCheckboxes = $('.exercise-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#selectAllExercises').prop('checked', false).prop('indeterminate', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#selectAllExercises').prop('checked', true).prop('indeterminate', false);
    } else {
        $('#selectAllExercises').prop('checked', false).prop('indeterminate', true);
    }
}

// 导入选中的习题
function importSelectedExercises() {
    selectedExercises.forEach(exercise => {
        // 将习题数据转换为试卷题目格式
        const questionData = convertExerciseToQuestion(exercise);
        addQuestionFromData(questionData);
    });

    layer.msg(`成功导入 ${selectedExercises.length} 道习题`, {icon: 1});
    calculateTotalScore();
}

// 将习题数据转换为试卷题目格式
function convertExerciseToQuestion(exercise) {
    const questionData = {
        type: exercise.type,
        question: exercise.question,
        score: 5 // 默认分值
    };

    // 根据题型处理选项和答案
    switch (exercise.type) {
        case 'single':
        case 'multiple':
            if (exercise.options) {
                // 检查选项数据是否已经是数组格式
                if (Array.isArray(exercise.options)) {
                    questionData.options = exercise.options;
                } else {
                    // 如果是字符串，尝试解析JSON
                    try {
                        questionData.options = JSON.parse(exercise.options);
                    } catch (e) {
                        console.warn('选项数据解析失败，使用默认选项:', e);
                        questionData.options = ['选项A', '选项B', '选项C', '选项D'];
                    }
                }
            } else {
                questionData.options = ['选项A', '选项B', '选项C', '选项D'];
            }
            questionData.answer = exercise.answer;
            break;

        case 'judge':
            questionData.answer = exercise.answer;
            break;

        case 'fill':
        case 'subjective':
            questionData.answer = exercise.answer;
            break;
    }

    return questionData;
}

// 从数据添加题目到试卷
function addQuestionFromData(questionData) {
    const questionsDiv = document.getElementById("questions");
    const noQuestionsMessage = document.getElementById("no-questions-message");

    // 如果存在提示文字，则移除
    if (noQuestionsMessage) {
        noQuestionsMessage.remove();
    }

    const questionDiv = document.createElement("div");
    questionDiv.className = "layui-form-item question";

    // 动态计算当前题目数量作为新题目的序号
    const currentQuestionCount = questionsDiv.querySelectorAll('.question').length;
    questionDiv.id = `question-${currentQuestionCount}`;

    // 使用与原有addQuestion函数相同的HTML结构
    let html = `
        <input type="hidden" id="type-${currentQuestionCount}" value="${questionData.type}">
        <div class="question-score">
            <div class="question-score-left">
                <label>分值：</label>
                <input type="number" id="score-${currentQuestionCount}" lay-verify="required|number" min="1" value="${questionData.score || 5}" autocomplete="off" class="layui-input">
            </div>
            <button type="button" class="delete-btn" onclick="removeQuestion(${currentQuestionCount})">删除题目</button>
        </div>
        <div class="layui-form-item question-header">
            <span class="question-number">${currentQuestionCount + 1}.</span>
            <input type="text" id="desc-${currentQuestionCount}" lay-verify="required" placeholder="请输入题目" autocomplete="off" class="layui-input" value="${questionData.question}">
        </div>
    `;

    // 根据题型生成选项部分
    if (questionData.type === "single" || questionData.type === "multiple") {
        html += generateOptionsHTML(currentQuestionCount, questionData);
    } else if (questionData.type === "judge") {
        html += generateJudgeOptionsHTML(currentQuestionCount, questionData);
    } else if (questionData.type === "fill") {
        html += generateFillAnswerHTML(currentQuestionCount, questionData);
    } else if (questionData.type === "subjective") {
        html += generateSubjectiveAnswerHTML(currentQuestionCount, questionData);
    }

    questionDiv.innerHTML = html;
    questionsDiv.appendChild(questionDiv);

    // 为分值输入框绑定 input 事件，实时更新总分
    const scoreInput = document.getElementById(`score-${currentQuestionCount}`);
    scoreInput.addEventListener('input', calculateTotalScore);

    // 为选项添加事件监听（复用原有逻辑）
    addEventListenersToQuestion(questionDiv, currentQuestionCount, questionData.type);

    // 重新计算题目序号和总分
    updateQuestionNumbers();
    calculateTotalScore();

    // 更新全局题目计数
    questionCount++;
}

// 生成选择题选项HTML
function generateOptionsHTML(questionIndex, questionData) {
    const options = questionData.options || ['选项A', '选项B', '选项C', '选项D'];
    const type = questionData.type;
    const inputType = type === "single" ? "radio" : "checkbox";
    const answers = questionData.answer ? (type === "multiple" ? questionData.answer.split(',') : [questionData.answer]) : [];

    let html = `<div class="options-container" id="options-container-${questionIndex}">`;

    options.forEach((option, index) => {
        const letter = String.fromCharCode(65 + index);
        const isCorrect = answers.includes(option) || answers.includes(letter);

        html += `
            <div class="option-item ${isCorrect ? 'correct' : ''}" data-index="${index}">
                <div class="option-content">
                    <input type="${inputType}" name="option-${questionIndex}" ${isCorrect ? 'checked' : ''} class="option-checkbox" title="正确答案">
                    <span class="option-letter">${letter}.</span>
                    <input type="text" class="layui-input option-text" placeholder="请输入选项内容" value="${option}">
                    ${isCorrect ? '<span class="correct-answer">正确答案</span>' : ''}
                </div>
                <button type="button" class="option-delete-btn" onclick="deleteOption(${questionIndex}, ${index})" title="删除选项"></button>
            </div>
        `;
    });

    html += `
        <div class="add-option">
            <button type="button" class="add-option-btn" onclick="addOption(${questionIndex})">
                <i class="layui-icon layui-icon-add-1"></i>
                添加选项
            </button>
        </div>
    </div>
    <input type="hidden" id="answer-${questionIndex}" value="${questionData.answer || (type === 'single' ? 'A' : '')}">
    `;

    return html;
}

// 生成判断题选项HTML
function generateJudgeOptionsHTML(questionIndex, questionData) {
    const answer = questionData.answer || '正确';

    let html = `
        <div class="options-container">
            <div class="option-item judge-item ${answer === '正确' ? 'correct' : ''}" data-value="正确">
                <div class="option-content">
                    <input type="radio" name="option-${questionIndex}" ${answer === '正确' ? 'checked' : ''} class="option-checkbox" title="正确答案">
                    <span class="option-letter">A.</span>
                    <span class="fixed-option">正确</span>
                    ${answer === '正确' ? '<span class="correct-answer">正确答案</span>' : ''}
                </div>
            </div>
            <div class="option-item judge-item ${answer === '错误' ? 'correct' : ''}" data-value="错误">
                <div class="option-content">
                    <input type="radio" name="option-${questionIndex}" ${answer === '错误' ? 'checked' : ''} class="option-checkbox" title="错误">
                    <span class="option-letter">B.</span>
                    <span class="fixed-option">错误</span>
                    ${answer === '错误' ? '<span class="correct-answer">正确答案</span>' : ''}
                </div>
            </div>
        </div>
        <input type="hidden" id="answer-${questionIndex}" value="${answer}">
    `;

    return html;
}

// 生成填空题答案HTML
function generateFillAnswerHTML(questionIndex, questionData) {
    return `
        <div class="layui-form-item fill-answer">
            <label class="layui-form-label">填空题答案：</label>
            <div class="layui-input-block">
                <input type="text" id="answer-${questionIndex}" lay-verify="required" autocomplete="off" class="layui-input" value="${questionData.answer || ''}">
            </div>
        </div>
    `;
}

// 生成主观题答案HTML
function generateSubjectiveAnswerHTML(questionIndex, questionData) {
    return `
        <div class="layui-form-item subjective-answer">
            <label class="layui-form-label">主观题答案：</label>
            <div class="layui-input-block">
                <textarea id="answer-${questionIndex}" lay-verify="required" class="layui-textarea" rows="4">${questionData.answer || ''}</textarea>
            </div>
        </div>
    `;
}

// 为导入的题目添加事件监听（复用原有逻辑）
function addEventListenersToQuestion(questionDiv, questionIndex, type) {
    if (type === "single" || type === "multiple") {
        const optionItems = questionDiv.querySelectorAll('.option-item');
        optionItems.forEach((item, index) => {
            const checkbox = item.querySelector('.option-checkbox');

            // 绑定整个选项项的点击事件
            item.addEventListener('click', function(e) {
                // 如果点击的是删除按钮或文本输入框，不处理
                if (e.target.classList.contains('option-delete-btn') ||
                    e.target.closest('.option-delete-btn') ||
                    e.target.classList.contains('option-text')) {
                    return;
                }

                // 阻止事件冒泡
                e.preventDefault();
                e.stopPropagation();

                if (type === "single") {
                    // 单选：移除所有正确答案标识
                    optionItems.forEach(optionItem => {
                        optionItem.querySelector('.option-checkbox').checked = false;
                        optionItem.classList.remove('correct');
                        const correctSpan = optionItem.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    });

                    // 设置当前为正确答案
                    checkbox.checked = true;
                    item.classList.add('correct');
                    if (!item.querySelector('.correct-answer')) {
                        const correctSpan = document.createElement('span');
                        correctSpan.className = 'correct-answer';
                        correctSpan.textContent = '正确答案';
                        item.querySelector('.option-content').appendChild(correctSpan);
                    }

                    // 更新答案值
                    document.getElementById(`answer-${questionIndex}`).value = String.fromCharCode(65 + index);
                } else if (type === "multiple") {
                    // 多选：切换选中状态
                    const isChecked = checkbox.checked;
                    checkbox.checked = !isChecked;

                    if (!isChecked) {
                        item.classList.add('correct');
                        if (!item.querySelector('.correct-answer')) {
                            const correctSpan = document.createElement('span');
                            correctSpan.className = 'correct-answer';
                            correctSpan.textContent = '正确答案';
                            item.querySelector('.option-content').appendChild(correctSpan);
                        }
                    } else {
                        item.classList.remove('correct');
                        const correctSpan = item.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    }

                    // 更新多选答案，收集所有选中的选项
                    const selectedOptions = [];
                    optionItems.forEach((optionItem, idx) => {
                        if (optionItem.querySelector('.option-checkbox').checked) {
                            selectedOptions.push(String.fromCharCode(65 + idx));
                        }
                    });
                    document.getElementById(`answer-${questionIndex}`).value = selectedOptions.join(',');
                }
            });

            // 单独绑定复选框的点击事件
            checkbox.addEventListener('click', function(e) {
                e.stopPropagation();
                // 触发父级的点击事件
                item.click();
            });

            // 阻止文本框的点击事件冒泡
            const optionText = item.querySelector('.option-text');
            if (optionText) {
                optionText.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });
    } else if (type === "judge") {
        // 为判断题添加事件监听
        const judgeItems = questionDiv.querySelectorAll('.judge-item');
        judgeItems.forEach((item) => {
            const radio = item.querySelector('.option-checkbox');
            const answer = item.getAttribute('data-value');

            item.addEventListener('click', function(e) {
                if (e.target.closest('.judge-item')) {
                    // 移除所有正确答案标识
                    judgeItems.forEach(judgeItem => {
                        judgeItem.querySelector('.option-checkbox').checked = false;
                        judgeItem.classList.remove('correct');
                        const correctSpan = judgeItem.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    });

                    // 设置当前为正确答案
                    radio.checked = true;
                    item.classList.add('correct');
                    if (!item.querySelector('.correct-answer')) {
                        const correctSpan = document.createElement('span');
                        correctSpan.className = 'correct-answer';
                        correctSpan.textContent = '正确答案';
                        item.querySelector('.option-content').appendChild(correctSpan);
                    }

                    // 更新答案值
                    document.getElementById(`answer-${questionIndex}`).value = answer;
                }
            });
        });
    }
}
