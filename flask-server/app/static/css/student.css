/* 基础样式 */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* 顶部导航栏样式 */
.top-navbar {
    background-color: #1a73e8;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.logo {
    position: absolute;
    left: 20px;
    font-weight: bold;
    font-size: 18px;
}

.nav-title {
    font-size: 24px;
    font-weight: 500;
}

.nav-actions {
    position: absolute;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-actions input {
    width: 150px;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
}

/* 主布局样式 */
.main-layout {
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
    margin-top: 120px;
}

.exam-container {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.exam-selector {
    margin-bottom: 30px;
}

.exam-content {
    display: flex;
    gap: 30px;
}

#examQuestions {
    flex: 1;
    padding: 20px;
    border-radius: 8px;
}

.exam-navigation {
    width: 250px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.question-type-section, .question-number-section {
    margin-bottom: 20px;
}

.question-type-section h3, .question-number-section h3 {
    margin-top: 0;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ddd;
    font-size: 16px;
}

/* 题目样式 */
.question {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.question:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

/* 表单元素样式 */
input,
textarea,
select {
    width: 100%;
    padding: 8px;
    margin: 5px 0;
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    background-color: #1a73e8;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    opacity: 0.9;
}

/* 题型和题号导航 */
.type-row, .question-row {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.type-btn, .question-btn {
    padding: 8px 12px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    font-size: 14px;
    flex: 0 0 auto;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.question-btn {
    min-width: 36px;
    padding: 6px;
}

.type-btn:hover, .question-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

/* 当前选中按钮样式 */
.type-btn.active, .question-btn.active {
    background-color: #0d5bb7;
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 答题界面优化样式 */
.exam-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.exam-header h2 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 500;
}

.exam-header p {
    margin: 0;
    opacity: 0.9;
}

.exam-status {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
}

.timer-container {
    text-align: center;
    padding: 15px;
}

.timer-display {
    font-size: 28px !important;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px !important;
    font-family: 'Courier New', monospace;
}

.progress-container {
    margin-top: 10px;
}

/* 题目卡片样式 */
.layui-card {
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.layui-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.layui-card-header {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
    color: #333;
}

/* 选项样式优化 */
.layui-form-item {
    margin-bottom: 12px;
}

.layui-form-item input[type="radio"] + div,
.layui-form-item input[type="checkbox"] + div {
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.layui-form-item input[type="radio"]:checked + div,
.layui-form-item input[type="checkbox"]:checked + div {
    background-color: #e6f7ff;
    border-color: #1890ff;
}

/* 导航按钮优化 */
.layui-btn-container .layui-btn {
    margin: 3px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.layui-btn-container .layui-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 提交按钮样式 */
#submitBtn {
    background: linear-gradient(45deg, #52c41a, #73d13d);
    border: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

#submitBtn:hover {
    background-color: #389e0d;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

/* 选项样式 */
.options {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.options label {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    width: 100%;
}

.options label input[type="radio"],
.options label input[type="checkbox"] {
    margin-right: 8px;
    width: auto;
}


/* 倒计时样式 */
#countdown-timer {
    text-align: center;
    margin-top: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

#timer-display {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    font-family: monospace;
}

#timer-status {
    font-size: 14px;
    padding: 3px 8px;
    border-radius: 10px;
    display: inline-block;
}

/* 不同状态的样式 */
.timer-active {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.timer-warning {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
    animation: blink 1s infinite;
}

.timer-ended {
    background-color: #fff1f0;
    color: #f5222d;
    border: 1px solid #ffa39e;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}