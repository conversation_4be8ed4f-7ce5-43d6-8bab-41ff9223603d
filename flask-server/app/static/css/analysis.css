body {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
}

/* 保留基础样式 */
.question-type {
    background-color: #1e9fff;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    display: inline-block;
    min-width: 40px;
    text-align: center;
}

.progress-bar {
    background-color: #f8f8f8;
    border-radius: 4px;
    height: 8px;
    margin-top: 5px;
    overflow: hidden;
}

.progress {
    background-color: #1e9fff;
    height: 100%;
}

.no-data {
    text-align: center;
    color: #6c757d;
    padding: 50px 0;
    font-style: italic;
}

.correct-answer {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #f5fffa;
    border-left: 4px solid #1e9fff;
    border-radius: 4px;
    color: #1b5e20;
    font-size: 0.95rem;
}

.correct-answer strong {
    color: #1e9fff;
}

/* 成绩徽章样式 */
.score-badge {
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.score-excellent {
    background: linear-gradient(135deg, #FF6B6B, #FF8E53);
}

.score-good {
    background: linear-gradient(135deg, #4ECDC4, #44A08D);
}

.score-pass {
    background: linear-gradient(135deg, #45B7D1, #96C93D);
}

.score-fail {
    background: linear-gradient(135deg, #FD79A8, #FDCB6E);
}

/* 统计指标样式 */
.layui-stat {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.layui-stat-title {
    font-size: 14px;
    margin-bottom: 10px;
    opacity: 0.9;
}

.layui-stat-number {
    font-size: 24px;
    font-weight: bold;
}

/* 错题分析样式 */
.wrong-questions-analysis {
    max-height: 600px;
    overflow-y: auto;
}

.wrong-question-item {
    background: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.wrong-question-item:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.question-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.question-type {
    background: #1e9fff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.error-rate {
    color: #ff5722;
    font-weight: bold;
    margin-left: auto;
}

.question-content {
    margin-bottom: 15px;
    line-height: 1.6;
}

.question-content p {
    margin: 8px 0;
}

.question-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

.stat-value {
    color: #333;
    font-weight: bold;
}

.improvement-suggestion {
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #e17055;
}

.suggestion-text {
    color: #2d3436;
    font-size: 14px;
    line-height: 1.5;
}

.no-wrong-questions {
    background: #00b894;
    border-radius: 12px;
    color: white;
}
